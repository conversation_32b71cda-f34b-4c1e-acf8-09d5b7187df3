-- Temperature Monitoring Enhancement for Food & Pharma Industries
-- This migration adds temperature tracking capabilities to the existing delivery tracking system

-- 1. Add temperature fields to existing TrackingUpdates table
ALTER TABLE TrackingUpdates ADD COLUMN IF NOT EXISTS temperature_c FLOAT;
ALTER TABLE TrackingUpdates ADD COLUMN IF NOT EXISTS humidity_pct FLOAT CHECK (humidity_pct >= 0 AND humidity_pct <= 100);

-- 2. Add temperature fields to LiveLocations table
ALTER TABLE LiveLocations ADD COLUMN IF NOT EXISTS temperature_c FLOAT;
ALTER TABLE LiveLocations ADD COLUMN IF NOT EXISTS humidity_pct FLOAT CHECK (humidity_pct >= 0 AND humidity_pct <= 100);

-- 3. Add temperature requirements to Trips table for cold chain deliveries
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS temperature_requirements JSONB;
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS is_temperature_sensitive BOOLEAN DEFAULT FALSE;

-- 4. Create temperature alert types enum first
DO $$ BEGIN
    CREATE TYPE temperature_alert_enum AS ENUM (
        'temperature_high',
        'temperature_low',
        'temperature_breach',
        'humidity_high',
        'humidity_low',
        'sensor_malfunction'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 5. Create severity enum for alerts
DO $$ BEGIN
    CREATE TYPE severity_enum AS ENUM (
        'low',
        'medium',
        'high',
        'critical'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 6. Create TemperatureAlerts table for monitoring temperature breaches
CREATE TABLE IF NOT EXISTS TemperatureAlerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL,
    agent_id UUID NOT NULL,
    alert_type temperature_alert_enum NOT NULL,
    temperature_c FLOAT NOT NULL,
    humidity_pct FLOAT,
    threshold_min FLOAT,
    threshold_max FLOAT,
    severity severity_enum NOT NULL DEFAULT 'medium',
    location GEOGRAPHY(POINT, 4326),
    resolution_note TEXT,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE
);

-- 7. Create TemperatureReadings table for detailed sensor data
CREATE TABLE IF NOT EXISTS TemperatureReadings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL,
    agent_id UUID NOT NULL,
    sensor_id VARCHAR(50),
    temperature_c FLOAT NOT NULL,
    humidity_pct FLOAT,
    battery_level FLOAT,
    signal_strength INTEGER,
    location GEOGRAPHY(POINT, 4326),
    recorded_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE
);

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_trip_id ON TemperatureAlerts(trip_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_agent_id ON TemperatureAlerts(agent_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_created_at ON TemperatureAlerts(created_at);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_severity ON TemperatureAlerts(severity);

CREATE INDEX IF NOT EXISTS idx_temperature_readings_trip_id ON TemperatureReadings(trip_id);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_agent_id ON TemperatureReadings(agent_id);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_recorded_at ON TemperatureReadings(recorded_at);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_sensor_id ON TemperatureReadings(sensor_id);

CREATE INDEX IF NOT EXISTS idx_tracking_updates_temperature ON TrackingUpdates(temperature_c) WHERE temperature_c IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_trips_temperature_sensitive ON Trips(is_temperature_sensitive) WHERE is_temperature_sensitive = TRUE;

-- 9. Create function to automatically check temperature thresholds
CREATE OR REPLACE FUNCTION check_temperature_thresholds()
RETURNS TRIGGER AS $$
DECLARE
    trip_temp_req JSONB;
    min_temp FLOAT;
    max_temp FLOAT;
    alert_severity severity_enum;
BEGIN
    -- Only check if this is a temperature-sensitive trip
    IF NEW.trip_id IS NOT NULL THEN
        -- Get temperature requirements for the trip
        SELECT temperature_requirements, is_temperature_sensitive 
        INTO trip_temp_req
        FROM Trips 
        WHERE trip_id = NEW.trip_id AND is_temperature_sensitive = TRUE;
        
        -- If trip has temperature requirements and current reading has temperature
        IF trip_temp_req IS NOT NULL AND NEW.temperature_c IS NOT NULL THEN
            min_temp := (trip_temp_req->>'min_temperature')::FLOAT;
            max_temp := (trip_temp_req->>'max_temperature')::FLOAT;
            
            -- Check for temperature breaches
            IF min_temp IS NOT NULL AND NEW.temperature_c < min_temp THEN
                -- Determine severity based on how far outside the range
                IF NEW.temperature_c < min_temp - 5 THEN
                    alert_severity := 'critical';
                ELSIF NEW.temperature_c < min_temp - 2 THEN
                    alert_severity := 'high';
                ELSE
                    alert_severity := 'medium';
                END IF;
                
                -- Insert temperature alert
                INSERT INTO TemperatureAlerts (
                    trip_id, agent_id, alert_type, temperature_c, humidity_pct,
                    threshold_min, threshold_max, severity, location
                ) VALUES (
                    NEW.trip_id, NEW.agent_id, 'temperature_low', NEW.temperature_c, NEW.humidity_pct,
                    min_temp, max_temp, alert_severity, NEW.geom
                );
                
            ELSIF max_temp IS NOT NULL AND NEW.temperature_c > max_temp THEN
                -- Determine severity based on how far outside the range
                IF NEW.temperature_c > max_temp + 5 THEN
                    alert_severity := 'critical';
                ELSIF NEW.temperature_c > max_temp + 2 THEN
                    alert_severity := 'high';
                ELSE
                    alert_severity := 'medium';
                END IF;
                
                -- Insert temperature alert
                INSERT INTO TemperatureAlerts (
                    trip_id, agent_id, alert_type, temperature_c, humidity_pct,
                    threshold_min, threshold_max, severity, location
                ) VALUES (
                    NEW.trip_id, NEW.agent_id, 'temperature_high', NEW.temperature_c, NEW.humidity_pct,
                    min_temp, max_temp, alert_severity, NEW.geom
                );
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Create trigger to automatically check temperature on new tracking updates
DROP TRIGGER IF EXISTS trigger_check_temperature_thresholds ON TrackingUpdates;
CREATE TRIGGER trigger_check_temperature_thresholds
    AFTER INSERT ON TrackingUpdates
    FOR EACH ROW
    EXECUTE FUNCTION check_temperature_thresholds();

-- 11. Update the existing live location trigger to include temperature data
CREATE OR REPLACE FUNCTION update_live_location_from_tracking()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert or update the LiveLocations table with the newest tracking data
    INSERT INTO LiveLocations (
        agent_id, role, trip_id, geom, timestamp, 
        speed_m_s, heading_deg, accuracy_m, altitude_m, 
        battery_pct, temperature_c, humidity_pct, source, updated_at
    )
    VALUES (
        NEW.agent_id, NEW.role, NEW.trip_id, 
        NEW.geom, NEW.timestamp, 
        COALESCE(NEW.speed_m_s, 0), 
        COALESCE(NEW.heading_deg, 0), 
        NEW.accuracy_m, NEW.altitude_m, 
        NEW.battery_pct, NEW.temperature_c, NEW.humidity_pct,
        NEW.source, CURRENT_TIMESTAMP
    )
    ON CONFLICT (agent_id)
    DO UPDATE SET
        role = EXCLUDED.role,
        trip_id = EXCLUDED.trip_id,
        geom = EXCLUDED.geom,
        timestamp = EXCLUDED.timestamp,
        speed_m_s = EXCLUDED.speed_m_s,
        heading_deg = EXCLUDED.heading_deg,
        accuracy_m = EXCLUDED.accuracy_m,
        altitude_m = EXCLUDED.altitude_m,
        battery_pct = EXCLUDED.battery_pct,
        temperature_c = EXCLUDED.temperature_c,
        humidity_pct = EXCLUDED.humidity_pct,
        source = EXCLUDED.source,
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        -- Only update if the new record is more recent
        EXCLUDED.timestamp > LiveLocations.timestamp;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Add sample temperature requirements for testing
-- Example: Frozen food delivery (-18°C to -15°C)
-- Example: Pharmaceutical delivery (2°C to 8°C)
-- Example: Fresh food delivery (0°C to 4°C)

-- 13. Add comments for documentation
COMMENT ON TABLE TemperatureAlerts IS 'Temperature breach alerts for cold chain monitoring';
COMMENT ON TABLE TemperatureReadings IS 'Detailed temperature sensor readings for compliance';
COMMENT ON COLUMN Trips.temperature_requirements IS 'JSON object with min_temperature, max_temperature, and other cold chain requirements';
COMMENT ON COLUMN Trips.is_temperature_sensitive IS 'Flag to indicate if this trip requires temperature monitoring';
COMMENT ON COLUMN TrackingUpdates.temperature_c IS 'Temperature reading in Celsius from GPS device or sensor';
COMMENT ON COLUMN TrackingUpdates.humidity_pct IS 'Humidity percentage from sensor (0-100)';

-- 14. Create view for temperature monitoring dashboard
CREATE OR REPLACE VIEW temperature_monitoring_dashboard AS
SELECT 
    t.trip_id,
    t.customer_id,
    t.agent_id,
    u.name as agent_name,
    t.status as trip_status,
    t.temperature_requirements,
    t.is_temperature_sensitive,
    ll.temperature_c as current_temperature,
    ll.humidity_pct as current_humidity,
    ll.timestamp as last_reading_time,
    COUNT(ta.id) as total_alerts,
    COUNT(CASE WHEN ta.severity = 'critical' THEN 1 END) as critical_alerts,
    COUNT(CASE WHEN ta.severity = 'high' THEN 1 END) as high_alerts,
    COUNT(CASE WHEN ta.resolved_at IS NULL THEN 1 END) as unresolved_alerts
FROM Trips t
LEFT JOIN Users u ON t.agent_id = u.id
LEFT JOIN LiveLocations ll ON t.agent_id = ll.agent_id
LEFT JOIN TemperatureAlerts ta ON t.trip_id = ta.trip_id
WHERE t.is_temperature_sensitive = TRUE
GROUP BY t.trip_id, t.customer_id, t.agent_id, u.name, t.status, 
         t.temperature_requirements, t.is_temperature_sensitive,
         ll.temperature_c, ll.humidity_pct, ll.timestamp;

COMMENT ON VIEW temperature_monitoring_dashboard IS 'Dashboard view for monitoring temperature-sensitive deliveries';

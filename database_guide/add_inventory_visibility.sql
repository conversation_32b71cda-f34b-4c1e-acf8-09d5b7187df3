-- Inventory Visibility Enhancement for Manufacturing Industry
-- This migration adds inventory tracking and supply chain visibility capabilities

-- 1. Create inventory status enum
DO $$ BEGIN
    CREATE TYPE inventory_status_enum AS ENUM (
        'in_stock',
        'low_stock',
        'out_of_stock',
        'on_order',
        'in_transit',
        'reserved',
        'damaged',
        'expired'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Create inventory alert types enum
DO $$ BEGIN
    CREATE TYPE inventory_alert_enum AS ENUM (
        'low_stock',
        'out_of_stock',
        'reorder_point',
        'overstock',
        'expiry_warning',
        'delivery_delay'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 3. Create Warehouses table for inventory locations
CREATE TABLE IF NOT EXISTS Warehouses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    location GEOGRAPHY(POINT, 4326),
    manager_id UUID,
    capacity_cubic_meters FLOAT,
    is_active BOOLEAN DEFAULT TRUE,
    operating_hours JSONB,
    contact_info JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (manager_id) REFERENCES Users(id) ON DELETE SET NULL
);

-- 4. Create Products table for inventory items
CREATE TABLE IF NOT EXISTS Products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sku VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    unit_of_measure VARCHAR(20) DEFAULT 'pieces',
    unit_price DECIMAL(10,2),
    weight_kg FLOAT,
    dimensions JSONB, -- {"length": 10, "width": 5, "height": 3}
    supplier_info JSONB,
    shelf_life_days INTEGER,
    storage_requirements JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. Create Inventory table for stock levels
CREATE TABLE IF NOT EXISTS Inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    warehouse_id UUID NOT NULL,
    product_id UUID NOT NULL,
    current_stock INTEGER NOT NULL DEFAULT 0,
    reserved_stock INTEGER NOT NULL DEFAULT 0,
    available_stock INTEGER GENERATED ALWAYS AS (current_stock - reserved_stock) STORED,
    reorder_point INTEGER NOT NULL DEFAULT 10,
    max_stock_level INTEGER,
    unit_cost DECIMAL(10,2),
    last_restocked_at TIMESTAMP,
    expiry_date DATE,
    batch_number VARCHAR(100),
    status inventory_status_enum NOT NULL DEFAULT 'in_stock',
    location_in_warehouse VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES Warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES Products(id) ON DELETE CASCADE,
    UNIQUE(warehouse_id, product_id, batch_number)
);

-- 6. Create InventoryMovements table for tracking stock changes
CREATE TABLE IF NOT EXISTS InventoryMovements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_id UUID NOT NULL,
    movement_type VARCHAR(50) NOT NULL, -- 'inbound', 'outbound', 'transfer', 'adjustment'
    quantity INTEGER NOT NULL,
    reference_type VARCHAR(50), -- 'trip', 'purchase_order', 'adjustment', 'transfer'
    reference_id UUID,
    reason TEXT,
    performed_by UUID,
    cost_per_unit DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_id) REFERENCES Inventory(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES Users(id) ON DELETE SET NULL
);

-- 7. Create InventoryAlerts table for stock alerts
CREATE TABLE IF NOT EXISTS InventoryAlerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_id UUID NOT NULL,
    alert_type inventory_alert_enum NOT NULL,
    severity severity_enum NOT NULL DEFAULT 'medium',
    current_stock INTEGER,
    threshold_value INTEGER,
    message TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID,
    resolved_at TIMESTAMP,
    resolution_note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_id) REFERENCES Inventory(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES Users(id) ON DELETE SET NULL
);

-- 8. Add inventory fields to Trips table for delivery tracking
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS inventory_items JSONB; -- Array of {product_id, quantity, warehouse_id}
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS affects_inventory BOOLEAN DEFAULT FALSE;
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS inventory_reserved BOOLEAN DEFAULT FALSE;

-- 9. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_warehouses_location ON Warehouses USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_warehouses_active ON Warehouses(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_warehouses_code ON Warehouses(code);

CREATE INDEX IF NOT EXISTS idx_products_sku ON Products(sku);
CREATE INDEX IF NOT EXISTS idx_products_category ON Products(category);
CREATE INDEX IF NOT EXISTS idx_products_active ON Products(is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_product ON Inventory(warehouse_id, product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_status ON Inventory(status);
CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON Inventory(warehouse_id, product_id) WHERE available_stock <= reorder_point;
CREATE INDEX IF NOT EXISTS idx_inventory_expiry ON Inventory(expiry_date) WHERE expiry_date IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_inventory_movements_inventory_id ON InventoryMovements(inventory_id);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_reference ON InventoryMovements(reference_type, reference_id);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_created_at ON InventoryMovements(created_at);

CREATE INDEX IF NOT EXISTS idx_inventory_alerts_inventory_id ON InventoryAlerts(inventory_id);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_type ON InventoryAlerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_unresolved ON InventoryAlerts(resolved) WHERE resolved = FALSE;

CREATE INDEX IF NOT EXISTS idx_trips_inventory ON Trips(affects_inventory) WHERE affects_inventory = TRUE;

-- 10. Create function to check inventory levels and generate alerts
CREATE OR REPLACE FUNCTION check_inventory_levels()
RETURNS TRIGGER AS $$
DECLARE
    alert_severity severity_enum;
BEGIN
    -- Check for low stock alerts
    IF NEW.available_stock <= NEW.reorder_point AND NEW.available_stock > 0 THEN
        -- Determine severity based on how low the stock is
        IF NEW.available_stock <= NEW.reorder_point * 0.5 THEN
            alert_severity := 'high';
        ELSE
            alert_severity := 'medium';
        END IF;
        
        -- Insert low stock alert if not already exists
        INSERT INTO InventoryAlerts (
            inventory_id, alert_type, severity, current_stock, threshold_value, message
        ) 
        SELECT NEW.id, 'low_stock', alert_severity, NEW.available_stock, NEW.reorder_point,
               'Stock level is below reorder point for ' || p.name || ' at ' || w.name
        FROM Products p, Warehouses w
        WHERE p.id = NEW.product_id AND w.id = NEW.warehouse_id
        AND NOT EXISTS (
            SELECT 1 FROM InventoryAlerts 
            WHERE inventory_id = NEW.id AND alert_type = 'low_stock' AND resolved = FALSE
        );
        
        -- Update inventory status
        NEW.status := 'low_stock';
        
    ELSIF NEW.available_stock <= 0 THEN
        -- Insert out of stock alert
        INSERT INTO InventoryAlerts (
            inventory_id, alert_type, severity, current_stock, threshold_value, message
        )
        SELECT NEW.id, 'out_of_stock', 'critical', NEW.available_stock, NEW.reorder_point,
               'Out of stock for ' || p.name || ' at ' || w.name
        FROM Products p, Warehouses w
        WHERE p.id = NEW.product_id AND w.id = NEW.warehouse_id
        AND NOT EXISTS (
            SELECT 1 FROM InventoryAlerts 
            WHERE inventory_id = NEW.id AND alert_type = 'out_of_stock' AND resolved = FALSE
        );
        
        -- Update inventory status
        NEW.status := 'out_of_stock';
        
    ELSE
        -- Stock is adequate, update status
        NEW.status := 'in_stock';
        
        -- Resolve any existing low stock or out of stock alerts
        UPDATE InventoryAlerts 
        SET resolved = TRUE, resolved_at = CURRENT_TIMESTAMP
        WHERE inventory_id = NEW.id 
          AND alert_type IN ('low_stock', 'out_of_stock') 
          AND resolved = FALSE;
    END IF;
    
    -- Check for expiry warnings (30 days before expiry)
    IF NEW.expiry_date IS NOT NULL AND NEW.expiry_date <= CURRENT_DATE + INTERVAL '30 days' THEN
        INSERT INTO InventoryAlerts (
            inventory_id, alert_type, severity, current_stock, message
        )
        SELECT NEW.id, 'expiry_warning', 'medium', NEW.available_stock,
               'Product ' || p.name || ' expires on ' || NEW.expiry_date || ' at ' || w.name
        FROM Products p, Warehouses w
        WHERE p.id = NEW.product_id AND w.id = NEW.warehouse_id
        AND NOT EXISTS (
            SELECT 1 FROM InventoryAlerts 
            WHERE inventory_id = NEW.id AND alert_type = 'expiry_warning' AND resolved = FALSE
        );
    END IF;
    
    NEW.updated_at := CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 11. Create trigger for inventory level checking
DROP TRIGGER IF EXISTS trigger_check_inventory_levels ON Inventory;
CREATE TRIGGER trigger_check_inventory_levels
    BEFORE UPDATE ON Inventory
    FOR EACH ROW
    EXECUTE FUNCTION check_inventory_levels();

-- 12. Create function to handle inventory movements
CREATE OR REPLACE FUNCTION process_inventory_movement()
RETURNS TRIGGER AS $$
BEGIN
    -- Update inventory stock based on movement type
    IF NEW.movement_type = 'inbound' THEN
        UPDATE Inventory 
        SET current_stock = current_stock + NEW.quantity,
            last_restocked_at = CURRENT_TIMESTAMP
        WHERE id = NEW.inventory_id;
        
    ELSIF NEW.movement_type = 'outbound' THEN
        UPDATE Inventory 
        SET current_stock = current_stock - NEW.quantity
        WHERE id = NEW.inventory_id;
        
    ELSIF NEW.movement_type = 'adjustment' THEN
        UPDATE Inventory 
        SET current_stock = current_stock + NEW.quantity
        WHERE id = NEW.inventory_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 13. Create trigger for inventory movements
DROP TRIGGER IF EXISTS trigger_process_inventory_movement ON InventoryMovements;
CREATE TRIGGER trigger_process_inventory_movement
    AFTER INSERT ON InventoryMovements
    FOR EACH ROW
    EXECUTE FUNCTION process_inventory_movement();

-- 14. Insert sample data for testing
-- Sample warehouses
INSERT INTO Warehouses (name, code, address, location, capacity_cubic_meters, operating_hours) VALUES 
(
    'Delhi Main Warehouse',
    'DLH001',
    'Sector 18, Gurgaon, Haryana',
    ST_SetSRID(ST_MakePoint(77.0266, 28.4595), 4326),
    5000.0,
    '{"monday": {"open": "08:00", "close": "18:00"}, "tuesday": {"open": "08:00", "close": "18:00"}}'
),
(
    'Mumbai Distribution Center',
    'MUM001', 
    'Andheri East, Mumbai, Maharashtra',
    ST_SetSRID(ST_MakePoint(72.8777, 19.0760), 4326),
    3000.0,
    '{"monday": {"open": "09:00", "close": "17:00"}, "tuesday": {"open": "09:00", "close": "17:00"}}'
);

-- Sample products
INSERT INTO Products (sku, name, description, category, unit_of_measure, unit_price, weight_kg, shelf_life_days) VALUES 
('ELEC001', 'Smartphone Battery', 'Lithium-ion battery for smartphones', 'Electronics', 'pieces', 25.00, 0.2, 730),
('FOOD001', 'Organic Rice', 'Premium organic basmati rice', 'Food', 'kg', 5.50, 1.0, 365),
('MED001', 'Paracetamol Tablets', 'Pain relief medication', 'Medicine', 'boxes', 12.00, 0.1, 1095),
('AUTO001', 'Car Engine Oil', 'Synthetic motor oil 5W-30', 'Automotive', 'liters', 8.75, 1.2, 1460);

-- Sample inventory
INSERT INTO Inventory (warehouse_id, product_id, current_stock, reorder_point, max_stock_level, unit_cost, batch_number, expiry_date) VALUES 
(
    (SELECT id FROM Warehouses WHERE code = 'DLH001'),
    (SELECT id FROM Products WHERE sku = 'ELEC001'),
    150, 20, 500, 22.50, 'BATCH001', '2025-12-31'
),
(
    (SELECT id FROM Warehouses WHERE code = 'DLH001'),
    (SELECT id FROM Products WHERE sku = 'FOOD001'),
    8, 50, 1000, 5.00, 'BATCH002', '2025-08-15' -- Low stock to trigger alert
),
(
    (SELECT id FROM Warehouses WHERE code = 'MUM001'),
    (SELECT id FROM Products WHERE sku = 'MED001'),
    300, 25, 800, 10.50, 'BATCH003', '2027-06-30'
),
(
    (SELECT id FROM Warehouses WHERE code = 'MUM001'),
    (SELECT id FROM Products WHERE sku = 'AUTO001'),
    0, 15, 200, 8.00, 'BATCH004', '2026-03-15' -- Out of stock to trigger alert
);

-- 15. Create view for inventory dashboard
CREATE OR REPLACE VIEW inventory_dashboard AS
SELECT 
    i.id as inventory_id,
    w.name as warehouse_name,
    w.code as warehouse_code,
    p.sku,
    p.name as product_name,
    p.category,
    i.current_stock,
    i.reserved_stock,
    i.available_stock,
    i.reorder_point,
    i.status,
    i.expiry_date,
    COUNT(ia.id) as total_alerts,
    COUNT(CASE WHEN ia.alert_type = 'low_stock' AND ia.resolved = FALSE THEN 1 END) as low_stock_alerts,
    COUNT(CASE WHEN ia.alert_type = 'out_of_stock' AND ia.resolved = FALSE THEN 1 END) as out_of_stock_alerts,
    COUNT(CASE WHEN ia.alert_type = 'expiry_warning' AND ia.resolved = FALSE THEN 1 END) as expiry_alerts,
    i.last_restocked_at,
    i.updated_at
FROM Inventory i
JOIN Warehouses w ON i.warehouse_id = w.id
JOIN Products p ON i.product_id = p.id
LEFT JOIN InventoryAlerts ia ON i.id = ia.inventory_id
WHERE w.is_active = TRUE AND p.is_active = TRUE
GROUP BY i.id, w.name, w.code, p.sku, p.name, p.category, 
         i.current_stock, i.reserved_stock, i.available_stock, 
         i.reorder_point, i.status, i.expiry_date, 
         i.last_restocked_at, i.updated_at;

-- 16. Add comments for documentation
COMMENT ON TABLE Warehouses IS 'Warehouse locations for inventory management';
COMMENT ON TABLE Products IS 'Product catalog for inventory tracking';
COMMENT ON TABLE Inventory IS 'Current stock levels and inventory status';
COMMENT ON TABLE InventoryMovements IS 'Historical record of all inventory movements';
COMMENT ON TABLE InventoryAlerts IS 'Inventory alerts and notifications';
COMMENT ON VIEW inventory_dashboard IS 'Dashboard view for inventory monitoring';

-- 17. Display summary of created data
SELECT 'Warehouses Created' as summary, COUNT(*) as count FROM Warehouses;
SELECT 'Products Created' as summary, COUNT(*) as count FROM Products;
SELECT 'Inventory Items Created' as summary, COUNT(*) as count FROM Inventory;
SELECT 'Inventory Alerts Generated' as summary, COUNT(*) as count FROM InventoryAlerts;

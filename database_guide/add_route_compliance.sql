-- Route Compliance Enhancement for Logistics Industry
-- This migration adds geofencing and route compliance tracking capabilities

-- 1. Create geofence types enum
DO $$ BEGIN
    CREATE TYPE geofence_type_enum AS ENUM (
        'delivery_zone',
        'restricted_area',
        'speed_limit_zone',
        'customer_area',
        'warehouse_zone',
        'no_go_zone'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Create compliance event types enum
DO $$ BEGIN
    CREATE TYPE compliance_event_enum AS ENUM (
        'route_deviation',
        'geofence_entry',
        'geofence_exit',
        'speed_violation',
        'unauthorized_stop',
        'delayed_delivery',
        'early_delivery'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 3. Create Geofences table for defining geographic boundaries
CREATE TABLE IF NOT EXISTS Geofences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    geofence_type geofence_type_enum NOT NULL,
    boundary GEOGRAPHY(POLYGON, 4326) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    speed_limit_kmh INTEGER,
    allowed_hours JSONB, -- {"start": "09:00", "end": "17:00", "days": ["monday", "tuesday"]}
    restrictions JSONB, -- Additional restrictions and rules
    created_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES Users(id) ON DELETE SET NULL
);

-- 4. Create RouteCompliance table for tracking compliance events
CREATE TABLE IF NOT EXISTS RouteCompliance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL,
    agent_id UUID NOT NULL,
    event_type compliance_event_enum NOT NULL,
    geofence_id UUID,
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    speed_kmh FLOAT,
    expected_location GEOGRAPHY(POINT, 4326),
    deviation_distance_m FLOAT,
    severity severity_enum NOT NULL DEFAULT 'medium',
    description TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID,
    resolved_at TIMESTAMP,
    resolution_note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (geofence_id) REFERENCES Geofences(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES Users(id) ON DELETE SET NULL
);

-- 5. Create PlannedRoutes table for storing expected routes
CREATE TABLE IF NOT EXISTS PlannedRoutes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL UNIQUE,
    route_points GEOGRAPHY(LINESTRING, 4326) NOT NULL,
    waypoints JSONB, -- Array of waypoint coordinates and metadata
    estimated_duration_minutes INTEGER,
    estimated_distance_km FLOAT,
    max_deviation_meters INTEGER DEFAULT 500,
    speed_limits JSONB, -- Speed limits for different segments
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE
);

-- 6. Add route compliance fields to Trips table
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS requires_compliance BOOLEAN DEFAULT FALSE;
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS compliance_score FLOAT CHECK (compliance_score >= 0 AND compliance_score <= 100);
ALTER TABLE Trips ADD COLUMN IF NOT EXISTS route_deviation_count INTEGER DEFAULT 0;

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_geofences_boundary ON Geofences USING GIST(boundary);
CREATE INDEX IF NOT EXISTS idx_geofences_type ON Geofences(geofence_type);
CREATE INDEX IF NOT EXISTS idx_geofences_active ON Geofences(is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_route_compliance_trip_id ON RouteCompliance(trip_id);
CREATE INDEX IF NOT EXISTS idx_route_compliance_agent_id ON RouteCompliance(agent_id);
CREATE INDEX IF NOT EXISTS idx_route_compliance_event_type ON RouteCompliance(event_type);
CREATE INDEX IF NOT EXISTS idx_route_compliance_created_at ON RouteCompliance(created_at);
CREATE INDEX IF NOT EXISTS idx_route_compliance_location ON RouteCompliance USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_route_compliance_unresolved ON RouteCompliance(resolved) WHERE resolved = FALSE;

CREATE INDEX IF NOT EXISTS idx_planned_routes_trip_id ON PlannedRoutes(trip_id);
CREATE INDEX IF NOT EXISTS idx_planned_routes_route_points ON PlannedRoutes USING GIST(route_points);

CREATE INDEX IF NOT EXISTS idx_trips_compliance ON Trips(requires_compliance) WHERE requires_compliance = TRUE;

-- 8. Create function to check geofence violations
CREATE OR REPLACE FUNCTION check_geofence_compliance(agent_location GEOGRAPHY, agent_id_param UUID, trip_id_param UUID)
RETURNS VOID AS $$
DECLARE
    geofence_record RECORD;
    current_hour INTEGER;
    current_day TEXT;
    allowed_hours JSONB;
BEGIN
    current_hour := EXTRACT(HOUR FROM CURRENT_TIME);
    current_day := LOWER(TO_CHAR(CURRENT_DATE, 'Day'));
    current_day := TRIM(current_day);

    -- Check all active geofences
    FOR geofence_record IN 
        SELECT id, name, geofence_type, boundary, speed_limit_kmh, allowed_hours, restrictions
        FROM Geofences 
        WHERE is_active = TRUE
    LOOP
        -- Check if agent is within this geofence
        IF ST_Within(agent_location, geofence_record.boundary) THEN
            -- Check for restricted areas
            IF geofence_record.geofence_type = 'restricted_area' OR geofence_record.geofence_type = 'no_go_zone' THEN
                INSERT INTO RouteCompliance (
                    trip_id, agent_id, event_type, geofence_id, location, severity, description
                ) VALUES (
                    trip_id_param, agent_id_param, 'geofence_entry', geofence_record.id, 
                    agent_location, 'high', 
                    'Agent entered restricted area: ' || geofence_record.name
                );
            END IF;

            -- Check time-based restrictions
            IF geofence_record.allowed_hours IS NOT NULL THEN
                allowed_hours := geofence_record.allowed_hours;
                -- Simple time check (can be enhanced for complex rules)
                IF NOT (allowed_hours ? 'days' AND allowed_hours->'days' @> to_jsonb(current_day)) THEN
                    INSERT INTO RouteCompliance (
                        trip_id, agent_id, event_type, geofence_id, location, severity, description
                    ) VALUES (
                        trip_id_param, agent_id_param, 'geofence_entry', geofence_record.id,
                        agent_location, 'medium',
                        'Agent in restricted time zone: ' || geofence_record.name
                    );
                END IF;
            END IF;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 9. Create function to check route deviation
CREATE OR REPLACE FUNCTION check_route_deviation()
RETURNS TRIGGER AS $$
DECLARE
    planned_route RECORD;
    deviation_distance FLOAT;
    max_deviation INTEGER;
BEGIN
    -- Only check for trips that require compliance
    IF NEW.trip_id IS NOT NULL THEN
        -- Get planned route for this trip
        SELECT pr.route_points, pr.max_deviation_meters
        INTO planned_route
        FROM PlannedRoutes pr
        JOIN Trips t ON pr.trip_id = t.trip_id
        WHERE pr.trip_id = NEW.trip_id AND t.requires_compliance = TRUE;

        IF FOUND THEN
            -- Calculate distance from planned route
            deviation_distance := ST_Distance(NEW.geom, planned_route.route_points);
            max_deviation := COALESCE(planned_route.max_deviation_meters, 500);

            -- Check if deviation exceeds threshold
            IF deviation_distance > max_deviation THEN
                -- Insert compliance violation
                INSERT INTO RouteCompliance (
                    trip_id, agent_id, event_type, location, 
                    deviation_distance_m, severity, description
                ) VALUES (
                    NEW.trip_id, NEW.agent_id, 'route_deviation', NEW.geom,
                    deviation_distance,
                    CASE 
                        WHEN deviation_distance > max_deviation * 3 THEN 'critical'
                        WHEN deviation_distance > max_deviation * 2 THEN 'high'
                        ELSE 'medium'
                    END,
                    'Route deviation of ' || ROUND(deviation_distance) || 'm from planned route'
                );

                -- Update trip deviation count
                UPDATE Trips 
                SET route_deviation_count = route_deviation_count + 1
                WHERE trip_id = NEW.trip_id;
            END IF;

            -- Check geofence compliance
            PERFORM check_geofence_compliance(NEW.geom, NEW.agent_id, NEW.trip_id);
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Create trigger for route compliance checking
DROP TRIGGER IF EXISTS trigger_check_route_compliance ON TrackingUpdates;
CREATE TRIGGER trigger_check_route_compliance
    AFTER INSERT ON TrackingUpdates
    FOR EACH ROW
    EXECUTE FUNCTION check_route_deviation();

-- 11. Create sample geofences for testing
INSERT INTO Geofences (name, description, geofence_type, boundary, speed_limit_kmh, allowed_hours) VALUES 
-- Delhi delivery zone
(
    'Delhi Central Delivery Zone',
    'Main delivery area in Central Delhi',
    'delivery_zone',
    ST_GeogFromText('POLYGON((77.1500 28.5500, 77.2500 28.5500, 77.2500 28.6500, 77.1500 28.6500, 77.1500 28.5500))'),
    40,
    '{"start": "06:00", "end": "22:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]}'
),
-- Restricted area (e.g., government buildings)
(
    'Government Restricted Area',
    'High security government area - no deliveries allowed',
    'restricted_area',
    ST_GeogFromText('POLYGON((77.2000 28.6100, 77.2100 28.6100, 77.2100 28.6200, 77.2000 28.6200, 77.2000 28.6100))'),
    NULL,
    NULL
),
-- Speed limit zone
(
    'School Zone - Speed Limit',
    'Reduced speed zone near schools',
    'speed_limit_zone',
    ST_GeogFromText('POLYGON((77.2200 28.6300, 77.2300 28.6300, 77.2300 28.6400, 77.2200 28.6400, 77.2200 28.6300))'),
    20,
    '{"start": "07:00", "end": "18:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}'
);

-- 12. Create sample planned routes for existing trips
INSERT INTO PlannedRoutes (trip_id, route_points, estimated_duration_minutes, estimated_distance_km, max_deviation_meters)
SELECT 
    trip_id,
    ST_MakeLine(pickup_location::geometry, drop_location::geometry)::geography,
    30,
    ST_Distance(pickup_location, drop_location) / 1000,
    300
FROM Trips 
WHERE trip_id IN (
    '11111111-1111-1111-1111-111111111111',
    '22222222-2222-2222-2222-222222222222',
    '33333333-3333-3333-3333-333333333333'
);

-- 13. Update sample trips to require compliance
UPDATE Trips 
SET requires_compliance = TRUE, compliance_score = 95.0
WHERE trip_id IN (
    '11111111-1111-1111-1111-111111111111',
    '22222222-2222-2222-2222-222222222222',
    '33333333-3333-3333-3333-333333333333'
);

-- 14. Create view for compliance dashboard
CREATE OR REPLACE VIEW route_compliance_dashboard AS
SELECT 
    t.trip_id,
    t.customer_id,
    t.agent_id,
    u.name as agent_name,
    t.status as trip_status,
    t.requires_compliance,
    t.compliance_score,
    t.route_deviation_count,
    COUNT(rc.id) as total_violations,
    COUNT(CASE WHEN rc.severity = 'critical' THEN 1 END) as critical_violations,
    COUNT(CASE WHEN rc.severity = 'high' THEN 1 END) as high_violations,
    COUNT(CASE WHEN rc.resolved = FALSE THEN 1 END) as unresolved_violations,
    MAX(rc.created_at) as last_violation_time
FROM Trips t
LEFT JOIN Users u ON t.agent_id = u.id
LEFT JOIN RouteCompliance rc ON t.trip_id = rc.trip_id
WHERE t.requires_compliance = TRUE
GROUP BY t.trip_id, t.customer_id, t.agent_id, u.name, t.status, 
         t.requires_compliance, t.compliance_score, t.route_deviation_count;

-- 15. Add comments for documentation
COMMENT ON TABLE Geofences IS 'Geographic boundaries for route compliance monitoring';
COMMENT ON TABLE RouteCompliance IS 'Route compliance violations and events';
COMMENT ON TABLE PlannedRoutes IS 'Expected routes for compliance checking';
COMMENT ON VIEW route_compliance_dashboard IS 'Dashboard view for route compliance monitoring';

-- 16. Display summary of created data
SELECT 'Geofences Created' as summary, COUNT(*) as count FROM Geofences;
SELECT 'Planned Routes Created' as summary, COUNT(*) as count FROM PlannedRoutes;
SELECT 'Compliance-Required Trips' as summary, COUNT(*) as count FROM Trips WHERE requires_compliance = TRUE;

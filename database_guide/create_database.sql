-- Create Database and Extensions
-- Note: This script should be run from the postgres database first
-- CREATE DATABASE delivery_tracking;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS postgis;
-- CREATE EXTENSION IF NOT EXISTS timescaledb; -- Commented out - install TimescaleDB if needed

-- <PERSON><PERSON> ENUM types first
CREATE TYPE user_type_enum AS ENUM ('customer', 'agent', 'admin');
CREATE TYPE role_enum AS ENUM ('driver', 'delivery', 'hybrid');
CREATE TYPE source_enum AS ENUM ('mobile_sdk', 'gps_device', 'manual');
CREATE TYPE trip_type_enum AS ENUM ('ride', 'delivery');
CREATE TYPE trip_status_enum AS ENUM ('requested', 'accepted', 'picked_up', 'in_transit', 'delivered', 'completed', 'cancelled');
CREATE TYPE payment_status_enum AS ENUM ('pending', 'paid', 'failed');

-- 1. Users table (base table - no dependencies)
CREATE TABLE Users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    user_type user_type_enum NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- 2. AgentProfiles table (depends on Users)
CREATE TABLE AgentProfiles (
    user_id UUID PRIMARY KEY,
    vehicle_type TEXT,
    vehicle_no TEXT,
    license_no TEXT,
    rating FLOAT DEFAULT 0.0,
    is_available BOOLEAN DEFAULT false,
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE
);

-- 3. Trips table (depends on Users)
CREATE TABLE Trips (
    trip_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_type trip_type_enum NOT NULL,
    customer_id UUID NOT NULL,
    agent_id UUID NOT NULL,
    pickup_location GEOGRAPHY(POINT, 4326) NOT NULL,
    drop_location GEOGRAPHY(POINT, 4326) NOT NULL,
    status trip_status_enum NOT NULL DEFAULT 'requested',
    item_details JSONB,
    fare_estimate FLOAT,
    delivery_fee FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES Users(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE SET NULL
);

-- 4. TripStatusUpdates table (depends on Trips and Users)
CREATE TABLE TripStatusUpdates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL,
    status trip_status_enum NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    location GEOGRAPHY(POINT, 4326),
    updated_by UUID,
    note TEXT,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES Users(id) ON DELETE SET NULL
);

-- 5. LiveLocations table (depends on Users and Trips)
CREATE TABLE LiveLocations (
    agent_id UUID PRIMARY KEY,
    role role_enum NOT NULL,
    trip_id UUID,
    geom GEOGRAPHY(POINT, 4326) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    speed_m_s FLOAT NOT NULL,
    heading_deg FLOAT NOT NULL,
    accuracy_m FLOAT,
    altitude_m FLOAT,
    battery_pct FLOAT CHECK (battery_pct >= 0 AND battery_pct <= 100),
    source source_enum,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE SET NULL
);

-- 6. TrackingUpdates table (depends on Users and Trips) - This will be a hypertable
CREATE TABLE TrackingUpdates (
    trackpoint_id BIGSERIAL,
    agent_id UUID NOT NULL,
    role role_enum NOT NULL,
    trip_id UUID,
    geom GEOGRAPHY(POINT, 4326) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    speed_m_s FLOAT,
    heading_deg FLOAT,
    accuracy_m FLOAT,
    altitude_m FLOAT,
    battery_pct FLOAT CHECK (battery_pct >= 0 AND battery_pct <= 100),
    is_active BOOLEAN DEFAULT true,
    source source_enum,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE SET NULL
);

-- Convert TrackingUpdates to hypertable (TimescaleDB)
SELECT create_hypertable('TrackingUpdates', 'timestamp', 'agent_id', 4);

-- 7. TrackingArchives table (depends on Users)
CREATE TABLE TrackingArchives (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL,
    role role_enum NOT NULL,
    date DATE NOT NULL,
    track_points JSONB NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    total_distance FLOAT,
    battery_min FLOAT,
    battery_max FLOAT,
    trip_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE
);

-- 8. Payments table (depends on Trips)
CREATE TABLE Payments (
    payment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL,
    amount FLOAT NOT NULL,
    status payment_status_enum NOT NULL DEFAULT 'pending',
    payment_mode TEXT,
    transaction_ref TEXT,
    paid_at TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE
);

-- 9. Ratings table (depends on Trips and Users)
CREATE TABLE Ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL,
    from_user UUID NOT NULL,
    to_user UUID NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
    FOREIGN KEY (from_user) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (to_user) REFERENCES Users(id) ON DELETE CASCADE
);

-- Create Indexes for Performance
-- Users table indexes
CREATE INDEX idx_users_email ON Users(email);
CREATE INDEX idx_users_phone ON Users(phone);
CREATE INDEX idx_users_user_type ON Users(user_type);
CREATE INDEX idx_users_is_active ON Users(is_active);

-- AgentProfiles indexes
CREATE INDEX idx_agent_profiles_is_available ON AgentProfiles(is_available);
CREATE INDEX idx_agent_profiles_rating ON AgentProfiles(rating);

-- Trips table indexes
CREATE INDEX idx_trips_customer_id ON Trips(customer_id);
CREATE INDEX idx_trips_agent_id ON Trips(agent_id);
CREATE INDEX idx_trips_status ON Trips(status);
CREATE INDEX idx_trips_trip_type ON Trips(trip_type);
CREATE INDEX idx_trips_created_at ON Trips(created_at);
CREATE INDEX idx_trips_pickup_location ON Trips USING GIST(pickup_location);
CREATE INDEX idx_trips_drop_location ON Trips USING GIST(drop_location);

-- TripStatusUpdates indexes
CREATE INDEX idx_trip_status_updates_trip_id ON TripStatusUpdates(trip_id);
CREATE INDEX idx_trip_status_updates_timestamp ON TripStatusUpdates(timestamp);
CREATE INDEX idx_trip_status_updates_status ON TripStatusUpdates(status);
CREATE INDEX idx_trip_status_updates_location ON TripStatusUpdates USING GIST(location);

-- LiveLocations indexes
CREATE INDEX idx_live_locations_role ON LiveLocations(role);
CREATE INDEX idx_live_locations_trip_id ON LiveLocations(trip_id);
CREATE INDEX idx_live_locations_timestamp ON LiveLocations(timestamp);
CREATE INDEX idx_live_locations_geom ON LiveLocations USING GIST(geom);
CREATE INDEX idx_live_locations_updated_at ON LiveLocations(updated_at);

-- TrackingUpdates indexes (optimized for TimescaleDB)
CREATE INDEX idx_tracking_updates_agent_id_timestamp ON TrackingUpdates(agent_id, timestamp DESC);
CREATE INDEX idx_tracking_updates_trip_id ON TrackingUpdates(trip_id);
CREATE INDEX idx_tracking_updates_role ON TrackingUpdates(role);
CREATE INDEX idx_tracking_updates_geom ON TrackingUpdates USING GIST(geom);
CREATE INDEX idx_tracking_updates_is_active ON TrackingUpdates(is_active);

-- TrackingArchives indexes
CREATE INDEX idx_tracking_archives_agent_id ON TrackingArchives(agent_id);
CREATE INDEX idx_tracking_archives_date ON TrackingArchives(date);
CREATE INDEX idx_tracking_archives_role ON TrackingArchives(role);
CREATE INDEX idx_tracking_archives_created_at ON TrackingArchives(created_at);

-- Payments indexes
CREATE INDEX idx_payments_trip_id ON Payments(trip_id);
CREATE INDEX idx_payments_status ON Payments(status);
CREATE INDEX idx_payments_paid_at ON Payments(paid_at);

-- Ratings indexes
CREATE INDEX idx_ratings_trip_id ON Ratings(trip_id);
CREATE INDEX idx_ratings_from_user ON Ratings(from_user);
CREATE INDEX idx_ratings_to_user ON Ratings(to_user);
CREATE INDEX idx_ratings_rating ON Ratings(rating);
CREATE INDEX idx_ratings_created_at ON Ratings(created_at);

-- Enable compression on TrackingUpdates hypertable (compress data older than 7 days)
ALTER TABLE TrackingUpdates SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'agent_id',
    timescaledb.compress_orderby = 'timestamp DESC'
);

SELECT add_compression_policy('TrackingUpdates', INTERVAL '7 days');

-- Create retention policy (keep data for 1 year)
SELECT add_retention_policy('TrackingUpdates', INTERVAL '1 year');

-- Insert sample data for testing
INSERT INTO Users (name, email, phone, user_type) VALUES
('John Doe', '<EMAIL>', '+**********', 'customer'),
('Jane Smith', '<EMAIL>', '+**********', 'agent'),
('Admin User', '<EMAIL>', '+**********', 'admin'),
('Mike Driver', '<EMAIL>', '+**********', 'agent'),
('Sarah Customer', '<EMAIL>', '+**********', 'customer');

-- Insert sample agent profiles
INSERT INTO AgentProfiles (user_id, vehicle_type, vehicle_no, license_no, rating, is_available)
SELECT id, 'car', 'ABC123', 'DL123456', 4.5, true
FROM Users WHERE user_type = 'agent' AND email = '<EMAIL>';

INSERT INTO AgentProfiles (user_id, vehicle_type, vehicle_no, license_no, rating, is_available)
SELECT id, 'bike', 'XYZ789', 'DL789012', 4.2, false
FROM Users WHERE user_type = 'agent' AND email = '<EMAIL>';

COMMENT ON DATABASE delivery_tracking IS 'Delivery tracking system with GPS tracking, trip management, and real-time location updates';
COMMENT ON TABLE Users IS 'Base user table for customers, agents, and admins';
COMMENT ON TABLE AgentProfiles IS 'Extended profile information for delivery agents and drivers';
COMMENT ON TABLE Trips IS 'Trip records for both rides and deliveries';
COMMENT ON TABLE TripStatusUpdates IS 'Status change history for trips';
COMMENT ON TABLE LiveLocations IS 'Current real-time location of agents';
COMMENT ON TABLE TrackingUpdates IS 'Historical GPS tracking data (TimescaleDB hypertable)';
COMMENT ON TABLE TrackingArchives IS 'Daily archived tracking data for long-term storage';
COMMENT ON TABLE Payments IS 'Payment records for completed trips';
COMMENT ON TABLE Ratings IS 'Rating and review system for trips';

YOUR first TABLE --  TrackingUpdates  ---- | Column          | Type                   | Required? | Description                                                             |
| --------------- | ---------------------- | --------- | ----------------------------------------------------------------------- |
| `trackpoint_id` | UUID or BIGSERIAL      | ✅ Yes     | Primary key                                                             |
| `agent_id`      | UUID                   | ✅ Yes     | User ID of driver/delivery agent                                        |
| `role`          | ENUM                   | ✅ Yes     | `driver`, `delivery`, `hybrid` — helps filter later                     |
| `trip_id`       | UUID (nullable)        | Optional  | Could be `ride_id` or `delivery_id` if assigned to one                  |
| `geom`          | GEOGRAPHY(POINT, 4326) | ✅ Yes     | Latitude & longitude as spatial point                                   |
| `timestamp`     | TIMESTAMP              | ✅ Yes     | Time of GPS ping                                                        |
| `speed_m_s`     | FLOAT                  | Optional  | Speed in meters/second (if available from device)                       |
| `heading_deg`   | FLOAT                  | Optional  | Compass heading in degrees (0–360°)                                     |
| `accuracy_m`    | FLOAT                  | Optional  | GPS accuracy radius in meters                                           |
| `altitude_m`    | FLOAT                  | Optional  | Optional — elevation from sea level                                     |
| `battery_pct`   | FLOAT (0–100)          | Optional  | Helps detect battery-saving issues or session drops                     |
| `is_active`     | BOOLEAN                | Optional  | Mark if tracking session is active (used by agent status monitoring)    |
| `source`        | ENUM                   | Optional  | `mobile_sdk`, `gps_device`, `manual` — useful for debugging/trust level | 2ND TABLE YPU HAVE TO CREATE TrackingArchives -- 
| Column           | Type              | Required? | Description                                                |
| ---------------- | ----------------- | --------- | ---------------------------------------------------------- |
| `id`             | UUID or BIGSERIAL | ✅ Yes     | Primary key                                                |
| `agent_id`       | UUID              | ✅ Yes     | Agent (driver/delivery)                                    |
| `role`           | ENUM              | ✅ Yes     | `driver`, `delivery`, `hybrid` — to distinguish use case   |
| `date`           | DATE              | ✅ Yes     | The day this track represents                              |
| `track_points`   | JSONB             | ✅ Yes     | Array of lat/lng/timestamp/speed/etc. (archived ping data) |
| `start_time`     | TIMESTAMP         | Optional  | First ping time (day start)                                |
| `end_time`       | TIMESTAMP         | Optional  | Last ping time (day end)                                   |
| `total_distance` | FLOAT             | Optional  | Calculated from points (in km or meters)                   |
| `battery_min`    | FLOAT             | Optional  | Lowest battery % recorded                                  |
| `battery_max`    | FLOAT             | Optional  | Highest battery % recorded                                 |
| `trip_count`     | INT               | Optional  | Number of trips completed that day                         |
| `created_at`     | TIMESTAMP         | ✅ Yes     | When archive was inserted                                  |
YOUR 3RD TABLE WILL BE LiveLocations --- | Column        | Type                                 | Required? | Why it's needed for interpolation            |
| ------------- | ------------------------------------ | --------- | -------------------------------------------- |
| `agent_id`    | UUID (PK)                            | ✅ Yes     | Identifies the driver or delivery agent      |
| `role`        | ENUM(`driver`, `delivery`, `hybrid`) | ✅ Yes     | To filter which kind of agent                |
| `trip_id`     | UUID                                 | Optional  | Current active ride or delivery              |
| `geom`        | GEOGRAPHY(POINT, 4326)               | ✅ Yes     | Current location (lat/lng)                   |
| `timestamp`   | TIMESTAMP                            | ✅ Yes     | When the ping was received                   |
| `speed_m_s`   | FLOAT                                | ✅ Yes     | In meters/second → used to estimate movement |
| `heading_deg` | FLOAT                                | ✅ Yes     | 0–360° (direction the agent is moving)       |
| `accuracy_m`  | FLOAT                                | Optional  | GPS radius of accuracy                       |
| `altitude_m`  | FLOAT                                | Optional  | If useful in hilly areas                     |
| `battery_pct` | FLOAT (0–100)                        | Optional  | Device status; useful for UX/debugging       |
| `source`      | ENUM                                 | Optional  | `mobile_sdk`, `gps_device`, etc.             |
| `updated_at`  | TIMESTAMP DEFAULT NOW()              | ✅ Yes     | Auto-updated every time a new ping comes in  |  YOUR 4TH TABLE WILL BE --- | Column            | Type                     | Required? | Description                                        |
| ----------------- | ------------------------ | --------- | -------------------------------------------------- |
| `trip_id`         | UUID (PK)                | ✅ Yes     | Unique trip ID                                     |
| `trip_type`       | ENUM(`ride`, `delivery`) | ✅ Yes     | Used to distinguish type                           |
| `customer_id`     | FK → Users               | ✅ Yes     | Rider or order placer                              |
| `agent_id`        | FK → Users               | ✅ Yes     | Driver or delivery agent                           |
| `pickup_location` | GEOGRAPHY(POINT)         | ✅ Yes     | Start point                                        |
| `drop_location`   | GEOGRAPHY(POINT)         | ✅ Yes     | End point                                          |
| `status`          | ENUM                     | ✅ Yes     | Current state: `requested`, `accepted`, etc.       |
| `item_details`    | JSONB                    | Optional  | Used for delivery items (if trip\_type = delivery) |
| `fare_estimate`   | FLOAT                    | Optional  | For rides                                          |
| `delivery_fee`    | FLOAT                    | Optional  | For deliveries                                     |
| `created_at`      | TIMESTAMP                | ✅ Yes     | Time the trip was initiated                        |
| `completed_at`    | TIMESTAMP                | Optional  | Time the trip ended                                |
YOUR 5TH TABLLE WILL BE -- TripStatusUpdates ---- | Column       | Type             | Required? | Description                                                                   |
| ------------ | ---------------- | --------- | ----------------------------------------------------------------------------- |
| `id`         | UUID (PK)        | ✅ Yes     | Unique row identifier                                                         |
| `trip_id`    | FK → Trips       | ✅ Yes     | Links to the trip (either ride or delivery)                                   |
| `status`     | ENUM             | ✅ Yes     | Status of the trip at this point (e.g. `requested`, `picked_up`, `completed`) |
| `timestamp`  | TIMESTAMP        | ✅ Yes     | When the status update occurred                                               |
| `location`   | GEOGRAPHY(POINT) | Optional  | Where the event occurred (useful for logs, customer support, map views)       |
| `updated_by` | FK → Users       | Optional  | Who triggered the update (agent, system, customer)                            |
| `note`       | TEXT             | Optional  | Human-readable comment or error reason (e.g., "Customer not at location")     |
6TH TABLE -- | Column       | Type      | Purpose                      |
| ------------ | --------- | ---------------------------- |
| `id`         | UUID      | Unique user ID               |
| `name`       | TEXT      | Full name                    |
| `email`      | TEXT      | For login / contact          |
| `phone`      | TEXT      | Primary identifier           |
| `user_type`  | ENUM      | `customer`, `agent`, `admin` |
| `created_at` | TIMESTAMP | Date of registration         |
| `is_active`  | BOOLEAN   | For banning / suspending     |
6TH TABLE -- Users --- | Column       | Type      | Purpose                      |
| ------------ | --------- | ---------------------------- |
| `id`         | UUID      | Unique user ID               |
| `name`       | TEXT      | Full name                    |
| `email`      | TEXT      | For login / contact          |
| `phone`      | TEXT      | Primary identifier           |
| `user_type`  | ENUM      | `customer`, `agent`, `admin` |
| `created_at` | TIMESTAMP | Date of registration         |
| `is_active`  | BOOLEAN   | For banning / suspending     |
YOUR 7TH TABLE --- AgentProfiles --- | Column         | Type  | Notes                          |
| -------------- | ----- | ------------------------------ |
| `user_id`      | UUID  | FK to `Users`                  |
| `vehicle_type` | TEXT  | `bike`, `car`, `scooter`, etc. |
| `vehicle_no`   | TEXT  | Registration plate             |
| `license_no`   | TEXT  | Govt ID                        |
| `rating`       | FLOAT | From riders/customers          |
| `is_available` | BOOL  | Online/offline toggle          |
YOUR 8TH TABLE -- | Column            | Type       | Notes                       |
| ----------------- | ---------- | --------------------------- |
| `payment_id`      | UUID       | Unique transaction          |
| `trip_id`         | FK → Trips | Which trip                  |
| `amount`          | FLOAT      | Final fare/delivery fee     |
| `status`          | ENUM       | `pending`, `paid`, `failed` |
| `payment_mode`    | TEXT       | `cash`, `card`, `UPI`, etc. |
| `transaction_ref` | TEXT       | Stripe/Razorpay reference   |
| `paid_at`         | TIMESTAMP  | When payment succeeded      |
YOUR 9TH TABLE --- | Column       | Type       | Description              |
| ------------ | ---------- | ------------------------ |
| `id`         | UUID       | Unique                   |
| `trip_id`    | FK → Trips | What the rating is about |
| `from_user`  | FK → Users | Who gave the rating      |
| `to_user`    | FK → Users | Who received it          |
| `rating`     | INTEGER    | Typically 1–5            |
| `review`     | TEXT       | Optional comment         |
| `created_at` | TIMESTAMP  |                          |


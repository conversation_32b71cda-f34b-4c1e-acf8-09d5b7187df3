# Delivery Tracking Database Setup

This directory contains all the necessary files to set up the PostgreSQL database for the delivery tracking system.

## Prerequisites

Before running the database setup, ensure you have the following installed:

1. **PostgreSQL** (version 12 or higher)
2. **PostGIS** extension for spatial data
3. **TimescaleDB** extension for time-series data
4. **uuid-ossp** extension for UUID generation

### Installing Prerequisites

#### Ubuntu/Debian:
```bash
# Install PostgreSQL and PostGIS
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib postgis postgresql-postgis

# Install TimescaleDB
sudo sh -c "echo 'deb https://packagecloud.io/timescale/timescaledb/ubuntu/ $(lsb_release -c -s) main' > /etc/apt/sources.list.d/timescaledb.list"
wget --quiet -O - https://packagecloud.io/timescale/timescaledb/gpgkey | sudo apt-key add -
sudo apt-get update
sudo apt-get install timescaledb-2-postgresql-14
```

#### macOS (using Homebrew):
```bash
# Install PostgreSQL and PostGIS
brew install postgresql postgis

# Install TimescaleDB
brew tap timescale/tap
brew install timescaledb
```

## Database Schema Overview

The database consists of 9 main tables:

1. **Users** - Base user table (customers, agents, admins)
2. **AgentProfiles** - Extended profile for delivery agents
3. **Trips** - Trip records for rides and deliveries
4. **TripStatusUpdates** - Status change history
5. **LiveLocations** - Real-time agent locations
6. **TrackingUpdates** - Historical GPS data (TimescaleDB hypertable)
7. **TrackingArchives** - Daily archived tracking data
8. **Payments** - Payment records
9. **Ratings** - Rating and review system

## Setup Instructions

### Method 1: Automated Setup (Recommended)

1. **Run the setup script:**
   ```bash
   cd database_guide
   ./setup_database.sh
   ```

   This script will:
   - Check prerequisites
   - Create the database
   - Create all tables with proper relationships
   - Set up indexes for performance
   - Configure TimescaleDB hypertables
   - Insert sample data

### Method 2: Manual Setup

1. **Create the database manually:**
   ```bash
   psql -U postgres -c "CREATE DATABASE delivery_tracking;"
   ```

2. **Run the SQL script:**
   ```bash
   psql -U postgres -d delivery_tracking -f create_database.sql
   ```

## Database Features

### TimescaleDB Integration
- **TrackingUpdates** table is configured as a hypertable for efficient time-series data storage
- Automatic compression for data older than 7 days
- Retention policy to keep data for 1 year
- Optimized for high-frequency GPS tracking data

### Spatial Data Support
- Uses PostGIS for geographic data types
- Spatial indexes on location columns for fast proximity queries
- Support for GPS coordinates, routes, and geofencing

### Performance Optimizations
- Comprehensive indexing strategy
- Foreign key relationships with appropriate cascade rules
- Optimized for both read and write operations

## Environment Configuration

1. **Copy the environment template:**
   ```bash
   cp backend/.env.example backend/.env
   ```

2. **Update the database credentials in `.env`:**
   ```env
   DB_HOST=localhost
   DB_PORT=5432
   DB_USER=postgres
   DB_PASSWORD=your_password
   DB_NAME=delivery_tracking
   ```

## Verification

After setup, verify the installation:

```bash
# Check tables
psql -U postgres -d delivery_tracking -c "\dt"

# Check hypertables
psql -U postgres -d delivery_tracking -c "SELECT * FROM timescaledb_information.hypertables;"

# Check sample data
psql -U postgres -d delivery_tracking -c "SELECT COUNT(*) FROM Users;"
```

## Go Backend Integration

The Go backend configuration is available in `backend/config/database.go`. To use it:

```go
import "your-project/backend/config"

// Connect to database
db, err := config.ConnectDatabase()
if err != nil {
    log.Fatal(err)
}
defer db.Close()

// Test setup
err = config.TestDatabaseConnection()
if err != nil {
    log.Fatal(err)
}
```

## Troubleshooting

### Common Issues:

1. **TimescaleDB not found:**
   - Ensure TimescaleDB is properly installed and enabled
   - Check: `SELECT * FROM pg_available_extensions WHERE name = 'timescaledb';`

2. **PostGIS not found:**
   - Install PostGIS extension for your PostgreSQL version
   - Check: `SELECT * FROM pg_available_extensions WHERE name = 'postgis';`

3. **Permission denied:**
   - Ensure your PostgreSQL user has CREATE DATABASE privileges
   - Run: `ALTER USER postgres CREATEDB;`

4. **Connection refused:**
   - Check if PostgreSQL service is running
   - Verify connection parameters (host, port, username)

## Next Steps

After successful database setup:

1. Configure your Go backend to use the database
2. Set up API endpoints for CRUD operations
3. Implement real-time GPS tracking
4. Add authentication and authorization
5. Set up monitoring and logging

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check PostgreSQL logs for detailed error messages

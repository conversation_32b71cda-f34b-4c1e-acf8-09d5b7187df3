-- Sample Temperature Monitoring Data for Testing
-- This script creates sample temperature-sensitive trips and readings

-- 1. Create sample temperature-sensitive trips
INSERT INTO Trips (
    trip_id, trip_type, customer_id, agent_id, 
    pickup_location, drop_location, status,
    temperature_requirements, is_temperature_sensitive,
    item_details, delivery_fee, created_at
) VALUES 
-- Frozen food delivery (-18°C to -15°C)
(
    '11111111-1111-1111-1111-111111111111',
    'delivery',
    (SELECT id FROM Users WHERE user_type = 'customer' LIMIT 1),
    (SELECT id FROM Users WHERE user_type = 'agent' LIMIT 1),
    ST_SetSRID(ST_MakePoint(77.2090, 28.6139), 4326), -- Delhi pickup
    ST_SetSRID(ST_MakePoint(77.2290, 28.6339), 4326), -- Delhi drop
    'in_transit',
    '{"min_temperature": -18, "max_temperature": -15, "compliance_standard": "HACCP", "special_instructions": "Frozen food delivery - maintain cold chain"}',
    true,
    '{"items": [{"name": "Frozen Pizza", "quantity": 5}, {"name": "Ice Cream", "quantity": 3}], "weight": "2.5kg"}',
    150.00,
    NOW() - INTERVAL '2 hours'
),

-- Pharmaceutical delivery (2°C to 8°C)
(
    '*************-2222-2222-************',
    'delivery',
    (SELECT id FROM Users WHERE user_type = 'customer' LIMIT 1 OFFSET 1),
    (SELECT id FROM Users WHERE user_type = 'agent' LIMIT 1 OFFSET 1),
    ST_SetSRID(ST_MakePoint(77.1025, 28.7041), 4326), -- Delhi pickup
    ST_SetSRID(ST_MakePoint(77.1225, 28.7241), 4326), -- Delhi drop
    'in_transit',
    '{"min_temperature": 2, "max_temperature": 8, "compliance_standard": "FDA", "special_instructions": "Pharmaceutical cold chain - temperature critical"}',
    true,
    '{"items": [{"name": "Insulin", "quantity": 10}, {"name": "Vaccines", "quantity": 20}], "weight": "1.2kg"}',
    300.00,
    NOW() - INTERVAL '1 hour'
),

-- Fresh food delivery (0°C to 4°C)
(
    '*************-3333-3333-************',
    'delivery',
    (SELECT id FROM Users WHERE user_type = 'customer' LIMIT 1 OFFSET 2),
    (SELECT id FROM Users WHERE user_type = 'agent' LIMIT 1 OFFSET 2),
    ST_SetSRID(ST_MakePoint(77.3910, 28.5355), 4326), -- Gurgaon pickup
    ST_SetSRID(ST_MakePoint(77.4110, 28.5555), 4326), -- Gurgaon drop
    'picked_up',
    '{"min_temperature": 0, "max_temperature": 4, "compliance_standard": "HACCP", "special_instructions": "Fresh dairy products - keep refrigerated"}',
    true,
    '{"items": [{"name": "Fresh Milk", "quantity": 6}, {"name": "Yogurt", "quantity": 12}], "weight": "3.8kg"}',
    80.00,
    NOW() - INTERVAL '30 minutes'
);

-- 2. Create sample temperature readings for these trips
INSERT INTO TemperatureReadings (
    id, trip_id, agent_id, sensor_id, temperature_c, humidity_pct,
    battery_level, signal_strength, location, recorded_at, created_at
) VALUES 
-- Readings for frozen food delivery (trip 1)
(
    uuid_generate_v4(),
    '11111111-1111-1111-1111-111111111111',
    (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111'),
    'TEMP_SENSOR_001',
    -16.5, 45.2, 85.0, 95,
    ST_SetSRID(ST_MakePoint(77.2090, 28.6139), 4326),
    NOW() - INTERVAL '2 hours',
    NOW() - INTERVAL '2 hours'
),
(
    uuid_generate_v4(),
    '11111111-1111-1111-1111-111111111111',
    (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111'),
    'TEMP_SENSOR_001',
    -15.8, 44.8, 84.5, 94,
    ST_SetSRID(ST_MakePoint(77.2150, 28.6180), 4326),
    NOW() - INTERVAL '90 minutes',
    NOW() - INTERVAL '90 minutes'
),
(
    uuid_generate_v4(),
    '11111111-1111-1111-1111-111111111111',
    (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111'),
    'TEMP_SENSOR_001',
    -12.2, 46.1, 84.0, 92, -- Temperature breach! Too warm
    ST_SetSRID(ST_MakePoint(77.2200, 28.6220), 4326),
    NOW() - INTERVAL '60 minutes',
    NOW() - INTERVAL '60 minutes'
),

-- Readings for pharmaceutical delivery (trip 2)
(
    uuid_generate_v4(),
    '*************-2222-2222-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************'),
    'TEMP_SENSOR_002',
    4.2, 55.0, 92.0, 98,
    ST_SetSRID(ST_MakePoint(77.1025, 28.7041), 4326),
    NOW() - INTERVAL '1 hour',
    NOW() - INTERVAL '1 hour'
),
(
    uuid_generate_v4(),
    '*************-2222-2222-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************'),
    'TEMP_SENSOR_002',
    3.8, 54.5, 91.5, 97,
    ST_SetSRID(ST_MakePoint(77.1100, 28.7100), 4326),
    NOW() - INTERVAL '45 minutes',
    NOW() - INTERVAL '45 minutes'
),
(
    uuid_generate_v4(),
    '*************-2222-2222-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************'),
    'TEMP_SENSOR_002',
    10.5, 58.2, 91.0, 95, -- Temperature breach! Too warm
    ST_SetSRID(ST_MakePoint(77.1150, 28.7150), 4326),
    NOW() - INTERVAL '30 minutes',
    NOW() - INTERVAL '30 minutes'
),

-- Readings for fresh food delivery (trip 3) - Normal readings
(
    uuid_generate_v4(),
    '*************-3333-3333-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-3333-3333-************'),
    'TEMP_SENSOR_003',
    2.1, 65.0, 88.0, 90,
    ST_SetSRID(ST_MakePoint(77.3910, 28.5355), 4326),
    NOW() - INTERVAL '30 minutes',
    NOW() - INTERVAL '30 minutes'
),
(
    uuid_generate_v4(),
    '*************-3333-3333-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-3333-3333-************'),
    'TEMP_SENSOR_003',
    1.8, 64.5, 87.5, 89,
    ST_SetSRID(ST_MakePoint(77.3950, 28.5380), 4326),
    NOW() - INTERVAL '20 minutes',
    NOW() - INTERVAL '20 minutes'
),
(
    uuid_generate_v4(),
    '*************-3333-3333-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-3333-3333-************'),
    'TEMP_SENSOR_003',
    2.5, 63.8, 87.0, 88,
    ST_SetSRID(ST_MakePoint(77.4000, 28.5420), 4326),
    NOW() - INTERVAL '10 minutes',
    NOW() - INTERVAL '10 minutes'
);

-- 3. Update tracking data with temperature information for these trips
UPDATE TrackingUpdates 
SET temperature_c = -16.5, humidity_pct = 45.2
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111')
  AND timestamp >= NOW() - INTERVAL '2 hours'
  AND timestamp <= NOW() - INTERVAL '90 minutes';

UPDATE TrackingUpdates 
SET temperature_c = -12.2, humidity_pct = 46.1
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111')
  AND timestamp >= NOW() - INTERVAL '90 minutes'
  AND timestamp <= NOW() - INTERVAL '30 minutes';

UPDATE TrackingUpdates 
SET temperature_c = 4.2, humidity_pct = 55.0
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************')
  AND timestamp >= NOW() - INTERVAL '1 hour'
  AND timestamp <= NOW() - INTERVAL '45 minutes';

UPDATE TrackingUpdates 
SET temperature_c = 10.5, humidity_pct = 58.2
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************')
  AND timestamp >= NOW() - INTERVAL '45 minutes'
  AND timestamp <= NOW() - INTERVAL '15 minutes';

UPDATE TrackingUpdates 
SET temperature_c = 2.1, humidity_pct = 65.0
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '*************-3333-3333-************')
  AND timestamp >= NOW() - INTERVAL '30 minutes';

-- 4. Update live locations with current temperature
UPDATE LiveLocations 
SET temperature_c = -12.2, humidity_pct = 46.1
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111');

UPDATE LiveLocations 
SET temperature_c = 10.5, humidity_pct = 58.2
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************');

UPDATE LiveLocations 
SET temperature_c = 2.5, humidity_pct = 63.8
WHERE agent_id = (SELECT agent_id FROM Trips WHERE trip_id = '*************-3333-3333-************');

-- 5. Create some sample temperature alerts (these should be created automatically by triggers, but let's add some manually for testing)
INSERT INTO TemperatureAlerts (
    id, trip_id, agent_id, alert_type, temperature_c, humidity_pct,
    threshold_min, threshold_max, severity, location, created_at
) VALUES 
-- Alert for frozen food temperature breach
(
    uuid_generate_v4(),
    '11111111-1111-1111-1111-111111111111',
    (SELECT agent_id FROM Trips WHERE trip_id = '11111111-1111-1111-1111-111111111111'),
    'temperature_high',
    -12.2, 46.1, -18.0, -15.0, 'high',
    ST_SetSRID(ST_MakePoint(77.2200, 28.6220), 4326),
    NOW() - INTERVAL '60 minutes'
),

-- Alert for pharmaceutical temperature breach
(
    uuid_generate_v4(),
    '*************-2222-2222-************',
    (SELECT agent_id FROM Trips WHERE trip_id = '*************-2222-2222-************'),
    'temperature_high',
    10.5, 58.2, 2.0, 8.0, 'critical',
    ST_SetSRID(ST_MakePoint(77.1150, 28.7150), 4326),
    NOW() - INTERVAL '30 minutes'
);

-- 6. Add some comments for clarity
COMMENT ON TABLE TemperatureReadings IS 'Sample temperature sensor readings for cold chain monitoring';
COMMENT ON TABLE TemperatureAlerts IS 'Sample temperature breach alerts for testing';

-- 7. Display summary of created data
SELECT 
    'Temperature-Sensitive Trips Created' as summary,
    COUNT(*) as count
FROM Trips 
WHERE is_temperature_sensitive = true;

SELECT 
    'Temperature Readings Created' as summary,
    COUNT(*) as count
FROM TemperatureReadings;

SELECT 
    'Temperature Alerts Created' as summary,
    COUNT(*) as count
FROM TemperatureAlerts;

-- 8. Show the temperature monitoring dashboard view
SELECT * FROM temperature_monitoring_dashboard;

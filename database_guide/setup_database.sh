#!/bin/bash

# Database Setup Script for Delivery Tracking System
# This script creates the database, tables, and initial data

set -e  # Exit on any error

# Set PATH to use PostgreSQL 17
export PATH="/opt/homebrew/opt/postgresql@17/bin:$PATH"

# Configuration
DB_NAME="delivery_tracking"
DB_USER="hgcgh"
DB_HOST="localhost"
DB_PORT="5432"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Database Setup for Delivery Tracking System${NC}"

# Check if PostgreSQL is running
echo -e "${YELLOW}📋 Checking PostgreSQL connection...${NC}"
if ! pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER > /dev/null 2>&1; then
    echo -e "${RED}❌ PostgreSQL is not running or not accessible${NC}"
    echo -e "${YELLOW}Please ensure PostgreSQL is running and accessible at $DB_HOST:$DB_PORT${NC}"
    exit 1
fi

echo -e "${GREEN}✅ PostgreSQL is running${NC}"

# Check if TimescaleDB extension is available
echo -e "${YELLOW}📋 Checking TimescaleDB availability...${NC}"
if ! psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT * FROM pg_available_extensions WHERE name = 'timescaledb';" | grep -q timescaledb; then
    echo -e "${RED}❌ TimescaleDB extension is not available${NC}"
    echo -e "${YELLOW}Please install TimescaleDB extension for PostgreSQL${NC}"
    echo -e "${YELLOW}Visit: https://docs.timescale.com/install/latest/self-hosted/installation-linux/${NC}"
    exit 1
fi

echo -e "${GREEN}✅ TimescaleDB is available${NC}"

# Check if PostGIS extension is available
echo -e "${YELLOW}📋 Checking PostGIS availability...${NC}"
if ! psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT * FROM pg_available_extensions WHERE name = 'postgis';" | grep -q postgis; then
    echo -e "${RED}❌ PostGIS extension is not available${NC}"
    echo -e "${YELLOW}Please install PostGIS extension for PostgreSQL${NC}"
    echo -e "${YELLOW}Ubuntu/Debian: sudo apt-get install postgresql-postgis${NC}"
    echo -e "${YELLOW}CentOS/RHEL: sudo yum install postgis${NC}"
    exit 1
fi

echo -e "${GREEN}✅ PostGIS is available${NC}"

# Drop database if it exists (optional - comment out if you want to preserve existing data)
echo -e "${YELLOW}🗑️  Dropping existing database (if exists)...${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null || true

# Create database first
echo -e "${YELLOW}🏗️  Creating database...${NC}"
if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;"; then
    echo -e "${GREEN}✅ Database created successfully!${NC}"
else
    echo -e "${RED}❌ Database creation failed${NC}"
    exit 1
fi

# Run setup script on the new database
echo -e "${YELLOW}🏗️  Creating tables and extensions...${NC}"
if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f create_database.sql; then
    echo -e "${GREEN}✅ Database setup completed successfully!${NC}"
else
    echo -e "${RED}❌ Database setup failed${NC}"
    exit 1
fi

# Verify tables were created
echo -e "${YELLOW}📋 Verifying table creation...${NC}"
TABLE_COUNT=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")

if [ "$TABLE_COUNT" -ge 9 ]; then
    echo -e "${GREEN}✅ All tables created successfully ($TABLE_COUNT tables)${NC}"
else
    echo -e "${RED}❌ Expected at least 9 tables, but found $TABLE_COUNT${NC}"
    exit 1
fi

# Show created tables
echo -e "${YELLOW}📋 Created tables:${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dt"

# Show hypertables (TimescaleDB)
echo -e "${YELLOW}📋 Created hypertables:${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT * FROM timescaledb_information.hypertables;"

# Show sample data
echo -e "${YELLOW}📋 Sample data inserted:${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 'Users' as table_name, COUNT(*) as count FROM Users 
UNION ALL SELECT 'AgentProfiles', COUNT(*) FROM AgentProfiles 
UNION ALL SELECT 'Trips', COUNT(*) FROM Trips 
UNION ALL SELECT 'TripStatusUpdates', COUNT(*) FROM TripStatusUpdates 
UNION ALL SELECT 'LiveLocations', COUNT(*) FROM LiveLocations 
UNION ALL SELECT 'TrackingUpdates', COUNT(*) FROM TrackingUpdates 
UNION ALL SELECT 'TrackingArchives', COUNT(*) FROM TrackingArchives 
UNION ALL SELECT 'Payments', COUNT(*) FROM Payments 
UNION ALL SELECT 'Ratings', COUNT(*) FROM Ratings;"

echo -e "${GREEN}🎉 Database setup completed successfully!${NC}"
echo -e "${YELLOW}📝 Database Details:${NC}"
echo -e "   Database Name: $DB_NAME"
echo -e "   Host: $DB_HOST"
echo -e "   Port: $DB_PORT"
echo -e "   User: $DB_USER"
echo -e ""
echo -e "${YELLOW}🔗 Connection String:${NC}"
echo -e "   postgresql://$DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
echo -e ""
echo -e "${GREEN}✅ You can now connect to your database and start using the delivery tracking system!${NC}"

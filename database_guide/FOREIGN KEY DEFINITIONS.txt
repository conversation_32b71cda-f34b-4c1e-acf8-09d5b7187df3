-- Agent<PERSON><PERSON><PERSON>les
<PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE

-- Trips
FOREIGN KEY (customer_id) REFERENCES Users(id) ON DELETE SET NULL,
FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE SET NULL

-- TripStatusUpdates
FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
FOREI<PERSON><PERSON> KEY (updated_by) REFERENCES Users(id) ON DELETE SET NULL

-- LiveLocations
FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE

-- TrackingUpdates
FOREI<PERSON>N KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE,
FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE SET NULL

-- TrackingArchives
FOREIGN KEY (agent_id) REFERENCES Users(id) ON DELETE CASCADE

-- Payments
FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE

-- Ratings
FOREIGN KEY (trip_id) REFERENCES Trips(trip_id) ON DELETE CASCADE,
FOREIGN KEY (from_user) REFERENCES Users(id) ON DELETE CASCADE,
FOREIGN KEY (to_user) REFERENCES Users(id) ON DELETE CASCADE

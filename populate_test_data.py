#!/usr/bin/env python3
"""
Populate delivery tracking database with test data for the last 3 days.
This script imports GPS data from CSV files and generates timeline status events.
"""

import psycopg2
import csv
import uuid
from datetime import datetime, timedelta
import random

# Database connection details
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'hgcgh',
    'password': '',
    'database': 'delivery_tracking'
}

# CSV files and their corresponding dates
CSV_FILES = [
    {
        'file': '/Users/<USER>/Downloads/delivery-tracking/gpx_data.csv',
        'days_ago': 1,  # Yesterday
        'agent_id': 'f1817bdf-f096-43ba-af8d-7e5d8b01c93f',  # <PERSON>
        'trip_id': '550e8400-e29b-41d4-a716-************'
    },
    {
        'file': '/Users/<USER>/Downloads/delivery-tracking/gpx_data_2.csv',
        'days_ago': 2,  # 2 days ago
        'agent_id': '3b35ff6d-d482-4cb5-bbc1-ed78774c59b2',  # <PERSON>
        'trip_id': '550e8400-e29b-41d4-a716-************'
    },
    {
        'file': '/Users/<USER>/Downloads/delivery-tracking/gpx_data_3.csv',
        'days_ago': 3,  # 3 days ago
        'agent_id': '114ea59d-f561-4697-b7f4-295a5013872b',  # Priya Sharma
        'trip_id': '3f6ff092-d0e3-43be-bd9b-96d07e7aa7b3'
    }
]

# Available delivery statuses for timeline events
DELIVERY_STATUSES = [
    'accepted', 'picked_up', 'in_transit', 'out_for_delivery', 
    'delivered', 'failed', 'rescheduled'
]

def connect_db():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        return None

def import_gps_data(conn, csv_file, days_ago, agent_id, trip_id):
    """Import GPS data from CSV file into trackingupdates table."""
    cursor = conn.cursor()
    
    # Calculate base timestamp for this dataset
    base_date = datetime.now() - timedelta(days=days_ago)
    base_timestamp = base_date.replace(hour=9, minute=0, second=0, microsecond=0)
    
    print(f"📍 Importing GPS data from {csv_file} for {base_date.strftime('%Y-%m-%d')}")
    
    try:
        with open(csv_file, 'r') as file:
            csv_reader = csv.DictReader(file)
            gps_points = []
            
            for i, row in enumerate(csv_reader):
                if not row['latitude'] or not row['longitude']:
                    continue
                    
                # Calculate timestamp with 30-second intervals
                timestamp = base_timestamp + timedelta(seconds=i * 30)
                
                # Prepare GPS point data
                gps_point = {
                    'agent_id': agent_id,
                    'trip_id': trip_id,
                    'latitude': float(row['latitude']),
                    'longitude': float(row['longitude']),
                    'timestamp': timestamp,
                    'speed_m_s': random.uniform(0, 15),  # Random speed 0-15 m/s
                    'heading_deg': random.uniform(0, 360),  # Random heading
                    'accuracy_m': random.uniform(3, 10),  # GPS accuracy 3-10m
                    'altitude_m': float(row['elevation']) if row['elevation'] else random.uniform(200, 300),
                    'battery_pct': random.uniform(20, 100)  # Battery level
                }
                gps_points.append(gps_point)
            
            # Batch insert GPS points
            insert_query = """
                INSERT INTO trackingupdates (
                    agent_id, role, trip_id, geom, timestamp, 
                    speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source
                ) VALUES (
                    %s, 'driver', %s, ST_SetSRID(ST_MakePoint(%s, %s), 4326), %s,
                    %s, %s, %s, %s, %s, 'mobile_sdk'
                )
            """
            
            for point in gps_points:
                cursor.execute(insert_query, (
                    point['agent_id'], point['trip_id'],
                    point['longitude'], point['latitude'], point['timestamp'],
                    point['speed_m_s'], point['heading_deg'], point['accuracy_m'],
                    point['altitude_m'], point['battery_pct']
                ))
            
            conn.commit()
            print(f"✅ Imported {len(gps_points)} GPS points for agent {agent_id}")
            return gps_points
            
    except Exception as e:
        print(f"❌ Error importing GPS data from {csv_file}: {e}")
        conn.rollback()
        return []
    finally:
        cursor.close()

def generate_timeline_events(conn, gps_points, trip_id, agent_id):
    """Generate timeline status events based on GPS data."""
    if not gps_points:
        return
        
    cursor = conn.cursor()
    print(f"📅 Generating timeline events for trip {trip_id}")
    
    try:
        # Generate status events every ~50 GPS points
        event_interval = 50
        events_generated = 0
        
        for i in range(0, len(gps_points), event_interval):
            if i + event_interval >= len(gps_points):
                break
                
            # Select a random GPS point within the interval
            point_index = i + random.randint(0, min(event_interval - 1, len(gps_points) - i - 1))
            gps_point = gps_points[point_index]
            
            # Select random status (avoid 'requested' as it's default)
            status = random.choice(DELIVERY_STATUSES)
            
            # Generate event
            event_query = """
                INSERT INTO tripstatusupdates (
                    trip_id, timestamp, location, updated_by, status, note
                ) VALUES (
                    %s, %s, ST_SetSRID(ST_MakePoint(%s, %s), 4326), %s, %s, %s
                )
            """
            
            # Create a note for the event
            notes = {
                'accepted': 'Trip accepted by agent',
                'picked_up': 'Package picked up from sender',
                'in_transit': 'Package in transit',
                'out_for_delivery': 'Out for delivery',
                'delivered': 'Package delivered successfully',
                'failed': 'Delivery attempt failed',
                'rescheduled': 'Delivery rescheduled'
            }
            
            cursor.execute(event_query, (
                trip_id,
                gps_point['timestamp'],
                gps_point['longitude'],
                gps_point['latitude'],
                agent_id,
                status,
                notes.get(status, f'Status updated to {status}')
            ))
            
            events_generated += 1
        
        conn.commit()
        print(f"✅ Generated {events_generated} timeline events for trip {trip_id}")
        
    except Exception as e:
        print(f"❌ Error generating timeline events: {e}")
        conn.rollback()
    finally:
        cursor.close()

def main():
    """Main function to populate test data."""
    print("🚀 Starting database population with test data...")
    
    # Connect to database
    conn = connect_db()
    if not conn:
        return
    
    try:
        total_gps_points = 0
        total_events = 0
        
        # Process each CSV file
        for csv_config in CSV_FILES:
            print(f"\n📂 Processing {csv_config['file']}")
            
            # Import GPS data
            gps_points = import_gps_data(
                conn, 
                csv_config['file'], 
                csv_config['days_ago'],
                csv_config['agent_id'],
                csv_config['trip_id']
            )
            
            if gps_points:
                total_gps_points += len(gps_points)
                
                # Generate timeline events
                generate_timeline_events(
                    conn, 
                    gps_points, 
                    csv_config['trip_id'],
                    csv_config['agent_id']
                )
        
        # Final summary
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM trackingupdates")
        final_gps_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM tripstatusupdates")
        final_events_count = cursor.fetchone()[0]
        
        cursor.close()
        
        print(f"\n🎉 Database population completed successfully!")
        print(f"📊 Final Statistics:")
        print(f"   • GPS Points: {final_gps_count}")
        print(f"   • Timeline Events: {final_events_count}")
        print(f"   • Date Range: Last 3 days")
        print(f"   • Agents: 3 different agents")
        
    except Exception as e:
        print(f"❌ Error in main process: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()

# .github/workflows/update-ola-maps-sdk.yml
name: Update Ola Maps SDK Docs

on:
  schedule:
    - cron: '0 0 * * 0' # Runs every Sunday at midnight
  workflow_dispatch:

jobs:
  update-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Update Ola Maps SDK
        run: |
          rm -rf docs/ola-maps-sdk
          git clone https://github.com/ola-maps/olamaps-web-sdk.git docs/ola-maps-sdk
      - name: Commit and push if there are changes
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add docs/ola-maps-sdk
          git commit -m "Update Ola Maps SDK documentation" || echo "No changes to commit"
          git push

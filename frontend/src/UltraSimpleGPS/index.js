/**
 * UltraSimpleGPS Module - Main Entry Point
 * 
 * This is the main export point for the refactored UltraSimpleGPS component.
 * It maintains the same API as the original monolithic component while
 * providing a clean, modular architecture underneath.
 * 
 * Usage:
 *   import UltraSimpleGPS from './UltraSimpleGPS';
 *   <UltraSimpleGPS agentId={1} />
 */

import UltraSimpleGPS from './UltraSimpleGPS';

// Re-export the main component as default
export default UltraSimpleGPS;

// Named exports for advanced usage (if needed in the future)
export { default as UltraSimpleGPS } from './UltraSimpleGPS';

// Export sub-modules for direct access (if needed)
export * from './constants';
export * from './utils';
export * from './services';
export * from './hooks';
export * from './components';

/**
 * Map Configuration Constants
 * 
 * Configuration for OLA Maps, satellite view, zoom levels, and map styles
 */

// OLA Maps Configuration
export const OLA_MAPS_CONFIG = {
  API_KEY: 'dIrHt1iSmxED4qrHKnABwVRfKkzYQUez0BFR73kQ', // Use hardcoded key like original
  
  // Map styles
  STYLES: {
    DEFAULT: 'https://api.olamaps.io/tiles/vector/v1/styles/default-light-standard/style.json',
  },
  
  // Default map settings
  DEFAULT_CENTER: [77.5946, 12.9716], // Bangalore coordinates
  DEFAULT_ZOOM: 12,
  
  // Zoom limits
  ZOOM_LIMITS: {
    MIN: 1,
    MAX_VECTOR: 22,
    MAX_SATELLITE: 16, // Very conservative limit to prevent "map data not available" errors
  }
};

// SDK Loading Configuration
export const SDK_CONFIG = {
  PRIMARY_CDN: 'https://cdn.jsdelivr.net/npm/olamaps-web-sdk@1.1.2/dist/olamaps-web-sdk.umd.js',
  FALLBACK_CDN: 'https://unpkg.com/olamaps-web-sdk@1.1.2/dist/olamaps-web-sdk.umd.js',
};

// Satellite View Configuration
export const SATELLITE_CONFIG = {
  SOURCES: {
    'esri-satellite': {
      type: 'raster',
      tiles: ['https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'],
      tileSize: 256,
      maxzoom: 16 // Conservative limit to prevent data unavailable errors
    },
    'osm-satellite': {
      type: 'raster',
      tiles: ['https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'],
      tileSize: 256,
      maxzoom: 16 // Conservative limit to prevent data unavailable errors
    }
  },

  LAYERS: [
    {
      id: 'satellite-layer',
      type: 'raster',
      source: 'esri-satellite',
      minzoom: 0,
      maxzoom: 16 // Conservative limit to prevent data unavailable errors
    }
  ]
};

// GPS Line Styling
export const GPS_LINE_STYLE = {
  COLOR: '#4285F4',
  WIDTH: 3,
  OPACITY: 1.0,
  CAP: 'round',
  JOIN: 'round'
};

// Arrow Configuration
export const ARROW_CONFIG = {
  SIZE: 24,
  COLOR: '#4285F4',
  STROKE_COLOR: '#FFFFFF',
  STROKE_WIDTH: 2,
  
  // Arrow density based on zoom level
  DENSITY_BY_ZOOM: {
    8: { interval: 2000, maxArrows: 20 },   // Low zoom: fewer arrows, larger intervals
    12: { interval: 1000, maxArrows: 30 },  // Medium zoom: moderate density
    15: { interval: 500, maxArrows: 40 },   // High zoom: more arrows
    18: { interval: 200, maxArrows: 50 }    // Very high zoom: maximum density
  },
  
  DEFAULT_MAX_ARROWS: 50,
  LOOK_AHEAD_DISTANCE: 5
};

// Performance Configuration
export const PERFORMANCE_CONFIG = {
  // GPS Cleaning Parameters (tuned to remove unwanted straight lines)
  GPS_CLEANING_TOLERANCE: 0.00005,
  MAX_SPEED_KMH: 80,              // Reduced from 120 to be more aggressive
  TIME_GAP_SECONDS: 3,
  DETOUR_RATIO_THRESHOLD: 1.8,    // Reduced from 2.5 to remove more outliers
  MAX_DISTANCE_JUMP_METERS: 500,  // New: Remove points that jump more than 500m
  MIN_POINT_DISTANCE_METERS: 5,   // New: Minimum distance between points

  // Caching
  CACHE_PREFIX: 'ultraSimpleGPS_',
  ROUTE_VISIBILITY_KEY: 'ultraSimpleGPS_showRoutes',

  // Timing
  MAP_INIT_DELAY: 100,
  STYLE_CHANGE_DELAY: 1000,
  ARROW_UPDATE_DELAY: 500,
  CLEANUP_DELAY: 100,
  DATE_CHANGE_DELAY: 500,
  DATE_CHANGE_RESET_DELAY: 1500
};

/**
 * Marker Configuration Constants
 * 
 * Configuration for all types of markers: status, timeline, delivery, start/end
 */

// Default Status Marker Colors
export const STATUS_MARKER_COLORS = {
  'requested': '#2196F3',      // <PERSON>
  'accepted': '#4CAF50',       // <PERSON>
  'picked_up': '#FF9800',      // Orange
  'in_transit': '#9C27B0',     // Purple
  'out_for_delivery': '#FF5722', // Deep Orange
  'delivered': '#4CAF50',      // <PERSON>
  'failed': '#F44336',         // Red
  'rescheduled': '#9C27B0',    // Purple
  'cancelled': '#757575',      // <PERSON>
  'returned': '#795548'        // <PERSON>
};

// Default Status Marker Icons
export const STATUS_MARKER_ICONS = {
  'requested': '?',
  'accepted': '✓',
  'picked_up': '📦',
  'in_transit': '🚚',
  'out_for_delivery': '🚛',
  'delivered': '✅',
  'failed': '❌',
  'rescheduled': '📅',
  'cancelled': '🚫',
  'returned': '↩️'
};

// Marker Size Configuration
export const MARKER_SIZES = {
  STATUS: {
    DEFAULT: 28,
    SMALL: 20,
    LARGE: 36
  },
  
  TIMELINE: {
    DEFAULT: 24,
    SMALL: 18,
    LARGE: 30
  },
  
  DELIVERY: {
    PICKUP: 30,
    DROP: 30
  },
  
  START_END: {
    START: 32,
    END: 32
  }
};

// Marker Border Configuration
export const MARKER_BORDERS = {
  DEFAULT_COLOR: '#FFFFFF',
  DEFAULT_WIDTH: 2,
  SELECTED_COLOR: '#FFD700',
  SELECTED_WIDTH: 3
};

// Timeline Event Configuration
export const TIMELINE_EVENT_CONFIG = {
  COLORS: {
    DEFAULT: '#2196F3',
    CHECKED_IN: '#4CAF50',
    CHECKED_OUT: '#FF9800',
    BREAK_START: '#9C27B0',
    BREAK_END: '#9C27B0',
    DELIVERED: '#4CAF50',
    FAILED: '#F44336'
  },
  
  ICONS: {
    DEFAULT: '📍',
    CHECKED_IN: '🟢',
    CHECKED_OUT: '🟡',
    BREAK_START: '⏸️',
    BREAK_END: '▶️',
    DELIVERED: '✅',
    FAILED: '❌'
  }
};

// Start/End Marker Configuration
export const START_END_MARKERS = {
  START: {
    COLOR: '#4CAF50',
    ICON: '🏠',
    BORDER_COLOR: '#FFFFFF',
    SIZE: 32
  },
  
  END: {
    COLOR: '#F44336',
    ICON: '🎯',
    BORDER_COLOR: '#FFFFFF',
    SIZE: 32,
    SHOW_PROFILE_PHOTO: true
  }
};

// Delivery Marker Configuration
export const DELIVERY_MARKERS = {
  PICKUP: {
    COLOR: '#2196F3',
    ICON: '📦',
    LABEL: 'PICKUP',
    SIZE: 30
  },
  
  DROP: {
    COLOR: '#4CAF50',
    ICON: '🎯',
    LABEL: 'DROP',
    SIZE: 30
  }
};

// Marker CSS Classes
export const MARKER_CLASSES = {
  BASE: 'custom-marker',
  STATUS: 'status-marker',
  TIMELINE: 'timeline-marker',
  DELIVERY_PICKUP: 'delivery-pickup-marker',
  DELIVERY_DROP: 'delivery-drop-marker',
  START: 'start-marker',
  END: 'end-marker'
};

// Marker Selectors for Cleanup
export const MARKER_SELECTORS = [
  '.custom-marker',
  '.timeline-marker',
  '.status-marker',
  '.delivery-pickup-marker',
  '.delivery-drop-marker',
  '.start-marker',
  '.end-marker',
  '.gps-direction-arrow',
  '.delivery-route-line'
];

// Route Line Configuration
export const ROUTE_LINE_CONFIG = {
  COLORS: {
    OLA_DIRECTIONS: '#4CAF50',
    OLA_DIRECTIONS_BASIC: '#2196F3',
    OLA_DISTANCE_MATRIX: '#FF9800',
    OLA_DISTANCE_MATRIX_BASIC: '#9C27B0',
    STRAIGHT_LINE: '#F44336'
  },
  
  WIDTHS: {
    DEFAULT: 3,
    HIGHLIGHTED: 5
  },
  
  OPACITY: {
    DEFAULT: 0.8,
    HIGHLIGHTED: 1.0
  },
  
  DASH_PATTERNS: {
    SOLID: [],
    DASHED: [5, 5],
    DOTTED: [2, 3]
  }
};

/**
 * Constants Module - Main Export Point
 * 
 * Re-exports all constants for easy importing
 */

// API Configuration
export * from './apiEndpoints';

// Map Configuration
export * from './mapConfig';

// Marker Configuration
export * from './markerConfig';

// Additional constants that don't fit in other categories
export const EARTH_RADIUS = {
  KM: 6371,
  MILES: 3959,
  METERS: 6371000
};

export const DISTANCE_UNITS = {
  KM: 'km',
  MILES: 'miles',
  METERS: 'meters'
};

export const CONVERSION_FACTORS = {
  METERS_TO_KM: 1000,
  METERS_TO_MILES: 0.000621371,
  KM_TO_MILES: 0.621371,
  MILES_TO_KM: 1.609344
};

export const DATE_FORMATS = {
  TODAY: 'today',
  YESTERDAY: 'yesterday',
  ISO_DATE: 'YYYY-MM-DD'
};

export const STORAGE_KEYS = {
  ROUTE_VISIBILITY: 'ultraSimpleGPS_showRoutes',
  DATA_CACHE_PREFIX: 'ultraSimpleGPS_cache_'
};

export const TIMELINE_EVENT_TYPES = {
  CHECKED_IN: 'checked_in',
  CHECKED_OUT: 'checked_out',
  BREAK_START: 'break_start',
  BREAK_END: 'break_end',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  RESCHEDULED: 'rescheduled'
};

export const DELIVERY_STATUSES = {
  REQUESTED: 'requested',
  ACCEPTED: 'accepted',
  PICKED_UP: 'picked_up',
  IN_TRANSIT: 'in_transit',
  OUT_FOR_DELIVERY: 'out_for_delivery',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  RESCHEDULED: 'rescheduled',
  CANCELLED: 'cancelled',
  RETURNED: 'returned'
};

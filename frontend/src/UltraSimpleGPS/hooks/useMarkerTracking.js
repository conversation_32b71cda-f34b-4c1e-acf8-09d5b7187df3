/**
 * useMarkerTracking Hook
 * 
 * Custom hook for managing marker lifecycle, tracking, and cleanup
 */

import { useCallback, useEffect } from 'react';
import { MarkerService } from '../services';

export const useMarkerTracking = (mapInstance) => {
    /**
     * Set custom marker settings
     */
    const setCustomMarkerSettings = useCallback((settings) => {
        MarkerService.setCustomMarkerSettings(settings);
    }, []);

    /**
     * Add status markers to map
     */
    const addStatusMarkers = useCallback((statusMarkersData) => {
        if (!mapInstance) {
            console.log('⚠️ Cannot add status markers: no map instance');
            return;
        }
        MarkerService.addStatusMarkers(mapInstance, statusMarkersData);
    }, [mapInstance]);

    /**
     * Add timeline event markers to map
     */
    const addTimelineEventMarkers = useCallback((events) => {
        if (!mapInstance) {
            console.log('⚠️ Cannot add timeline markers: no map instance');
            return;
        }
        MarkerService.addTimelineEventMarkers(mapInstance, events);
    }, [mapInstance]);

    /**
     * Add delivery markers to map
     */
    const addDeliveryMarkers = useCallback((deliveries) => {
        if (!mapInstance || !deliveries || deliveries.length === 0) {
            console.log('🚚 No deliveries to add to map');
            return;
        }

        console.log(`🚚 Adding ${deliveries.length} delivery markers to map`);

        const mapContainer = mapInstance.getContainer();
        const markerElements = [];
        let markersAdded = 0;

        deliveries.forEach((delivery, index) => {
            try {
                // Add pickup marker
                if (delivery.pickup_location) {
                    const pickupMarker = document.createElement('div');
                    pickupMarker.className = 'delivery-pickup-marker';
                    pickupMarker.style.cssText = `
                        position: absolute;
                        width: 30px;
                        height: 30px;
                        background-color: #2196F3;
                        border: 2px solid #FFFFFF;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        color: white;
                        cursor: pointer;
                        z-index: 1000;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    `;
                    pickupMarker.innerHTML = '📦';

                    // Position pickup marker
                    const pickupPixel = mapInstance.project([delivery.pickup_location.longitude, delivery.pickup_location.latitude]);
                    pickupMarker.style.left = `${pickupPixel.x - 15}px`;
                    pickupMarker.style.top = `${pickupPixel.y - 15}px`;

                    // Add click handler for pickup marker
                    const pickupClickHandler = () => {
                        const locationName = delivery.pickup_location?.display_name ||
                                           delivery.pickup_location?.address ||
                                           'Pickup Location';
                        const info = `🚚 PICKUP LOCATION\n\n📍 ${locationName}\n\nCustomer: ${delivery.customer_name || 'Unknown'}\nPhone: ${delivery.customer_phone || 'N/A'}\nItem: ${delivery.item_details?.description || 'No description'}\nWeight: ${delivery.item_details?.weight || 'N/A'}\nValue: ₹${delivery.item_details?.value || 'N/A'}\nFee: ₹${delivery.delivery_fee || 0}\nStatus: ${delivery.status.toUpperCase()}\nCreated: ${new Date(delivery.created_at).toLocaleString()}`;
                        alert(info);
                    };

                    pickupMarker.addEventListener('click', pickupClickHandler);

                    // Add to global tracking system
                    MarkerService.trackedMarkers.push(pickupMarker);
                    MarkerService.eventListeners.push(() => {
                        pickupMarker.removeEventListener('click', pickupClickHandler);
                    });

                    // Add to container
                    mapContainer.appendChild(pickupMarker);
                    markerElements.push({ element: pickupMarker, coordinates: [delivery.pickup_location.longitude, delivery.pickup_location.latitude] });
                    markersAdded++;
                }

                // Add drop marker
                if (delivery.drop_location) {
                    const dropMarker = document.createElement('div');
                    dropMarker.className = 'delivery-drop-marker';
                    dropMarker.style.cssText = `
                        position: absolute;
                        width: 30px;
                        height: 30px;
                        background-color: #4CAF50;
                        border: 2px solid #FFFFFF;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        color: white;
                        cursor: pointer;
                        z-index: 1000;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    `;
                    dropMarker.innerHTML = '🎯';

                    // Position drop marker
                    const dropPixel = mapInstance.project([delivery.drop_location.longitude, delivery.drop_location.latitude]);
                    dropMarker.style.left = `${dropPixel.x - 15}px`;
                    dropMarker.style.top = `${dropPixel.y - 15}px`;

                    // Add click handler for drop marker
                    const dropClickHandler = () => {
                        const locationName = delivery.drop_location?.display_name ||
                                           delivery.drop_location?.address ||
                                           'Drop Location';
                        const info = `🎯 DROP LOCATION\n\n📍 ${locationName}\n\nCustomer: ${delivery.customer_name || 'Unknown'}\nPhone: ${delivery.customer_phone || 'N/A'}\nItem: ${delivery.item_details?.description || 'No description'}\nWeight: ${delivery.item_details?.weight || 'N/A'}\nValue: ₹${delivery.item_details?.value || 'N/A'}\nFee: ₹${delivery.delivery_fee || 0}\nStatus: ${delivery.status.toUpperCase()}\nCreated: ${new Date(delivery.created_at).toLocaleString()}`;
                        alert(info);
                    };

                    dropMarker.addEventListener('click', dropClickHandler);

                    // Add to global tracking system
                    MarkerService.trackedMarkers.push(dropMarker);
                    MarkerService.eventListeners.push(() => {
                        dropMarker.removeEventListener('click', dropClickHandler);
                    });

                    // Add to container
                    mapContainer.appendChild(dropMarker);
                    markerElements.push({ element: dropMarker, coordinates: [delivery.drop_location.longitude, delivery.drop_location.latitude] });
                    markersAdded++;
                }

            } catch (error) {
                console.error(`❌ Error adding delivery markers for delivery ${index}:`, error);
            }
        });

        // Update marker positions when map moves/zooms
        const updateDeliveryMarkerPositions = () => {
            markerElements.forEach(({ element, coordinates }) => {
                const pixel = mapInstance.project(coordinates);
                element.style.left = `${pixel.x - 15}px`;
                element.style.top = `${pixel.y - 15}px`;
            });
        };

        mapInstance.on('move', updateDeliveryMarkerPositions);
        mapInstance.on('zoom', updateDeliveryMarkerPositions);

        console.log(`✅ Added ${markersAdded} delivery markers to map`);
    }, [mapInstance]);

    /**
     * Clear all markers
     */
    const clearAllMarkers = useCallback(() => {
        return MarkerService.nuclearClearAllMarkers();
    }, []);

    /**
     * Count current markers on map
     */
    const countCurrentMarkers = useCallback(() => {
        return MarkerService.countCurrentMarkers(mapInstance);
    }, [mapInstance]);

    /**
     * Get status marker color
     */
    const getStatusMarkerColor = useCallback((status) => {
        return MarkerService.getStatusMarkerColor(status);
    }, []);

    /**
     * Get status marker icon
     */
    const getStatusMarkerIcon = useCallback((status) => {
        return MarkerService.getStatusMarkerIcon(status);
    }, []);

    /**
     * Get timeline event title
     */
    const getTimelineEventTitle = useCallback((event) => {
        return MarkerService.getTimelineEventTitle(event);
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            clearAllMarkers();
        };
    }, [clearAllMarkers]);

    return {
        // Actions
        setCustomMarkerSettings,
        addStatusMarkers,
        addTimelineEventMarkers,
        addDeliveryMarkers,
        clearAllMarkers,
        countCurrentMarkers,
        
        // Utilities
        getStatusMarkerColor,
        getStatusMarkerIcon,
        getTimelineEventTitle
    };
};

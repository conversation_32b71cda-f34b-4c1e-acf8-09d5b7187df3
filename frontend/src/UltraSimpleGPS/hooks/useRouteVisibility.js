/**
 * useRouteVisibility Hook
 * 
 * Custom hook for managing route visibility toggle and localStorage persistence
 */

import { useState, useCallback, useEffect } from 'react';
import { STORAGE_KEYS } from '../constants';

export const useRouteVisibility = () => {
    // Initialize from localStorage, default to FALSE (routes OFF by default for cost saving)
    const [showRoutes, setShowRoutes] = useState(() => {
        const saved = localStorage.getItem(STORAGE_KEYS.ROUTE_VISIBILITY);
        const defaultValue = saved !== null ? JSON.parse(saved) : false;
        console.log('🛣️ ULTRA GPS INIT: Route visibility initialized to:', defaultValue, 'from localStorage:', saved);
        return defaultValue;
    });

    /**
     * Toggle route visibility
     */
    const toggleRouteVisibility = useCallback(() => {
        const newValue = !showRoutes;
        setShowRoutes(newValue);
        localStorage.setItem(STORAGE_KEYS.ROUTE_VISIBILITY, JSON.stringify(newValue));
        
        console.log(`🛣️ Route visibility toggled to: ${newValue ? 'ON' : 'OFF'}`);
        
        // If routes are disabled, clear existing routes from map
        if (!newValue && window.olaMapInstance) {
            const routeElements = document.querySelectorAll('.delivery-route-line');
            routeElements.forEach(element => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            });
            console.log('🛣️ Cleared existing route lines from map');
        }
        
        return newValue;
    }, [showRoutes]);

    /**
     * Set route visibility directly
     */
    const setRouteVisibility = useCallback((visible) => {
        setShowRoutes(visible);
        localStorage.setItem(STORAGE_KEYS.ROUTE_VISIBILITY, JSON.stringify(visible));
        
        console.log(`🛣️ Route visibility set to: ${visible ? 'ON' : 'OFF'}`);
        
        // If routes are disabled, clear existing routes from map
        if (!visible && window.olaMapInstance) {
            const routeElements = document.querySelectorAll('.delivery-route-line');
            routeElements.forEach(element => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            });
            console.log('🛣️ Cleared existing route lines from map');
        }
    }, []);

    /**
     * Get current route visibility status
     */
    const getRouteVisibility = useCallback(() => {
        return showRoutes;
    }, [showRoutes]);

    /**
     * Reset to default (OFF)
     */
    const resetToDefault = useCallback(() => {
        setRouteVisibility(false);
    }, [setRouteVisibility]);

    // Log changes for debugging
    useEffect(() => {
        console.log(`🛣️ Route visibility changed to: ${showRoutes ? 'ON' : 'OFF'}`);
    }, [showRoutes]);

    return {
        // State
        showRoutes,
        
        // Actions
        toggleRouteVisibility,
        setRouteVisibility,
        getRouteVisibility,
        resetToDefault
    };
};

/**
 * useMapInstance Hook
 * 
 * Custom hook for managing map instance, initialization, and map operations
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { MapService } from '../services';
import { PERFORMANCE_CONFIG } from '../constants';

export const useMapInstance = () => {
    // State
    const [isSatelliteView, setIsSatelliteView] = useState(false);
    const [isHighZoomFallback, setIsHighZoomFallback] = useState(false);

    // Refs
    const mapRef = useRef(null);
    const mapInstanceRef = useRef(null);

    /**
     * Initialize map
     */
    const initializeMap = useCallback(async (container, options = {}) => {
        try {
            console.log('🚀 Starting map initialization...');

            // Validate container
            const targetContainer = container || mapRef.current;
            if (!targetContainer) {
                throw new Error('Map container is not available. Make sure mapRef is properly set.');
            }

            // Ensure options have valid defaults
            const safeOptions = {
                center: [77.5946, 12.9716], // Bangalore coordinates as fallback
                zoom: 12,
                ...options
            };

            // Validate center coordinates
            if (!Array.isArray(safeOptions.center) || safeOptions.center.length !== 2) {
                console.warn('⚠️ Invalid center in options, using default');
                safeOptions.center = [77.5946, 12.9716];
            }

            // Validate zoom
            if (typeof safeOptions.zoom !== 'number' || isNaN(safeOptions.zoom)) {
                console.warn('⚠️ Invalid zoom in options, using default');
                safeOptions.zoom = 12;
            }

            console.log('🗺️ Initializing map with options:', safeOptions);

            const mapInstance = await MapService.initializeMap(targetContainer, safeOptions);
            mapInstanceRef.current = mapInstance;

            console.log('✅ Map initialized successfully');
            return mapInstance;

        } catch (error) {
            console.error('❌ Map initialization failed:', error);
            throw error;
        }
    }, []);

    /**
     * Toggle satellite view
     */
    const toggleSatelliteView = useCallback(() => {
        const newSatelliteState = !isSatelliteView;
        setIsSatelliteView(newSatelliteState);

        console.log(`🛰️ Switching to ${newSatelliteState ? 'satellite' : 'standard'} view...`);

        MapService.toggleSatelliteView(newSatelliteState);

        if (newSatelliteState) {
            // Add zoom listener for satellite view feedback
            const handleSatelliteZoom = () => {
                const currentZoom = mapInstanceRef.current?.getZoom();
                const maxSafeZoom = 16; // Conservative satellite zoom limit

                if (currentZoom >= maxSafeZoom - 1) {
                    console.log(`🛰️ HIGH ZOOM: Approaching maximum satellite zoom ${currentZoom.toFixed(1)}/${maxSafeZoom}`);
                    setIsHighZoomFallback(true);
                } else {
                    setIsHighZoomFallback(false);
                }

                // Show user-friendly message when at zoom limit
                if (currentZoom >= maxSafeZoom) {
                    console.log(`⚠️ Maximum satellite zoom reached (${maxSafeZoom}). Higher zoom levels may show "map data not available".`);
                }
            };

            if (mapInstanceRef.current) {
                mapInstanceRef.current.on('zoomend', handleSatelliteZoom);
                mapInstanceRef.current.on('zoom', handleSatelliteZoom); // Also check during zoom
                handleSatelliteZoom(); // Check initial zoom
            }
        } else {
            setIsHighZoomFallback(false);
        }
    }, [isSatelliteView]);

    /**
     * Fit map to bounds
     */
    const fitToBounds = useCallback((bounds, options = {}) => {
        MapService.fitToBounds(bounds, options);
    }, []);

    /**
     * Add source to map
     */
    const addSource = useCallback((id, source) => {
        MapService.addSource(id, source);
    }, []);

    /**
     * Add layer to map
     */
    const addLayer = useCallback((layer) => {
        MapService.addLayer(layer);
    }, []);

    /**
     * Remove layer from map
     */
    const removeLayer = useCallback((layerId) => {
        MapService.removeLayer(layerId);
    }, []);

    /**
     * Remove source from map
     */
    const removeSource = useCallback((sourceId) => {
        MapService.removeSource(sourceId);
    }, []);

    /**
     * Get current map instance
     */
    const getMapInstance = useCallback(() => {
        return mapInstanceRef.current || MapService.getMapInstance();
    }, []);

    /**
     * Check if map is loaded
     */
    const isMapLoaded = useCallback(() => {
        return MapService.isMapLoaded();
    }, []);

    /**
     * Update map with GPS data
     */
    const updateMapWithData = useCallback((gpsPoints, timelineEvents, statusMarkers) => {
        const mapInstance = getMapInstance();
        if (!mapInstance) {
            console.log('⚠️ No map instance available for data update');
            return;
        }

        // GPS line drawing is handled by useGPSData hook with proper cleaning
    }, [getMapInstance]);

    /**
     * Show last known location on map
     */
    const showLastKnownLocationOnMap = useCallback((location) => {
        const mapInstance = getMapInstance();
        if (!mapInstance || !location) return;

        console.log('📍 Showing last known location on map:', location);

        try {
            // Center map on last known location
            mapInstance.easeTo({
                center: [location.longitude, location.latitude],
                zoom: 15,
                duration: 1000
            });

            // Add a marker for last known location
            const markerElement = document.createElement('div');
            markerElement.className = 'last-known-location-marker';
            markerElement.style.cssText = `
                position: absolute;
                width: 32px;
                height: 32px;
                background-color: #FF5722;
                border: 3px solid #FFFFFF;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: white;
                cursor: pointer;
                z-index: 1000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            `;
            markerElement.innerHTML = '📍';

            // Add click handler
            markerElement.addEventListener('click', () => {
                const info = `Last Known Location\n\nDate: ${location.date || 'Unknown'}\nSource: ${location.source || 'Unknown'}\nDays Back: ${location.daysBack || 'N/A'}`;
                alert(info);
            });

            // Position marker
            const updateMarkerPosition = () => {
                const pixel = mapInstance.project([location.longitude, location.latitude]);
                markerElement.style.left = pixel.x + 'px';
                markerElement.style.top = pixel.y + 'px';
            };

            // Add to map container
            const mapContainer = mapInstance.getContainer();
            mapContainer.appendChild(markerElement);

            // Initial positioning
            updateMarkerPosition();

            // Update position on map movement
            mapInstance.on('move', updateMarkerPosition);
            mapInstance.on('zoom', updateMarkerPosition);

            console.log('✅ Last known location marker added to map');

        } catch (error) {
            console.error('❌ Error showing last known location:', error);
        }
    }, [getMapInstance]);

    /**
     * Cleanup map instance
     */
    const cleanup = useCallback(() => {
        if (mapInstanceRef.current) {
            try {
                MapService.destroy();
                mapInstanceRef.current = null;
                console.log('✅ Map instance cleaned up');
            } catch (error) {
                console.error('❌ Error cleaning up map:', error);
            }
        }
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            cleanup();
        };
    }, [cleanup]);

    return {
        // State
        isSatelliteView,
        isHighZoomFallback,

        // Refs
        mapRef,
        mapInstanceRef,

        // Actions
        initializeMap,
        toggleSatelliteView,
        fitToBounds,
        addSource,
        addLayer,
        removeLayer,
        removeSource,
        getMapInstance,
        isMapLoaded,
        updateMapWithData,
        showLastKnownLocationOnMap,
        cleanup
    };
};

/**
 * useGPSData Hook
 * 
 * Custom hook for managing GPS data fetching, caching, and state
 */

import { useState, useCallback, useRef } from 'react';
import { TrackingDataService, DeliveryService, MapService } from '../services';
import {
    calculateTotalDistanceFromGpsPoints,
    calculateTimelineDistances,
    cleanAndSnapGPSPoints,
    filterValidGPSPoints,
    removeDuplicateGPSPoints,
    calculateDistance
} from '../utils';

export const useGPSData = (agentId) => {
    // State
    const [gpsPoints, setGpsPoints] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [agentProfile, setAgentProfile] = useState(null);
    const [timelineEvents, setTimelineEvents] = useState([]);
    const [statusMarkers, setStatusMarkers] = useState([]);
    const [deliveryMarkers, setDeliveryMarkers] = useState([]);
    const [deliveryRoutes, setDeliveryRoutes] = useState([]);
    const [batteryLevel, setBatteryLevel] = useState(null);
    const [deviceId, setDeviceId] = useState(null);
    const [stats, setStats] = useState({ distance: '0', unit: 'km' });
    const [distanceCalculations, setDistanceCalculations] = useState([]);
    const [lastKnownLocation, setLastKnownLocation] = useState(null);
    const [isMapLoading, setIsMapLoading] = useState(true);
    const [currentDate, setCurrentDate] = useState('today'); // Track current date for filtering

    // Refs for performance tracking
    const loadingStartTime = useRef(null);

    /**
     * Special filtering for today's real-time GPS data to remove unwanted straight lines
     */
    const filterTodaysGPSData = useCallback((coordinates) => {
        if (!coordinates || coordinates.length < 3) return coordinates;

        const filtered = [coordinates[0]]; // Always keep first point

        for (let i = 1; i < coordinates.length - 1; i++) {
            const prev = coordinates[i - 1];
            const current = coordinates[i];
            const next = coordinates[i + 1];

            // Calculate distances in meters
            const distToPrev = calculateDistance(prev, current) * 1000;
            const distToNext = calculateDistance(current, next) * 1000;

            // More aggressive filtering for today's data
            const maxJumpDistance = 300; // Reduced from 500m for today's data
            const maxSpeed = 60; // km/h - more conservative for real-time data
            const timeGap = 3; // seconds
            const maxDistancePerPoint = (maxSpeed * 1000 / 3600) * timeGap;

            // Keep point if it passes all checks
            if (distToPrev < maxJumpDistance &&
                distToNext < maxJumpDistance &&
                distToPrev < maxDistancePerPoint) {
                filtered.push(current);
            }
        }

        filtered.push(coordinates[coordinates.length - 1]); // Always keep last point
        return filtered;
    }, []);

    /**
     * Fetch tracking data for a specific date
     */
    const fetchTrackingData = useCallback(async (date, distanceUnit = 'km') => {
        loadingStartTime.current = performance.now();
        setLoading(true);
        setIsMapLoading(true);
        setError(null);

        try {
            // Always clear existing data before fetching to prevent mixing
            // console.log('🧹 Pre-fetch clear: Ensuring clean state before new data load');
            setGpsPoints([]);
            setTimelineEvents([]);
            setAgentProfile(null);
            setCurrentDate(date); // Track current date for GPS filtering

            // Fetch tracking data
            const data = await TrackingDataService.fetchTrackingData(agentId, date);

            // Set agent profile
            setAgentProfile(data.agent_profile);

            // Set GPS points
            const gpsPointsData = data.gpsPoints || [];
            setGpsPoints(gpsPointsData);

            // Set status markers
            setStatusMarkers(data.statusMarkers);

            // Set timeline events immediately (without geocoding for fast loading)
            setTimelineEvents(data.timelineEvents || []);

            // Start smart background geocoding after map loads
            if (data.timelineEvents && data.timelineEvents.length > 0) {
                startSmartGeocoding(data.timelineEvents);
            }

            // Set battery level and device ID
            setBatteryLevel(data.battery_level);
            setDeviceId(data.device_id);

            // Calculate distance from GPS points
            const distanceInKm = calculateTotalDistanceFromGpsPoints(data.gpsPoints);
            const distance = distanceUnit === 'miles' ? distanceInKm * 0.621371 : distanceInKm;

            setStats({
                distance: distance.toFixed(1),
                unit: distanceUnit
            });

            // Calculate timeline distances (using initial timeline events)
            if (data.timelineEvents && data.timelineEvents.length > 1) {
                const calculatedDistances = calculateTimelineDistances(data.timelineEvents);
                setDistanceCalculations(calculatedDistances);
                console.log(`📊 Calculated ${calculatedDistances.length} sequential distances between timeline events`);
            }

            // Fetch deliveries for this date
            await fetchDeliveries(date);

            console.log(`✅ Successfully loaded tracking data for ${date}`);

        } catch (err) {
            console.error('❌ Error fetching tracking data:', err);
            setError(err.message);

            // Clear timeline events as empty array (don't set to null/undefined)
            setTimelineEvents([]);
            console.log('🔍 SET TIMELINE EVENTS TO EMPTY ARRAY FOR ERROR');

            // Try to fetch last known location
            try {
                const location = await TrackingDataService.fetchLastKnownLocationFromPreviousDates(date, agentId);
                if (location) {
                    setLastKnownLocation(location);
                    console.log('⚡ BACKGROUND SUCCESS: Found last known location');
                }
            } catch (locationError) {
                console.error('⚡ BACKGROUND ERROR:', locationError);
            }

        } finally {
            setLoading(false);
            setIsMapLoading(false);

            // Performance tracking
            if (loadingStartTime.current) {
                const loadTime = Math.round(performance.now() - loadingStartTime.current);
                console.log(`⚡ DATA LOAD SPEED: Completed in ${loadTime}ms`);
            }
        }
    }, [agentId]);

    /**
     * Smart background geocoding - geocodes timeline events progressively
     * Only geocodes events that don't already have cached names from database
     * @param {Array} events - Timeline events to geocode
     */
    const startSmartGeocoding = useCallback(async (events) => {
        // Count events that need geocoding (no cached address)
        const eventsNeedingGeocoding = events.filter(event =>
            !event.address && event.latitude && event.longitude
        );

        if (eventsNeedingGeocoding.length === 0) {
            console.log('🎯 All events already have cached geocoded names - no API calls needed!');
            return;
        }

        console.log(`🧠 Starting smart geocoding for ${eventsNeedingGeocoding.length}/${events.length} events (others cached)`);

        // Add a small delay to let the map load first
        await new Promise(resolve => setTimeout(resolve, 500));

        const geocodedEvents = [...events]; // Copy original events
        let hasUpdates = false;

        for (let i = 0; i < events.length; i++) {
            const event = events[i];

            try {
                // Skip if already has cached address from database or no coordinates
                if (event.address || !event.latitude || !event.longitude) {
                    continue;
                }

                console.log(`🌍 Geocoding uncached event ${i + 1}/${events.length}:`, event.event_type);

                // Create address query from coordinates for reverse geocoding
                const addressQuery = `${event.latitude},${event.longitude}`;

                // Import geocoding functions dynamically to avoid circular imports
                const { geocodeAddress, saveGeocodedName } = await import('../utils/geocoding');
                const geocoded = await geocodeAddress(addressQuery);

                if (geocoded && geocoded.display_name) {
                    geocodedEvents[i] = {
                        ...event,
                        address: geocoded.display_name,
                        geocoded_source: geocoded.source
                    };
                    hasUpdates = true;

                    // Save geocoded name to database for future use
                    if (event.id) {
                        const saved = await saveGeocodedName(event.id, geocoded.display_name);
                        if (saved) {
                            console.log(`💾 Cached geocoded name for future use: ${geocoded.display_name}`);
                        }
                    }

                    // Update timeline immediately with this single geocoded event
                    setTimelineEvents([...geocodedEvents]);
                    console.log(`✅ Geocoded and cached event ${i + 1}: ${geocoded.display_name}`);
                } else {
                    // Fallback to coordinates
                    geocodedEvents[i] = {
                        ...event,
                        address: `${event.latitude.toFixed(6)}, ${event.longitude.toFixed(6)}`,
                        geocoded_source: 'coordinates'
                    };
                }

                // Small delay between requests to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 200));

            } catch (error) {
                console.error(`❌ Smart geocoding failed for event ${i + 1}:`, error);
                // Keep original event with coordinates as fallback
                geocodedEvents[i] = {
                    ...event,
                    address: event.latitude && event.longitude
                        ? `${event.latitude.toFixed(6)}, ${event.longitude.toFixed(6)}`
                        : 'Location unavailable',
                    geocoded_source: 'error'
                };
            }
        }

        // Final update if there were any changes
        if (hasUpdates) {
            setTimelineEvents(geocodedEvents);
            console.log(`✅ Smart geocoding completed - ${eventsNeedingGeocoding.length} new locations cached`);
        }

    }, []);

    /**
     * Fetch deliveries for a specific agent and date
     */
    const fetchDeliveries = useCallback(async (date) => {
        try {
            console.log(`🚚 Fetching deliveries for agent ${agentId} on date: ${date}`);
            const deliveries = await DeliveryService.fetchDeliveries(agentId, date);
            setDeliveryMarkers(deliveries);

            // Extract routes from deliveries
            console.log('🔍 Checking deliveries for route data:', deliveries);

            const routes = deliveries
                .filter(delivery => {
                    const hasRoute = delivery.route && delivery.route.coordinates;
                    console.log(`📦 Delivery ${delivery.id}: has route = ${hasRoute}`, delivery.route);
                    return hasRoute;
                })
                .map(delivery => ({
                    id: delivery.id,
                    route: delivery.route,
                    delivery: delivery
                }));

            setDeliveryRoutes(routes);
            console.log(`✅ Loaded ${deliveries.length} deliveries and ${routes.length} routes for agent ${agentId} on ${date}`);

            if (routes.length === 0) {
                console.log('⚠️ No routes found in deliveries. Routes will not be drawn.');
            }
        } catch (error) {
            console.error(`❌ Error fetching deliveries for agent ${agentId} on ${date}:`, error);
            setDeliveryMarkers([]);
            setDeliveryRoutes([]);
        }
    }, [agentId]);

    /**
     * Draw delivery routes on map
     */
    const drawGPSLine = useCallback((mapInstance) => {
        if (!mapInstance || !gpsPoints || gpsPoints.length === 0) {
            return;
        }

        try {

            // Set the map instance on the MapService singleton
            MapService.setMapInstance(mapInstance);

            // Convert GPS points to coordinate format [lng, lat]
            const rawCoordinates = gpsPoints.map(point => {
                // Handle different GPS point formats
                if (Array.isArray(point) && point.length >= 2) {
                    // Already in [lng, lat] format
                    return [parseFloat(point[0]), parseFloat(point[1])];
                } else if (point && typeof point === 'object' && point.longitude && point.latitude) {
                    // Object format with longitude/latitude properties
                    return [parseFloat(point.longitude), parseFloat(point.latitude)];
                } else {
                    return null;
                }
            }).filter(coord => coord && !isNaN(coord[0]) && !isNaN(coord[1]));

            if (rawCoordinates.length > 0) {
                // Apply GPS cleaning to remove unwanted straight lines and noise
                const isToday = currentDate === 'today' || currentDate === 'Today';

                // Step 1: Filter valid GPS points
                const validCoordinates = filterValidGPSPoints(rawCoordinates);

                // Step 2: Remove duplicate points (more aggressive for today's data)
                const duplicateThreshold = isToday ? 15 : 10;
                const deduplicatedCoordinates = removeDuplicateGPSPoints(validCoordinates, duplicateThreshold);

                // Step 3: Apply additional filtering for today's real-time data
                let processedCoordinates = deduplicatedCoordinates;
                if (isToday && deduplicatedCoordinates.length > 2) {
                    processedCoordinates = filterTodaysGPSData(deduplicatedCoordinates);
                }

                // Step 4: Clean and snap GPS points to remove noise and unwanted straight lines
                const cleanedCoordinates = cleanAndSnapGPSPoints(processedCoordinates);

                if (cleanedCoordinates.length > 0) {
                    MapService.drawGPSLine(cleanedCoordinates);
                }
            }
        } catch (error) {
            console.error('❌ Error drawing GPS line:', error);
        }
    }, [gpsPoints, currentDate, filterTodaysGPSData]);



    const drawDeliveryRoutes = useCallback((mapInstance, showRoutes = true) => {
        // console.log(`🛣️ drawDeliveryRoutes called: mapInstance=${!!mapInstance}, showRoutes=${showRoutes}, deliveryRoutes.length=${deliveryRoutes.length}`);

        if (!mapInstance || !showRoutes) {
            // console.log('⚠️ Skipping route drawing: map not ready or routes disabled');
            return;
        }

        try {
            console.log(`🛣️ Drawing ${deliveryRoutes.length} delivery routes on map`);

            // Set the map instance on the MapService singleton
            MapService.setMapInstance(mapInstance);

            // Clear existing routes first
            MapService.clearRoutes();

            // Draw each route
            deliveryRoutes.forEach((routeData, index) => {
                try {
                    console.log(`🔍 Drawing route ${index + 1}:`, routeData);

                    const routeOptions = {
                        color: '#2196F3',
                        width: 4,
                        opacity: 0.8
                    };

                    const drawnRoute = MapService.drawRoute(routeData.route, routeData.delivery, routeOptions);
                    if (drawnRoute) {
                        console.log(`✅ Drew route ${index + 1}/${deliveryRoutes.length} for delivery ${routeData.delivery.id}`);
                    } else {
                        console.log(`❌ Failed to draw route ${index + 1} for delivery ${routeData.delivery.id}`);
                    }
                } catch (error) {
                    console.error(`❌ Error drawing route ${index}:`, error);
                }
            });

            console.log(`✅ Finished drawing ${deliveryRoutes.length} delivery routes`);
        } catch (error) {
            console.error('❌ Error drawing delivery routes:', error);
        }
    }, [deliveryRoutes]);

    /**
     * Fetch last known location
     */
    const fetchLastKnownLocation = useCallback(async () => {
        try {
            const location = await TrackingDataService.fetchLastKnownLocation(agentId);
            setLastKnownLocation(location);
            return location;
        } catch (error) {
            console.error('❌ Error fetching last known location:', error);
            return null;
        }
    }, [agentId]);

    /**
     * Clear all data
     */
    const clearData = useCallback(() => {
        setGpsPoints([]);
        setTimelineEvents([]);
        setStatusMarkers([]);
        setDeliveryMarkers([]);
        setDeliveryRoutes([]);
        setAgentProfile(null);
        setBatteryLevel(null);
        setDeviceId(null);
        setStats({ distance: '0', unit: 'km' });
        setDistanceCalculations([]);
        setLastKnownLocation(null);
        setError(null);

        // Clear routes from map
        try {
            // We need a map instance to clear routes, but it might not be available during cleanup
            // The routes will be cleared when new routes are drawn anyway
            console.log('🧹 Routes will be cleared on next route draw');
        } catch (error) {
            console.log('⚠️ Error clearing routes from map:', error);
        }

        console.log('🧹 GPS data and routes cleared');
    }, []);

    /**
     * Update distance unit and recalculate stats
     */
    const updateDistanceUnit = useCallback((newUnit) => {
        if (stats.distance !== '0') {
            const currentDistanceInMeters = stats.unit === 'km'
                ? parseFloat(stats.distance) * 1000
                : parseFloat(stats.distance) / 0.000621371;

            const newDistance = newUnit === 'miles' 
                ? currentDistanceInMeters * 0.000621371
                : currentDistanceInMeters / 1000;

            setStats({
                distance: newDistance.toFixed(1),
                unit: newUnit
            });
        }
    }, [stats]);

    return {
        // State
        gpsPoints,
        loading,
        error,
        agentProfile,
        timelineEvents,
        statusMarkers,
        deliveryMarkers,
        deliveryRoutes,
        batteryLevel,
        deviceId,
        stats,
        distanceCalculations,
        lastKnownLocation,
        isMapLoading,

        // Actions
        fetchTrackingData,
        fetchDeliveries,
        fetchLastKnownLocation,
        clearData,
        updateDistanceUnit,
        drawGPSLine,
        drawDeliveryRoutes
    };
};

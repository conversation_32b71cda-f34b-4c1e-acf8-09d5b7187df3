/**
 * Timeline Event Component
 * 
 * Individual timeline event with selection and distance display
 */

import React from 'react';
import { formatTimestamp } from '../../utils';

const TimelineEvent = ({
    event,
    index,
    isSelected = false,
    onSelect,
    onNotesClick,
    onCenterClick,
    distanceToNext = null,
    customMarkerSettings = {}
}) => {
    const getEventIcon = (eventType) => {
        // Check if user has custom settings for this event type
        if (customMarkerSettings[eventType]) {
            if (customMarkerSettings[eventType].icon_image_url && customMarkerSettings[eventType].icon_image_url.trim() !== '') {
                // Return HTML for custom image
                const imageUrl = customMarkerSettings[eventType].icon_image_url;
                return `<img src="${imageUrl}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;" alt="Custom marker" />`;
            } else if (customMarkerSettings[eventType].icon_text && customMarkerSettings[eventType].icon_text.trim() !== '') {
                return customMarkerSettings[eventType].icon_text;
            }
        }

        // Default icons
        const icons = {
            'checked_in': '🟢',
            'checked_out': '🟡',
            'break_start': '⏸️',
            'break_end': '▶️',
            'delivered': '✅',
            'failed': '❌',
            'rescheduled': '📅'
        };
        return icons[eventType] || '📍';
    };

    const getEventColor = (eventType) => {
        // Check if user has custom settings for this event type
        if (customMarkerSettings[eventType]) {
            return customMarkerSettings[eventType].color;
        }

        // Default colors
        const colors = {
            'checked_in': '#4CAF50',
            'checked_out': '#FF9800',
            'break_start': '#9C27B0',
            'break_end': '#4CAF50',
            'delivered': '#4CAF50',
            'failed': '#F44336',
            'rescheduled': '#9C27B0'
        };
        return colors[eventType] || '#4285F4';
    };

    const getMarkerSize = (eventType) => {
        // Check if user has custom settings for this event type
        if (customMarkerSettings[eventType]) {
            return customMarkerSettings[eventType].size || 28;
        }
        return 28; // Default size
    };

    const getBorderSettings = (eventType) => {
        // Check if user has custom settings for this event type
        if (customMarkerSettings[eventType]) {
            return {
                color: customMarkerSettings[eventType].border_color || '#FFFFFF',
                width: customMarkerSettings[eventType].border_width || 2
            };
        }
        return { color: '#FFFFFF', width: 2 }; // Default border
    };

    const getEventTitle = (eventType) => {
        const titles = {
            'checked_in': 'Checked In',
            'checked_out': 'Checked Out',
            'break_start': 'Break Started',
            'break_end': 'Break Ended',
            'delivered': 'Delivery Completed',
            'failed': 'Delivery Failed',
            'rescheduled': 'Delivery Rescheduled'
        };
        return titles[eventType] || 'Timeline Event';
    };

    const eventType = event.event_type || event.status || event.type || 'unknown';
    const eventIcon = getEventIcon(eventType);
    const eventColor = getEventColor(eventType);
    const markerSize = getMarkerSize(eventType);
    const borderSettings = getBorderSettings(eventType);
    const eventTitle = getEventTitle(eventType);
    const locationName = event.address || 'Location not available';
    const eventTime = formatTimestamp(event.recorded_at);

    console.log(`🎨 Timeline event ${index} (${eventType}):`, {
        customSettings: customMarkerSettings[eventType],
        icon: eventIcon,
        color: eventColor,
        size: markerSize,
        border: borderSettings
    });

    return (
        <div className={`timeline-event ${isSelected ? 'selected' : ''}`}>
            {/* Event Marker */}
            <div
                className="timeline-marker"
                onClick={onSelect}
                style={{
                    backgroundColor: isSelected ? '#FFD700' : eventColor,
                    width: `${markerSize}px`,
                    height: `${markerSize}px`,
                    border: `${borderSettings.width}px solid ${borderSettings.color}`,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: `${Math.max(10, markerSize * 0.4)}px`,
                    cursor: 'pointer',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}
                dangerouslySetInnerHTML={{ __html: eventIcon }}
            >
            </div>

            {/* Event Content */}
            <div className="timeline-event-content">
                <div className="timeline-event-header">
                    <div className="timeline-event-title">
                        {eventTitle}
                    </div>
                    <div className="timeline-event-time">
                        {eventTime}
                    </div>
                </div>

                <div className="timeline-event-location">
                    📍 {locationName}
                    {!event.address && event.latitude && event.longitude && (
                        <span className="geocoding-indicator" title="Loading location name from API...">
                            🌍
                        </span>
                    )}
                    {event.address && event.geocoded_source && (
                        <span className="cached-indicator" title="Location name loaded from database cache">
                            💾
                        </span>
                    )}
                </div>

                {/* Event Actions */}
                <div className="timeline-event-actions">
                    <button
                        className="timeline-notes-button"
                        onClick={onNotesClick}
                        title="View notes"
                    >
                        📝 Notes
                    </button>

                    <button
                        className="timeline-center-button"
                        onClick={() => onCenterClick && onCenterClick(event)}
                        title="Center map on this location"
                        style={{
                            backgroundColor: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '4px 8px',
                            fontSize: '11px',
                            cursor: 'pointer',
                            marginRight: '4px'
                        }}
                    >
                        🎯 Center
                    </button>

                    <button
                        className="timeline-select-button"
                        onClick={onSelect}
                        title="Select for distance calculation"
                        style={{
                            backgroundColor: isSelected ? '#FFD700' : '#f0f0f0',
                            color: isSelected ? '#333' : '#666'
                        }}
                    >
                        {isSelected ? '✓ Selected' : 'Select'}
                    </button>
                </div>

                {/* Distance to Next Event */}
                {distanceToNext && (
                    <div className="timeline-distance">
                        📏 {distanceToNext.distance.toFixed(2)} {distanceToNext.unit} to next event
                    </div>
                )}
            </div>
        </div>
    );
};

export default TimelineEvent;

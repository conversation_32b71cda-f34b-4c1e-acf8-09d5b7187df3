/* Timeline Styles */

.timeline-container {
    position: absolute;
    right: 20px;
    top: 420px;
    width: 380px;
    max-height: calc(100vh - 440px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.08);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

/* Timeline content when integrated into controls */
.timeline-content {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
}

.timeline-header {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(248, 249, 250, 0.8);
    border-radius: 16px 16px 0 0;
    backdrop-filter: blur(8px);
}

.timeline-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.timeline-duration {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.timeline-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    gap: 12px;
    color: #666;
}

.timeline-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4285F4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.direct-distance-display {
    padding: 12px 20px;
    background: #e3f2fd;
    border-bottom: 1px solid #e0e0e0;
}

.direct-distance-header {
    font-size: 12px;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 4px;
}

.direct-distance-value {
    font-size: 18px;
    font-weight: bold;
    color: #1976d2;
}

.direct-distance-time {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.segment-highlight-info {
    font-size: 12px;
    color: #1976D2;
    margin-top: 8px;
    padding: 6px 12px;
    background: rgba(25, 118, 210, 0.1);
    border-radius: 4px;
    border-left: 3px solid #1976D2;
}

.timeline-selection-hint {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    margin: 16px 0;
    font-size: 14px;
    color: #2E7D32;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.timeline-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.timeline-content::-webkit-scrollbar {
    width: 6px;
}

.timeline-content::-webkit-scrollbar-track {
    background: transparent;
}

.timeline-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.timeline-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.timeline-line {
    position: absolute;
    left: 50px;
    top: 20px;
    bottom: 20px;
    width: 2px;
    background-color: #e0e0e0;
    z-index: 1;
}

.timeline-event {
    position: relative;
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    z-index: 2;
}

.timeline-event.selected {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
    padding: 8px;
    margin: -8px -8px 16px -8px;
}

.timeline-marker {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
    cursor: pointer;
    transition: all 0.2s;
}

.timeline-marker:hover {
    transform: scale(1.1);
}

.timeline-event-content {
    flex: 1;
    margin-left: 16px;
}

.timeline-event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
}

.timeline-event-title {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.timeline-event-time {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.timeline-event-location {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Geocoding indicator */
.geocoding-indicator {
    font-size: 0.8rem;
    opacity: 0.7;
    animation: pulse 2s infinite;
}

/* Cached indicator */
.cached-indicator {
    font-size: 0.7rem;
    opacity: 0.6;
    color: #28a745;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

.timeline-event-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.timeline-notes-button,
.timeline-select-button {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f0f0f0;
    color: #666;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
}

.timeline-notes-button:hover,
.timeline-select-button:hover {
    background: #e0e0e0;
}

.timeline-center-button {
    padding: 4px 8px !important;
    border: 1px solid #007bff !important;
    border-radius: 4px !important;
    background: #007bff !important;
    color: white !important;
    font-size: 11px !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 2px !important;
    margin-right: 4px !important;
}

.timeline-center-button:hover {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3) !important;
}

.timeline-distance {
    font-size: 11px;
    color: #4285F4;
    font-weight: 500;
}

.timeline-no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.no-data-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-data-message {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
}

.no-data-suggestion {
    font-size: 14px;
    color: #999;
    line-height: 1.4;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/**
 * Timeline Component
 * 
 * Displays timeline events with distance calculations and selection
 */

import React from 'react';
import TimelineEvent from './TimelineEvent';
import './Timeline.css';
import { calculateDuration } from '../../utils';

const Timeline = ({
    timelineEvents = [],
    selectedPoints = [],
    onPointSelect,
    onNotesClick,
    onCenterClick,
    distanceCalculations = [],
    directDistance = null,
    loading = false,
    customMarkerSettings = {},
    isIntegrated = false
}) => {
    // Calculate total duration if we have events
    const getTotalDuration = () => {
        if (timelineEvents && timelineEvents.length >= 2) {
            const firstEvent = timelineEvents[0];
            const lastEvent = timelineEvents[timelineEvents.length - 1];
            return calculateDuration(firstEvent.recorded_at, lastEvent.recorded_at);
        }
        return null;
    };

    const totalDuration = getTotalDuration();

    if (loading) {
        return (
            <div className={isIntegrated ? "timeline-content" : "timeline-container"}>
                {!isIntegrated && (
                    <div className="timeline-header">
                        <h3>📍 Timeline</h3>
                    </div>
                )}
                <div className="timeline-loading">
                    <div className="timeline-spinner"></div>
                    <span>Loading timeline...</span>
                </div>
            </div>
        );
    }

    return (
        <div className={isIntegrated ? "timeline-content" : "timeline-container"}>
            {/* Timeline Header - Only show when not integrated */}
            {!isIntegrated && (
                <div className="timeline-header">
                    <h3>📍 Timeline</h3>
                    {totalDuration && (
                        <div className="timeline-duration">
                            ⏱️ Total: {totalDuration}
                        </div>
                    )}
                </div>
            )}



            {/* Direct Distance Display */}
            {directDistance && selectedPoints.length === 2 && (
                <div className="direct-distance-display">
                    <div className="direct-distance-header">
                        📏 Direct Distance
                    </div>
                    <div className="direct-distance-value">
                        {directDistance.distance.toFixed(2)} {directDistance.unit}
                    </div>
                    <div className="direct-distance-time">
                        ⏱️ {(() => {
                            const fromEvent = timelineEvents[selectedPoints[0]];
                            const toEvent = timelineEvents[selectedPoints[1]];
                            return calculateDuration(fromEvent.recorded_at, toEvent.recorded_at);
                        })()}
                    </div>
                    <div className="segment-highlight-info">
                        🎯 <strong>Route segment highlighted on map</strong>
                    </div>
                </div>
            )}



            {/* Timeline Events */}
            <div className="timeline-content">
                {timelineEvents && timelineEvents.length > 0 ? (
                    <>
                        {/* Timeline Line */}
                        <div className="timeline-line"></div>
                        
                        {/* Timeline Events */}
                        {timelineEvents.map((event, index) => (
                            <TimelineEvent
                                key={event.id || index}
                                event={event}
                                index={index}
                                isSelected={selectedPoints.includes(index)}
                                onSelect={() => onPointSelect(index)}
                                onNotesClick={() => onNotesClick(event)}
                                onCenterClick={onCenterClick}
                                distanceToNext={distanceCalculations.find(d => d.fromIndex === index)}
                                customMarkerSettings={customMarkerSettings}
                            />
                        ))}
                    </>
                ) : (
                    <div className="timeline-no-data">
                        <div className="no-data-icon">📍</div>
                        <div className="no-data-message">
                            No timeline events found for this date
                        </div>
                        <div className="no-data-suggestion">
                            Try selecting a different date or check if the agent was active
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Timeline;

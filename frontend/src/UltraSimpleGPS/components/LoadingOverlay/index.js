/**
 * Loading Overlay Component
 * 
 * Displays loading state with spinner and message
 */

import React from 'react';
import './LoadingOverlay.css';

const LoadingOverlay = ({ 
    isVisible = false, 
    message = 'Loading GPS data...', 
    showSpinner = true 
}) => {
    if (!isVisible) return null;

    return (
        <div className="loading-overlay">
            <div className="loading-content">
                {showSpinner && (
                    <div className="loading-spinner">
                        <div className="spinner"></div>
                    </div>
                )}
                <div className="loading-message">
                    {message}
                </div>
            </div>
        </div>
    );
};

export default LoadingOverlay;

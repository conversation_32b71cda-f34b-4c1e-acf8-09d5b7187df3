/* UI Layout Improvements for Better Visual Hierarchy */

/* Main container improvements */
.ultra-simple-gps {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, "Helvetica Neue", Arial, sans-serif;
}

/* Map container with proper anchoring */
.map-container {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #f5f5f5;
}

/* UI Panel Base Styles */
.ui-panel {
    position: absolute;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

/* Left Panel (Controls) */
.left-panel {
    top: 20px;
    left: 20px;
    max-width: 420px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

/* Right Panel (Timeline) */
.right-panel {
    top: 20px;
    right: 20px;
    width: 380px;
    max-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
}

/* Top Panel (Date Navigation) */
.top-panel {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 300px;
}

/* Panel Section Styling */
.panel-section {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section-title {
    font-size: 10px;
    font-weight: 600;
    color: #555;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: flex;
    align-items: center;
    gap: 3px;
}

/* Button Grid Layouts */
.button-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    align-items: stretch;
}

.button-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
    align-items: stretch;
}

/* Enhanced Button Styles */
.ui-button {
    padding: 4px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    border: 1px solid;
    text-decoration: none;
    outline: none;
    min-height: 24px;
    box-sizing: border-box;
}

.ui-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ui-button:active {
    transform: translateY(0);
}

/* Button Variants */
.ui-button.primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.ui-button.danger {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.ui-button.secondary {
    background: #6f42c1;
    border-color: #6f42c1;
    color: white;
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
}

.ui-button.outline {
    background: white;
    border-color: #e0e0e0;
    color: #666;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ui-button.success {
    background: #28a745;
    border-color: #28a745;
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.ui-button.warning {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* Stats Display */
.stats-display {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .left-panel {
        max-width: 360px;
    }
    
    .right-panel {
        width: 320px;
    }
}

@media (max-width: 768px) {
    .left-panel,
    .right-panel {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        width: 100%;
        max-width: none;
        margin: 10px;
        max-height: none;
    }
    
    .ultra-simple-gps {
        flex-direction: column;
    }
    
    .map-container {
        height: 60vh;
    }
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Scrollbar Styling */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

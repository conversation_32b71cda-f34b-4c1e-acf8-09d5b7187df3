/**
 * Map Container Component
 * 
 * Container for the map with loading overlay
 */

import React from 'react';
import LoadingOverlay from '../LoadingOverlay';
import './MapContainer.css';

const MapContainer = ({ 
    mapRef, 
    isMapLoading = false, 
    loadingMessage = 'Loading map...',
    children 
}) => {
    return (
        <div className="map-container">
            {/* Map Element */}
            <div 
                ref={mapRef}
                className="map-element"
                style={{
                    width: '100%',
                    height: '100%',
                    position: 'relative'
                }}
            />
            
            {/* Loading Overlay */}
            <LoadingOverlay 
                isVisible={isMapLoading}
                message={loadingMessage}
            />
            
            {/* Additional children (controls, etc.) */}
            {children}
        </div>
    );
};

export default MapContainer;

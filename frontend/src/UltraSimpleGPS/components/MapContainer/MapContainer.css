/* Map Container Styles */

.map-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.map-element {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #f5f5f5;
}

/* Ensure map controls are properly positioned */
.map-container .mapboxgl-control-container {
    position: absolute;
}

.map-container .mapboxgl-ctrl-top-left {
    top: 10px;
    left: 10px;
}

.map-container .mapboxgl-ctrl-top-right {
    top: 10px;
    right: 10px;
}

.map-container .mapboxgl-ctrl-bottom-left {
    bottom: 10px;
    left: 10px;
}

.map-container .mapboxgl-ctrl-bottom-right {
    bottom: 10px;
    right: 10px;
}

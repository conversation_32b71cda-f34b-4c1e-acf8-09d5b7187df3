/**
 * Date Navigation Component
 * 
 * Handles date navigation with previous/next buttons and current date display
 */

import React from 'react';
import { formatDateForDisplayWithFallback } from '../../utils';

const DateNavigation = ({ currentDate, onNavigate, isChanging = false }) => {
    return (
        <div style={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            border: '1px solid #e0e0e0',
            overflow: 'hidden'
        }}>
            {/* Previous Date Button */}
            <button
                onClick={() => onNavigate('prev')}
                disabled={isChanging}
                style={{
                    padding: '12px 16px',
                    backgroundColor: isChanging ? '#f5f5f5' : 'white',
                    border: 'none',
                    cursor: isChanging ? 'not-allowed' : 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: isChanging ? '#999' : '#333',
                    borderRight: '1px solid #e0e0e0',
                    transition: 'background-color 0.2s'
                }}
                onMouseOver={(e) => {
                    if (!isChanging) {
                        e.target.style.backgroundColor = '#f0f0f0';
                    }
                }}
                onMouseOut={(e) => {
                    if (!isChanging) {
                        e.target.style.backgroundColor = 'white';
                    }
                }}
                title="Previous day"
            >
                ← Prev
            </button>

            {/* Current Date Display */}
            <div style={{
                padding: '12px 20px',
                fontSize: '14px',
                fontWeight: '600',
                color: '#333',
                minWidth: '120px',
                textAlign: 'center',
                backgroundColor: isChanging ? '#f9f9f9' : 'white',
                transition: 'background-color 0.2s'
            }}>
                {isChanging ? (
                    <span style={{ color: '#666' }}>
                        🔄 Loading...
                    </span>
                ) : (
                    formatDateForDisplayWithFallback(currentDate)
                )}
            </div>

            {/* Next Date Button */}
            <button
                onClick={() => onNavigate('next')}
                disabled={isChanging}
                style={{
                    padding: '12px 16px',
                    backgroundColor: isChanging ? '#f5f5f5' : 'white',
                    border: 'none',
                    cursor: isChanging ? 'not-allowed' : 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: isChanging ? '#999' : '#333',
                    borderLeft: '1px solid #e0e0e0',
                    transition: 'background-color 0.2s'
                }}
                onMouseOver={(e) => {
                    if (!isChanging) {
                        e.target.style.backgroundColor = '#f0f0f0';
                    }
                }}
                onMouseOut={(e) => {
                    if (!isChanging) {
                        e.target.style.backgroundColor = 'white';
                    }
                }}
                title="Next day"
            >
                Next →
            </button>
        </div>
    );
};

export default DateNavigation;

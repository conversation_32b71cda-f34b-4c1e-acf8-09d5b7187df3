/**
 * Route Toggle Component
 * 
 * Toggle route visibility on/off for cost optimization
 */

import React from 'react';

const RouteToggle = ({ showRoutes, onToggle }) => {
    return (
        <button
            onClick={onToggle}
            className={`ui-button ${showRoutes ? 'warning' : 'outline'}`}
            style={{
                position: 'relative',
                width: '100%'
            }}
            title={`${showRoutes ? 'Hide' : 'Show'} delivery routes (cost optimization)`}
        >
            {showRoutes ? '🛣️ Routes ON' : '🛣️ Routes OFF'}
            
            {/* Cost saving indicator when routes are OFF */}
            {!showRoutes && (
                <span style={{
                    position: 'absolute',
                    top: '-8px',
                    right: '-8px',
                    width: '16px',
                    height: '16px',
                    backgroundColor: '#4CAF50',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '8px',
                    color: 'white',
                    fontWeight: 'bold'
                }}>
                    💰
                </span>
            )}
        </button>
    );
};

export default RouteToggle;

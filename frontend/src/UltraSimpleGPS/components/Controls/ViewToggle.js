/**
 * View Toggle Component
 * 
 * Toggle between satellite and standard map view
 */

import React from 'react';

const ViewToggle = ({ isSatelliteView, onToggle, isHighZoomFallback = false }) => {
    return (
        <button
            onClick={onToggle}
            className={`ui-button ${isSatelliteView ? 'success' : 'outline'}`}
            style={{
                position: 'relative',
                width: '100%'
            }}
            title={`Switch to ${isSatelliteView ? 'standard' : 'satellite'} view`}
        >
            {isSatelliteView ? '🛰️ Satellite' : '🗺️ Standard'}
            
            {/* High zoom feedback indicator */}
            {isHighZoomFallback && (
                <span style={{
                    position: 'absolute',
                    top: '-8px',
                    right: '-8px',
                    width: '16px',
                    height: '16px',
                    backgroundColor: '#FF9800',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '8px',
                    color: 'white',
                    fontWeight: 'bold'
                }}>
                    !
                </span>
            )}
        </button>
    );
};

export default ViewToggle;

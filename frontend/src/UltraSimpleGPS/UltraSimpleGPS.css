/* UltraSimpleGPS Main Styles */
.ultra-simple-gps {
  width: 100%;
  height: 100vh;
  display: flex;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Map Container */
.map-container {
  flex: 1;
  position: relative;
}

.map-element {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* Sidebar */
.sidebar {
  width: 375px;
  background-color: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  margin-right: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.user-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-indicator.active {
  background-color: #00aa44;
}

.status-indicator.inactive {
  background-color: #ff4444;
}

.user-id {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.battery-info {
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.device-info {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: flex;
  align-items: center;
}

/* Controls */
.controls {
  display: flex;
  gap: 8px;
}

.control-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.control-button:hover {
  background-color: #f5f5f5;
}

.control-button.active {
  background: #4285F4;
  border-color: #4285F4;
  color: white;
}

.control-button.satellite-fallback {
  background: #ff9900;
  border-color: #ff9900;
  color: white;
}

/* Date Navigation */
.date-navigation {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #666;
  padding: 8px;
}

.nav-button:hover {
  color: #333;
}

.date-display {
  display: flex;
  align-items: center;
  margin: 0 20px;
  font-weight: 600;
  color: #333;
}

.manual-controls {
  display: flex;
  gap: 5px;
  margin-left: 10px;
}

.manual-button {
  border: none;
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
}

.manual-button.refresh {
  background: #007bff;
}

.manual-button.customize {
  background: #667eea;
}

.manual-button.route-toggle {
  background: #4CAF50;
}

.manual-button.route-toggle.off {
  background: #f44336;
}

/* Stats */
.stats-container {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.stats-scroll {
  display: flex;
  overflow-x: auto;
  gap: 12px;
  padding-bottom: 8px;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.stat-card {
  min-width: 80px;
  text-align: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  flex-shrink: 0;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 10px;
  color: #6c757d;
  text-transform: uppercase;
}

.distance-toggle {
  padding: 2px 6px;
  font-size: 9px;
  background-color: #e9ecef;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #6c757d;
  margin-top: 4px;
}

/* Timeline */
.timeline-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  position: relative;
}

.timeline-line {
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 200px;
  width: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.timeline-event {
  position: relative;
  margin-bottom: 24px;
  padding-left: 60px;
}

.timeline-event:last-child {
  margin-bottom: 0;
}

.timeline-icon {
  position: absolute;
  left: 8px;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  font-size: 12px;
}

.timeline-content {
  background-color: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.timeline-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}

.battery-badge {
  font-size: 11px;
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 10px;
}

.timeline-address {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.timeline-reason {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
}

.timeline-device {
  font-size: 11px;
  color: #999;
  margin-bottom: 6px;
}

.timeline-notes {
  font-size: 12px;
  color: #666;
  background-color: #f8f9fa;
  padding: 6px 8px;
  border-radius: 4px;
  border-left: 3px solid #4285F4;
}

.notes-button {
  font-size: 11px;
  color: #666;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.notes-button:hover {
  background-color: #e9ecef;
}

/* Distance Calculator */
.distance-calculator {
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  position: relative;
  z-index: 20;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
  max-width: 100%;
  overflow: hidden;
}

.distance-calculator-title {
  font-size: 14px;
  font-weight: 600;
  color: #2e7d32;
  margin-bottom: 8px;
}

.distance-calculator-points {
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  word-wrap: break-word;
}

.distance-calculator-results {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.distance-result {
  flex: 1;
  font-size: 14px;
  font-weight: 700;
  color: #2e7d32;
  text-align: center;
  background-color: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e8f5e8;
}

.time-result {
  flex: 1;
  font-size: 14px;
  font-weight: 700;
  color: #1976d2;
  text-align: center;
  background-color: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e3f2fd;
}

.distance-calculator-close {
  background: white;
  border: 1px solid #4caf50;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 11px;
  color: #2e7d32;
  cursor: pointer;
  margin-top: 8px;
  width: 100%;
  font-weight: 500;
  transition: all 0.2s ease;
}

.distance-calculator-close:hover {
  background-color: #f1f8e9;
}

/* No Data State */
.no-data-container {
  position: relative;
  padding-left: 60px;
  padding-top: 20px;
}

.no-data-icon {
  position: absolute;
  left: 11px;
  top: 20px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #ddd;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.no-data-card {
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
  text-align: center;
}

.no-data-emoji {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.4;
}

.no-data-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #666;
}

.no-data-message {
  font-size: 13px;
  margin-bottom: 12px;
  color: #888;
}

.no-data-hint {
  font-size: 11px;
  opacity: 0.7;
  background-color: #f8f9fa;
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid #e9ecef;
  display: inline-block;
}

/* Error State */
.error-message {
  padding: 10px;
  background-color: #ffcccc;
  color: #cc0000;
  font-size: 14px;
  border-radius: 4px;
  margin-bottom: 10px;
}

/* Marker Styles */
.custom-marker {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
}

.timeline-marker {
  width: 32px;
  height: 32px;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.start-marker {
  width: 40px;
  height: 40px;
  background-color: #00aa44;
  border: 3px solid white;
}

.end-marker {
  width: 48px;
  height: 48px;
  border: 4px solid white;
  box-shadow: 0 3px 12px rgba(0,0,0,0.4);
}

.status-marker {
  border: 3px solid white;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.last-known-marker {
  width: 36px;
  height: 36px;
  border: 3px solid white;
}

.delivery-pickup-marker {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.delivery-drop-marker {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Route Lines */
.delivery-route-line {
  position: absolute;
  pointer-events: none;
  z-index: 500;
}

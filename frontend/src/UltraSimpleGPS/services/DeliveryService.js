/**
 * Delivery Service
 * 
 * Service class for fetching delivery data and managing delivery-related operations
 */

import { API_ENDPOINTS } from '../constants';
import { getRouteWithOLA } from '../utils';

class DeliveryService {
    constructor() {
        this.deliveryCache = {};
    }

    /**
     * Fetch deliveries for a specific agent and date
     * @param {string} agentId - Agent UUID
     * @param {string} date - Date string
     * @returns {Promise<Array>} Array of deliveries
     */
    async fetchDeliveries(agentId, date) {
        // Convert relative dates to proper date format
        let targetDate;
        if (date === 'today') {
            targetDate = new Date().toISOString().split('T')[0];
        } else if (date === 'yesterday') {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            targetDate = yesterday.toISOString().split('T')[0];
        } else {
            targetDate = date;
        }

        try {

            console.log(`🚚 Fetching deliveries for agent ${agentId} on date: ${targetDate}`);

            // Create cache key with both agentId and date
            const cacheKey = `${agentId}-${targetDate}`;

            // Check cache first
            if (this.deliveryCache[cacheKey]) {
                console.log(`💰 DELIVERY CACHE HIT: Using cached deliveries for ${cacheKey}`);
                return this.deliveryCache[cacheKey];
            }

            const response = await fetch(API_ENDPOINTS.DELIVERY_BY_DATE(agentId, targetDate));
            
            if (!response.ok) {
                console.log(`⚠️ Deliveries API returned ${response.status} for agent ${agentId} on date ${targetDate}`);
                return [];
            }

            const data = await response.json();
            console.log(`📦 Deliveries API response for agent ${agentId} on ${targetDate}:`, data);

            let deliveries = [];
            if (data.success && data.data && data.data.deliveries && Array.isArray(data.data.deliveries)) {
                deliveries = data.data.deliveries;
            } else if (data.data && Array.isArray(data.data)) {
                deliveries = data.data;
            } else if (Array.isArray(data)) {
                deliveries = data;
            }

            console.log(`✅ Found ${deliveries.length} deliveries for agent ${agentId} on ${targetDate}`);

            // Process deliveries to add routes if needed
            const processedDeliveries = await this.processDeliveries(deliveries);

            // Cache the processed deliveries
            this.deliveryCache[cacheKey] = processedDeliveries;

            return processedDeliveries;

        } catch (error) {
            console.error(`❌ Error fetching deliveries for agent ${agentId} on ${targetDate}:`, error);
            return [];
        }
    }

    /**
     * Process deliveries to add route information
     * @param {Array} deliveries - Raw deliveries data
     * @returns {Promise<Array>} Processed deliveries with routes
     */
    async processDeliveries(deliveries) {
        console.log('🔍 Raw deliveries data:', deliveries);
        const processedDeliveries = [];

        for (const delivery of deliveries) {
            try {
                console.log('🔍 Processing delivery:', delivery);
                const processedDelivery = { ...delivery };

                // Add route between pickup and drop locations if both exist
                if (delivery.pickup_location && delivery.drop_location &&
                    delivery.pickup_location.latitude && delivery.pickup_location.longitude &&
                    delivery.drop_location.latitude && delivery.drop_location.longitude) {
                    
                    console.log(`🛣️ Getting route for delivery ${delivery.id}`);
                    
                    const startCoords = {
                        latitude: delivery.pickup_location.latitude,
                        longitude: delivery.pickup_location.longitude
                    };
                    
                    const endCoords = {
                        latitude: delivery.drop_location.latitude,
                        longitude: delivery.drop_location.longitude
                    };

                    try {
                        const route = await getRouteWithOLA(startCoords, endCoords);
                        if (route) {
                            processedDelivery.route = route;
                            console.log(`✅ Added route to delivery ${delivery.id}`);
                        }
                    } catch (routeError) {
                        console.log(`⚠️ Could not get route for delivery ${delivery.id}:`, routeError.message);

                        // Create a simple straight-line route for testing
                        console.log('🧪 Creating mock straight-line route for testing');
                        const mockRoute = {
                            coordinates: [
                                [startCoords.longitude, startCoords.latitude], // [longitude, latitude]
                                [endCoords.longitude, endCoords.latitude]      // [longitude, latitude]
                            ],
                            distance: 1000, // mock distance
                            duration: 300   // mock duration
                        };
                        processedDelivery.route = mockRoute;
                        console.log(`✅ Added mock route to delivery ${delivery.id}:`, mockRoute);
                    }
                }

                processedDeliveries.push(processedDelivery);

            } catch (error) {
                console.error(`❌ Error processing delivery ${delivery.id}:`, error);
                // Add delivery without route processing
                processedDeliveries.push(delivery);
            }
        }

        return processedDeliveries;
    }

    /**
     * Get delivery by ID
     * @param {string} deliveryId - Delivery ID
     * @returns {Promise<Object|null>} Delivery data or null
     */
    async getDeliveryById(deliveryId) {
        try {
            console.log(`🔍 Fetching delivery by ID: ${deliveryId}`);
            
            const response = await fetch(`${API_ENDPOINTS.DELIVERIES}/${deliveryId}`);
            
            if (!response.ok) {
                console.log(`⚠️ Delivery API returned ${response.status} for ID ${deliveryId}`);
                return null;
            }

            const data = await response.json();
            
            if (data.success && data.data) {
                return data.data;
            }
            
            return data;

        } catch (error) {
            console.error(`❌ Error fetching delivery ${deliveryId}:`, error);
            return null;
        }
    }

    /**
     * Update delivery status
     * @param {string} deliveryId - Delivery ID
     * @param {string} status - New status
     * @param {Object} additionalData - Additional data to update
     * @returns {Promise<boolean>} Success status
     */
    async updateDeliveryStatus(deliveryId, status, additionalData = {}) {
        try {
            console.log(`📝 Updating delivery ${deliveryId} status to: ${status}`);
            
            const response = await fetch(`${API_ENDPOINTS.DELIVERIES}/${deliveryId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    status,
                    ...additionalData,
                    updated_at: new Date().toISOString()
                })
            });

            if (!response.ok) {
                console.error(`❌ Failed to update delivery ${deliveryId}: ${response.status}`);
                return false;
            }

            const data = await response.json();
            console.log(`✅ Updated delivery ${deliveryId}:`, data);

            // Clear cache to force refresh
            this.clearCache();

            return true;

        } catch (error) {
            console.error(`❌ Error updating delivery ${deliveryId}:`, error);
            return false;
        }
    }

    /**
     * Create new delivery
     * @param {Object} deliveryData - Delivery data
     * @returns {Promise<Object|null>} Created delivery or null
     */
    async createDelivery(deliveryData) {
        try {
            console.log('📝 Creating new delivery:', deliveryData);
            
            const response = await fetch(API_ENDPOINTS.DELIVERIES, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...deliveryData,
                    created_at: new Date().toISOString()
                })
            });

            if (!response.ok) {
                console.error(`❌ Failed to create delivery: ${response.status}`);
                return null;
            }

            const data = await response.json();
            console.log('✅ Created delivery:', data);

            // Clear cache to force refresh
            this.clearCache();

            return data.success ? data.data : data;

        } catch (error) {
            console.error('❌ Error creating delivery:', error);
            return null;
        }
    }

    /**
     * Get deliveries by agent
     * @param {number} agentId - Agent ID
     * @param {string} date - Date string (optional)
     * @returns {Promise<Array>} Array of deliveries
     */
    async getDeliveriesByAgent(agentId, date = null) {
        try {
            console.log(`🔍 Fetching deliveries for agent ${agentId}${date ? ` on ${date}` : ''}`);
            
            let url = `${API_ENDPOINTS.DELIVERIES}?agent_id=${agentId}`;
            if (date) {
                url += `&date=${date}`;
            }

            const response = await fetch(url);
            
            if (!response.ok) {
                console.log(`⚠️ Deliveries API returned ${response.status} for agent ${agentId}`);
                return [];
            }

            const data = await response.json();
            
            let deliveries = [];
            if (data.success && data.data && Array.isArray(data.data)) {
                deliveries = data.data;
            } else if (Array.isArray(data)) {
                deliveries = data;
            }

            console.log(`✅ Found ${deliveries.length} deliveries for agent ${agentId}`);
            return deliveries;

        } catch (error) {
            console.error(`❌ Error fetching deliveries for agent ${agentId}:`, error);
            return [];
        }
    }

    /**
     * Get delivery statistics
     * @param {string} date - Date string
     * @param {number} agentId - Agent ID (optional)
     * @returns {Promise<Object>} Delivery statistics
     */
    async getDeliveryStats(date, agentId = null) {
        try {
            const deliveries = agentId 
                ? await this.getDeliveriesByAgent(agentId, date)
                : await this.fetchDeliveries(date);

            const stats = {
                total: deliveries.length,
                delivered: 0,
                failed: 0,
                in_progress: 0,
                pending: 0
            };

            deliveries.forEach(delivery => {
                switch (delivery.status) {
                    case 'delivered':
                        stats.delivered++;
                        break;
                    case 'failed':
                        stats.failed++;
                        break;
                    case 'in_transit':
                    case 'out_for_delivery':
                    case 'picked_up':
                        stats.in_progress++;
                        break;
                    default:
                        stats.pending++;
                }
            });

            console.log(`📊 Delivery stats for ${date}:`, stats);
            return stats;

        } catch (error) {
            console.error(`❌ Error calculating delivery stats for ${date}:`, error);
            return { total: 0, delivered: 0, failed: 0, in_progress: 0, pending: 0 };
        }
    }

    /**
     * Clear delivery cache
     */
    clearCache() {
        this.deliveryCache = {};
        console.log('🧹 Delivery cache cleared');
    }

    /**
     * Get cache size
     * @returns {number} Number of cached items
     */
    getCacheSize() {
        return Object.keys(this.deliveryCache).length;
    }
}

// Export singleton instance
export default new DeliveryService();

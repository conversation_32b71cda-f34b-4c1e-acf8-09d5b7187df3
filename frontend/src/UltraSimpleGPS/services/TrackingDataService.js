/**
 * Tracking Data Service
 * 
 * Service class for fetching tracking data, managing cache, and handling API calls
 */

import { API_ENDPOINTS, STORAGE_KEYS } from '../constants';
import { getTimezoneHeaders, validateAndNormalizeDate } from '../utils';

class TrackingDataService {
    constructor() {
        this.cache = {};
        this.loadingStartTime = null;
    }

    /**
     * Fetch tracking data for an agent on a specific date
     * @param {number} agentId - Agent ID
     * @param {string} date - Date string
     * @returns {Promise<Object>} Tracking data
     */
    async fetchTrackingData(agentId, date) {
        // Start performance tracking
        this.loadingStartTime = performance.now();
        
        // Validate and normalize date
        const targetDate = validateAndNormalizeDate(date);
        
        // Check cache first
        const cacheKey = `${agentId}-${targetDate}`;
        if (this.cache[cacheKey]) {
            // console.log(`💰 CACHE HIT: Using cached data for ${targetDate}, avoiding API call`);
            const cachedData = this.cache[cacheKey];

            // Log cache performance
            // const loadTime = Math.round(performance.now() - this.loadingStartTime);
            // console.log(`⚡ CACHE SPEED: Loaded in ${loadTime}ms (cached)`);

            return cachedData;
        }

        try {
            // console.log(`📡 Fetching GPS points for Agent ${agentId} on date: ${targetDate}...`);
            const apiUrl = API_ENDPOINTS.TRACKING_HISTORY(agentId) + `?date=${targetDate}`;
            // console.log(`📡 API URL: ${apiUrl}`);

            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseData = await response.json();
            console.log(`⚡ TRACKING HISTORY RESPONSE for ${targetDate}:`, responseData);

            // Handle new backend response format
            const data = responseData.success ? responseData.data : responseData;
            console.log(`👤 AGENT PROFILE in tracking response:`, data.agent_profile);
            const trackingData = data.tracking_data || data.all_points || [];

            console.log(`🔍 GPS DATA: Found ${trackingData.length || 0} GPS points for ${targetDate}`);

            // Process the data
            const processedData = this.processTrackingData(data, trackingData);
            
            // Cache the processed data
            this.cacheData(cacheKey, processedData);

            // Log API performance
            // const loadTime = Math.round(performance.now() - this.loadingStartTime);
            // console.log(`⚡ API SPEED: Loaded in ${loadTime}ms (fresh data)`);
            
            return processedData;

        } catch (error) {
            console.error('❌ Error fetching tracking data:', error);
            
            // Provide more specific error messages
            let errorMessage = 'Failed to load tracking data';
            if (error.message.includes('400')) {
                errorMessage = `Invalid date format: ${targetDate}. Please try a different date.`;
            } else if (error.message.includes('404')) {
                errorMessage = `No tracking data found for agent ${agentId} on ${targetDate}.`;
            } else if (error.message.includes('500')) {
                errorMessage = 'Server error. Please try again later.';
            }
            
            throw new Error(errorMessage);
        }
    }

    /**
     * Process raw tracking data into structured format
     * @param {Object} data - Raw API data
     * @param {Array} trackingData - Raw tracking points
     * @returns {Object} Processed data
     */
    processTrackingData(data, trackingData) {
        // Convert tracking data to GPS points format
        const gpsPoints = trackingData?.map((point) => {
            // New backend format has location.longitude/latitude
            if (point.location) {
                return [
                    parseFloat(point.location.longitude),
                    parseFloat(point.location.latitude)
                ];
            }
            // Old backend format has longitude/latitude directly
            return [
                parseFloat(point.longitude),
                parseFloat(point.latitude)
            ];
        }) || [];

        console.log(`🔍 GPS PROCESSING: Converted ${trackingData?.length || 0} tracking points → ${gpsPoints.length} GPS coordinates`);

        // Process status markers from status_updates
        const statusMarkers = data.status_updates?.filter(statusUpdate => {
            // Only include status updates with valid coordinates
            const lng = parseFloat(statusUpdate.longitude);
            const lat = parseFloat(statusUpdate.latitude);
            return !isNaN(lng) && !isNaN(lat) &&
                   lng !== null && lat !== null &&
                   lng >= -180 && lng <= 180 &&
                   lat >= -90 && lat <= 90;
        }).map(statusUpdate => ({
            id: statusUpdate.id,
            tripId: statusUpdate.trip_id,
            status: statusUpdate.status,
            timestamp: statusUpdate.timestamp,
            location: [
                parseFloat(statusUpdate.longitude),
                parseFloat(statusUpdate.latitude)
            ],
            note: statusUpdate.note
        })) || [];

        // Process timeline events
        const timelineEvents = data.timeline || [];

        return {
            agent_profile: data.agent_profile,
            gpsPoints,
            timelineEvents,
            statusMarkers,
            battery_level: data.battery_level,
            device_id: timelineEvents.length > 0 ? timelineEvents[timelineEvents.length - 1].device_id : null
        };
    }

    /**
     * Fetch last known location for an agent
     * @param {number} agentId - Agent ID
     * @returns {Promise<Object|null>} Last known location or null
     */
    async fetchLastKnownLocation(agentId) {
        try {
            console.log('🔍 Fetching last known location for agent', agentId);
            const timezoneHeaders = getTimezoneHeaders();
            const response = await fetch(API_ENDPOINTS.LIVE_LOCATION(agentId), {
                headers: {
                    'Content-Type': 'application/json',
                    ...timezoneHeaders
                }
            });

            if (!response.ok) {
                console.log(`⚠️ Last known location API returned ${response.status}`);
                return null;
            }

            const data = await response.json();
            console.log('🔍 Last known location response:', data);

            if (data.success && data.data && data.data.latitude && data.data.longitude) {
                const location = {
                    latitude: parseFloat(data.data.latitude),
                    longitude: parseFloat(data.data.longitude),
                    timestamp: data.data.timestamp,
                    source: 'live_location_api'
                };
                console.log('✅ Last known location found:', location);
                return location;
            }

            console.log('⚠️ No last known location data available');
            return null;

        } catch (error) {
            console.error('❌ Error fetching last known location:', error);
            return null;
        }
    }

    /**
     * Search for last known location from previous dates
     * @param {string} currentDate - Current date to search from
     * @param {number} agentId - Agent ID
     * @param {number} maxDaysBack - Maximum days to search back
     * @returns {Promise<Object|null>} Last known location or null
     */
    async fetchLastKnownLocationFromPreviousDates(currentDate, agentId, maxDaysBack = 7) {
        const startTime = performance.now();
        console.log(`⚡ LIGHTNING SEARCH: Starting fast search for last known location (max ${maxDaysBack} days back)...`);

        try {
            let bestLocation = null;
            let searchDate = new Date();

            // If current date is not today, start from that date
            if (currentDate !== 'today') {
                try {
                    searchDate = new Date(currentDate);
                } catch (error) {
                    console.log('⚠️ Invalid current date, using today as starting point');
                }
            }

            for (let daysBack = 1; daysBack <= maxDaysBack; daysBack++) {
                const checkDate = new Date(searchDate);
                checkDate.setDate(checkDate.getDate() - daysBack);
                const dateString = checkDate.toISOString().split('T')[0];

                console.log(`⚡ FAST CHECK: Day ${daysBack} - checking ${dateString}...`);

                try {
                    const data = await this.fetchTrackingData(agentId, dateString);
                    
                    if (data.gpsPoints && data.gpsPoints.length > 0) {
                        const lastPoint = data.gpsPoints[data.gpsPoints.length - 1];
                        bestLocation = {
                            latitude: lastPoint[1],
                            longitude: lastPoint[0],
                            date: dateString,
                            daysBack: daysBack,
                            source: 'historical_gps'
                        };
                        console.log(`⚡ LIGHTNING HIT: Found location ${daysBack} days back on ${dateString}`);
                        break;
                    }
                } catch (error) {
                    console.log(`⚡ FAST SKIP: No data for ${dateString}, continuing search...`);
                }
            }

            const endTime = performance.now();
            const searchTime = Math.round(endTime - startTime);

            if (bestLocation) {
                console.log(`⚡ LIGHTNING SUCCESS: Found location in ${searchTime}ms (${bestLocation.daysBack} days back)`);
                return bestLocation;
            } else {
                console.log(`⚡ LIGHTNING COMPLETE: No data found in ${searchTime}ms (checked ${maxDaysBack} days)`);
                return null;
            }

        } catch (error) {
            const endTime = performance.now();
            const searchTime = Math.round(endTime - startTime);
            console.error(`❌ LIGHTNING ERROR: Search failed in ${searchTime}ms:`, error);
            return null;
        }
    }

    /**
     * Cache processed data
     * @param {string} cacheKey - Cache key
     * @param {Object} data - Data to cache
     */
    cacheData(cacheKey, data) {
        this.cache[cacheKey] = {
            ...data,
            cachedAt: Date.now()
        };
        console.log(`💰 CACHED: Data stored for key ${cacheKey}`);
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache = {};
        console.log('🧹 Cache cleared');
    }

    /**
     * Get cache size
     * @returns {number} Number of cached items
     */
    getCacheSize() {
        return Object.keys(this.cache).length;
    }
}

// Export singleton instance
export default new TrackingDataService();

/**
 * MapUtils - Utility functions for map calculations and operations
 */

export class MapUtils {
    /**
     * Calculate distance between two points using Haversine formula
     * @param {Array} point1 - [lng, lat]
     * @param {Array} point2 - [lng, lat]
     * @returns {number} Distance in meters
     */
    static calculateDistance(point1, point2) {
        const R = 6371000; // Earth's radius in meters
        const lat1 = point1[1] * Math.PI / 180;
        const lat2 = point2[1] * Math.PI / 180;
        const deltaLat = (point2[1] - point1[1]) * Math.PI / 180;
        const deltaLng = (point2[0] - point1[0]) * Math.PI / 180;

        const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                  Math.cos(lat1) * Math.cos(lat2) *
                  Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c; // Distance in meters
    }

    /**
     * Calculate correct bearing between two GPS points for arrow direction
     * @param {Array} point1 - [lng, lat] starting point
     * @param {Array} point2 - [lng, lat] ending point
     * @returns {number} Bearing in degrees (0-360)
     */
    static calculateCorrectBearing(point1, point2) {
        // Validate input points
        if (!point1 || !point2 || !Array.isArray(point1) || !Array.isArray(point2)) {
            return 0; // Default bearing
        }

        if (point1.length < 2 || point2.length < 2) {
            return 0; // Default bearing
        }

        // Check for null/undefined coordinates
        if (point1[0] == null || point1[1] == null || point2[0] == null || point2[1] == null) {
            return 0; // Default bearing
        }

        const lat1 = point1[1] * Math.PI / 180;
        const lat2 = point2[1] * Math.PI / 180;
        const deltaLng = (point2[0] - point1[0]) * Math.PI / 180;

        const y = Math.sin(deltaLng) * Math.cos(lat2);
        const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLng);

        let bearing = Math.atan2(y, x) * 180 / Math.PI;

        // Check for NaN result
        if (isNaN(bearing)) {
            return 0; // Default bearing
        }

        // Normalize to 0-360 degrees
        bearing = (bearing + 360) % 360;

        return bearing;
    }

    /**
     * Calculate precise bearing between two points for arrow direction
     * @param {Array} point1 - [lng, lat]
     * @param {Array} point2 - [lng, lat]
     * @returns {number} Bearing in degrees
     */
    static calculatePreciseBearing(point1, point2) {
        return MapUtils.calculateCorrectBearing(point1, point2);
    }

    /**
     * Calculate bearing between two points (legacy function)
     * @param {Array} point1 - [lng, lat]
     * @param {Array} point2 - [lng, lat]
     * @returns {number} Bearing in degrees
     */
    static calculateBearing(point1, point2) {
        return MapUtils.calculatePreciseBearing(point1, point2);
    }

    /**
     * Validate coordinates
     * @param {Array} coordinates - [lng, lat]
     * @returns {boolean} True if valid
     */
    static validateCoordinates(coordinates) {
        return Array.isArray(coordinates) && 
               coordinates.length >= 2 && 
               coordinates[0] != null && 
               coordinates[1] != null && 
               !isNaN(coordinates[0]) && 
               !isNaN(coordinates[1]);
    }

    /**
     * Validate bearing value
     * @param {number} bearing - Bearing in degrees
     * @returns {boolean} True if valid
     */
    static validateBearing(bearing) {
        return bearing != null && !isNaN(bearing);
    }

    /**
     * Validate zoom level
     * @param {number} zoom - Zoom level
     * @returns {boolean} True if valid
     */
    static validateZoom(zoom) {
        return zoom != null && !isNaN(zoom) && zoom >= 0 && zoom <= 22;
    }
}

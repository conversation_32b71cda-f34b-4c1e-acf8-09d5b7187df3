/**
 * MapService - Main service class (refactored and cleaned)
 * 
 * This is the main MapService that coordinates all map operations
 * using the modular architecture with separate classes for different concerns.
 */

import { MapInitializer } from './MapInitializer';
import { GPSRenderer } from './GPSRenderer';
import { MapCleaner } from './MapCleaner';
import { SATELLITE_CONFIG, OLA_MAPS_CONFIG } from '../../constants/mapConfig';

class MapService {
    constructor() {
        this.mapInstance = null;
        this.initializer = new MapInitializer();
        this.gpsRenderer = null;
        this.cleaner = null;
        this.agentId = null;
    }

    /**
     * Set agent ID for profile photo generation
     * @param {string} agentId - Agent ID
     */
    setAgentId(agentId) {
        this.agentId = agentId;

        // If GPS renderer exists, update its agent ID
        if (this.gpsRenderer) {
            this.gpsRenderer.agentId = agentId;
        }
    }

    /**
     * Set marker settings from database
     * @param {Object} settings - Marker settings object from database
     */
    setMarkerSettings(settings) {
        if (this.gpsRenderer) {
            this.gpsRenderer.setMarkerSettings(settings);
        } else {
            // Store settings for when renderer is initialized
            this._pendingMarkerSettings = settings;
        }
    }

    /**
     * Set map instance for the singleton
     * @param {Object} mapInstance - OLA Maps instance
     */
    setMapInstance(mapInstance) {
        this.mapInstance = mapInstance;

        // Initialize renderers and cleaners with the map instance and agent ID
        this.gpsRenderer = new GPSRenderer(mapInstance, this.agentId);
        this.cleaner = new MapCleaner(mapInstance);

        // Apply any pending marker settings
        if (this._pendingMarkerSettings) {
            this.gpsRenderer.setMarkerSettings(this._pendingMarkerSettings);
            delete this._pendingMarkerSettings;
        }

        // Store GPS renderer globally for cleanup access
        window.gpsRenderer = this.gpsRenderer;

        // Add arrow image for GPS direction arrows
        this.gpsRenderer.addArrowImage();

        console.log('🗺️ MapService: Map instance set and renderers initialized');
    }

    /**
     * Initialize map instance
     * @param {HTMLElement} container - Map container element
     * @param {Object} options - Map initialization options
     * @returns {Promise<Object>} Map instance
     */
    async initializeMap(container, options = {}) {
        try {
            const mapInstance = await this.initializer.initializeMap(container, options);
            this.setMapInstance(mapInstance);
            return mapInstance;
        } catch (error) {
            console.error('❌ MapService: Failed to initialize map:', error);
            throw error;
        }
    }

    /**
     * Draw GPS tracking line with arrows
     * @param {Array} gpsPoints - GPS coordinates [[lng, lat], ...]
     */
    drawGPSLine(gpsPoints) {
        if (!this.gpsRenderer) {
            console.error('❌ GPS renderer not initialized');
            return;
        }
        
        this.gpsRenderer.drawGPSLine(gpsPoints);
    }

    /**
     * Clear GPS line and arrows from map
     */
    clearGPSLine() {
        if (!this.cleaner) {
            console.error('❌ Map cleaner not initialized');
            return;
        }
        
        this.cleaner.clearGPSLineCompletely();
    }

    /**
     * Clear GPS line completely (comprehensive cleanup)
     */
    clearGPSLineCompletely() {
        this.clearGPSLine();
    }

    /**
     * Reset GPS line drawing flag (for error recovery)
     */
    resetGPSLineDrawingFlag() {
        if (this.gpsRenderer) {
            this.gpsRenderer.resetGPSLineDrawingFlag();
        }
    }

    /**
     * Highlight route segment between two timeline points
     * @param {Array} allGpsPoints - All GPS coordinates
     * @param {Array} timelineEvents - Timeline events
     * @param {Array} selectedIndices - [startIndex, endIndex] of selected timeline points
     */
    highlightRouteSegment(allGpsPoints, timelineEvents, selectedIndices) {
        if (!this.gpsRenderer) {
            console.error('❌ GPS renderer not initialized');
            return;
        }

        this.gpsRenderer.drawRouteSegmentHighlight(allGpsPoints, timelineEvents, selectedIndices);
    }

    /**
     * Get current map instance
     * @returns {Object|null} Map instance
     */
    getMapInstance() {
        return this.mapInstance;
    }

    /**
     * Check if map is loaded
     * @returns {boolean} True if map is loaded
     */
    isMapLoaded() {
        return this.mapInstance !== null;
    }

    /**
     * Destroy map instance and cleanup
     */
    destroy() {
        if (this.cleaner) {
            this.cleaner.clearAll();
        }
        
        if (this.gpsRenderer) {
            this.gpsRenderer.resetGPSLineDrawingFlag();
        }
        
        this.mapInstance = null;
        this.gpsRenderer = null;
        this.cleaner = null;
        
        console.log('🗺️ MapService: Destroyed and cleaned up');
    }

    /**
     * Fit map to bounds
     * @param {Array} bounds - [[minLng, minLat], [maxLng, maxLat]]
     * @param {Object} options - Fit bounds options
     */
    fitToBounds(bounds, options = {}) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for fitToBounds');
            return;
        }

        try {
            this.mapInstance.fitBounds(bounds, {
                padding: 50,
                duration: 1000,
                ...options
            });
        } catch (error) {
            console.error('❌ Error fitting bounds:', error);
        }
    }

    /**
     * Center map on specific coordinates with smooth animation
     * @param {Array} coordinates - [lng, lat] coordinates
     * @param {number} zoom - Zoom level (optional)
     * @param {Object} options - Animation options
     */
    centerOnLocation(coordinates, zoom = 16, options = {}) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for centerOnLocation');
            return;
        }

        if (!Array.isArray(coordinates) || coordinates.length !== 2) {
            console.error('❌ Invalid coordinates for centerOnLocation:', coordinates);
            return;
        }

        try {
            const [lng, lat] = coordinates;

            // Validate coordinates
            if (isNaN(lng) || isNaN(lat)) {
                console.error('❌ Invalid coordinate values:', { lng, lat });
                return;
            }

            console.log('🎯 Centering map on location:', { lng, lat, zoom });

            // Use flyTo for smooth animation
            this.mapInstance.flyTo({
                center: [lng, lat],
                zoom: zoom,
                duration: options.duration || 1500,
                essential: true // This animation is considered essential with respect to prefers-reduced-motion
            });

        } catch (error) {
            console.error('❌ Error centering on location:', error);
        }
    }

    /**
     * Center map on timeline event location
     * @param {Object} event - Timeline event object
     * @param {number} zoom - Zoom level (optional)
     */
    centerOnTimelineEvent(event, zoom = 16) {
        if (!event) {
            console.error('❌ No event provided for centerOnTimelineEvent');
            return;
        }

        let coordinates = null;

        // Try different coordinate formats
        if (event.longitude && event.latitude) {
            coordinates = [parseFloat(event.longitude), parseFloat(event.latitude)];
        } else if (event.lng && event.lat) {
            coordinates = [parseFloat(event.lng), parseFloat(event.lat)];
        } else if (event.location && Array.isArray(event.location) && event.location.length === 2) {
            coordinates = [parseFloat(event.location[0]), parseFloat(event.location[1])];
        } else if (event.coordinates && Array.isArray(event.coordinates) && event.coordinates.length === 2) {
            coordinates = [parseFloat(event.coordinates[0]), parseFloat(event.coordinates[1])];
        }

        if (coordinates) {
            console.log('🎯 Centering on timeline event:', event.event_type || 'Event', coordinates);
            this.centerOnLocation(coordinates, zoom);
        } else {
            console.error('❌ Could not extract coordinates from timeline event:', event);
        }
    }

    /**
     * Center map on route segment between two timeline events
     * @param {Array} gpsPoints - All GPS points
     * @param {Array} timelineEvents - Timeline events
     * @param {Array} selectedIndices - Selected timeline event indices
     */
    centerOnRouteSegment(gpsPoints, timelineEvents, selectedIndices) {
        if (!gpsPoints || !timelineEvents || !selectedIndices || selectedIndices.length !== 2) {
            console.error('❌ Invalid parameters for centerOnRouteSegment');
            return;
        }

        try {
            const [startIndex, endIndex] = selectedIndices.sort((a, b) => a - b);
            const startEvent = timelineEvents[startIndex];
            const endEvent = timelineEvents[endIndex];

            if (!startEvent || !endEvent) {
                console.error('❌ Could not find timeline events for indices:', selectedIndices);
                return;
            }

            // Extract coordinates from events
            const startCoords = this.extractEventCoordinates(startEvent);
            const endCoords = this.extractEventCoordinates(endEvent);

            if (!startCoords || !endCoords) {
                console.error('❌ Could not extract coordinates from timeline events');
                return;
            }

            // Calculate bounds for the segment
            const minLng = Math.min(startCoords[0], endCoords[0]);
            const maxLng = Math.max(startCoords[0], endCoords[0]);
            const minLat = Math.min(startCoords[1], endCoords[1]);
            const maxLat = Math.max(startCoords[1], endCoords[1]);

            // Add some padding to the bounds
            const padding = 0.001; // Adjust as needed
            const bounds = [
                [minLng - padding, minLat - padding],
                [maxLng + padding, maxLat + padding]
            ];

            console.log('🎯 Centering on route segment bounds:', bounds);
            this.fitToBounds(bounds, { padding: 100 });

        } catch (error) {
            console.error('❌ Error centering on route segment:', error);
        }
    }

    /**
     * Extract coordinates from timeline event (helper method)
     * @param {Object} event - Timeline event
     * @returns {Array|null} [lng, lat] coordinates or null
     */
    extractEventCoordinates(event) {
        if (event.longitude && event.latitude) {
            return [parseFloat(event.longitude), parseFloat(event.latitude)];
        } else if (event.lng && event.lat) {
            return [parseFloat(event.lng), parseFloat(event.lat)];
        } else if (event.location && Array.isArray(event.location) && event.location.length === 2) {
            return [parseFloat(event.location[0]), parseFloat(event.location[1])];
        } else if (event.coordinates && Array.isArray(event.coordinates) && event.coordinates.length === 2) {
            return [parseFloat(event.coordinates[0]), parseFloat(event.coordinates[1])];
        }
        return null;
    }

    /**
     * Toggle satellite view
     * @param {boolean} enableSatellite - Whether to enable satellite view
     */
    toggleSatelliteView(enableSatellite) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for satellite toggle');
            return;
        }

        console.log(`🛰️ Switching to ${enableSatellite ? 'satellite' : 'standard'} view...`);

        try {
            if (enableSatellite) {
                // Switch to satellite view using configured satellite sources
                this.mapInstance.setStyle({
                    version: 8,
                    sources: SATELLITE_CONFIG.SOURCES,
                    layers: SATELLITE_CONFIG.LAYERS
                });

                // Set zoom limits for satellite mode
                const currentZoom = this.mapInstance.getZoom();
                const maxSafeZoom = OLA_MAPS_CONFIG.ZOOM_LIMITS.MAX_SATELLITE;

                this.mapInstance.setMaxZoom(maxSafeZoom);

                // If current zoom exceeds safe limit, smoothly zoom out
                if (currentZoom > maxSafeZoom) {
                    this.mapInstance.easeTo({
                        zoom: maxSafeZoom,
                        duration: 1000
                    });
                    console.log(`🎯 Adjusted zoom from ${currentZoom.toFixed(1)} to ${maxSafeZoom} for satellite view`);
                }

                console.log('✅ Switched to satellite view');
            } else {
                // Switch back to standard OLA Maps view
                this.mapInstance.setStyle(OLA_MAPS_CONFIG.STYLES.DEFAULT);
                this.mapInstance.setMaxZoom(OLA_MAPS_CONFIG.ZOOM_LIMITS.MAX_VECTOR);
                console.log('✅ Switched back to standard view');
            }
        } catch (error) {
            console.error('❌ Error toggling satellite view:', error);
        }
    }

    /**
     * Add source to map
     * @param {string} id - Source ID
     * @param {Object} source - Source configuration
     */
    addSource(id, source) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for addSource');
            return;
        }

        try {
            if (!this.mapInstance.getSource(id)) {
                this.mapInstance.addSource(id, source);
            }
        } catch (error) {
            console.error(`❌ Error adding source ${id}:`, error);
        }
    }

    /**
     * Add layer to map
     * @param {Object} layer - Layer configuration
     */
    addLayer(layer) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for addLayer');
            return;
        }

        try {
            if (!this.mapInstance.getLayer(layer.id)) {
                this.mapInstance.addLayer(layer);
            }
        } catch (error) {
            console.error(`❌ Error adding layer ${layer.id}:`, error);
        }
    }

    /**
     * Remove layer from map
     * @param {string} layerId - Layer ID
     */
    removeLayer(layerId) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for removeLayer');
            return;
        }

        try {
            if (this.mapInstance.getLayer(layerId)) {
                this.mapInstance.removeLayer(layerId);
            }
        } catch (error) {
            console.error(`❌ Error removing layer ${layerId}:`, error);
        }
    }

    /**
     * Remove source from map
     * @param {string} sourceId - Source ID
     */
    removeSource(sourceId) {
        if (!this.mapInstance) {
            console.error('❌ Map instance not available for removeSource');
            return;
        }

        try {
            if (this.mapInstance.getSource(sourceId)) {
                this.mapInstance.removeSource(sourceId);
            }
        } catch (error) {
            console.error(`❌ Error removing source ${sourceId}:`, error);
        }
    }
}

// Create singleton instance
const mapServiceInstance = new MapService();

export default mapServiceInstance;

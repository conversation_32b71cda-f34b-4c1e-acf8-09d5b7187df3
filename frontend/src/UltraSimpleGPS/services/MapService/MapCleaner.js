/**
 * Map<PERSON>lean<PERSON> - Handles cleanup of map layers and sources
 */

export class MapCleaner {
    constructor(mapInstance) {
        this.mapInstance = mapInstance;
    }

    /**
     * Clear GPS line and arrows from map (comprehensive cleanup)
     */
    clearGPSLineCompletely() {
        if (!this.mapInstance) return;

        // All possible GPS-related layers that might exist (including segment highlighting)
        const allGPSLayers = [
            'gps-path', 'gps-direction', 'gps-arrows', 'gps-tick-marks',
            'route', 'arrows', 'gps-line-layer', 'gps-markers',
            'gps-path-background', 'gps-path-highlight', 'gps-segment-markers'
        ];

        // All possible GPS-related sources that might exist (including segment highlighting)
        const allGPSSources = [
            'gps-line', 'gps-arrows', 'gps-ticks', 'route', 'route-arrows',
            'gps-markers', 'gps-points',
            'gps-line-background', 'gps-line-highlight', 'gps-segment-markers'
        ];

        // Remove all GPS layers
        allGPSLayers.forEach(layerId => {
            try {
                if (this.mapInstance.getLayer && this.mapInstance.getLayer(layerId)) {
                    this.mapInstance.removeLayer(layerId);
                }
            } catch (e) {
                // Silently ignore - layer might not exist
            }
        });

        // Remove all GPS sources
        allGPSSources.forEach(sourceId => {
            try {
                if (this.mapInstance.getSource && this.mapInstance.getSource(sourceId)) {
                    this.mapInstance.removeSource(sourceId);
                }
            } catch (e) {
                // Silently ignore - source might not exist
            }
        });

        // Clear directional arrows
        this.clearDirectionalArrows();

        // Clear start/end markers
        this.clearStartEndMarkers();
    }

    /**
     * Clear directional arrows
     */
    clearDirectionalArrows() {
        if (!this.mapInstance) return;

        try {
            if (this.mapInstance.getLayer('gps-direction')) {
                this.mapInstance.removeLayer('gps-direction');
            }
        } catch (e) {
            // Silently ignore
        }

        try {
            if (this.mapInstance.getSource('gps-arrows')) {
                this.mapInstance.removeSource('gps-arrows');
            }
        } catch (e) {
            // Silently ignore
        }
    }

    /**
     * Clear start and end markers
     */
    clearStartEndMarkers() {
        if (!this.mapInstance) return;

        try {
            if (this.mapInstance.getLayer('gps-markers')) {
                this.mapInstance.removeLayer('gps-markers');
            }
        } catch (e) {
            // Silently ignore
        }

        try {
            if (this.mapInstance.getSource('gps-markers')) {
                this.mapInstance.removeSource('gps-markers');
            }
        } catch (e) {
            // Silently ignore
        }

        // Clear profile markers (DOM-based markers)
        if (window.gpsRenderer && window.gpsRenderer.profileMarkers) {
            window.gpsRenderer.profileMarkers.forEach(markerElement => {
                try {
                    // Call cleanup function if it exists
                    if (markerElement._cleanup) {
                        markerElement._cleanup();
                    } else {
                        // Fallback: remove from DOM directly
                        if (markerElement.parentNode) {
                            markerElement.parentNode.removeChild(markerElement);
                        }
                    }
                } catch (e) {
                    // Silently ignore
                }
            });
            window.gpsRenderer.profileMarkers = [];
        }

        // AGGRESSIVE CLEANUP: Remove all GPS-related markers by class name
        if (this.mapInstance) {
            const mapContainer = this.mapInstance.getContainer();
            const gpsMarkerSelectors = [
                '.gps-start-marker',
                '.gps-end-marker',
                '.start-marker',
                '.end-marker',
                '[class*="gps-"][class*="marker"]'
            ];

            gpsMarkerSelectors.forEach(selector => {
                try {
                    const markers = mapContainer.querySelectorAll(selector);
                    markers.forEach(marker => {
                        if (marker.parentNode) {
                            marker.parentNode.removeChild(marker);
                        }
                    });
                    if (markers.length > 0) {
                        console.log(`🧹 Cleaned up ${markers.length} GPS markers with selector: ${selector}`);
                    }
                } catch (e) {
                    // Silently ignore
                }
            });
        }
    }

    /**
     * Clear all map layers and sources (complete cleanup)
     */
    clearAll() {
        this.clearGPSLineCompletely();
        
        // Clear any additional cleanup if needed
        // This can be extended for other types of cleanup
    }
}

/**
 * MapInitializer - Handles OLA Maps SDK loading and map initialization
 */

import { OLA_MAPS_CONFIG, SDK_CONFIG } from '../../constants';

export class MapInitializer {
    constructor() {
        this.isSDKLoaded = false;
    }

    /**
     * Load OLA Maps SDK dynamically
     * @returns {Promise<Object>} OLA Maps instance
     */
    async loadOlaMapsSDK() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (window.OlaMaps) {
                console.log('✅ OLA Maps already loaded');
                this.isSDKLoaded = true;
                resolve(window.OlaMaps);
                return;
            }

            console.log('🔄 Dynamically loading OLA Maps SDK...');

            const script = document.createElement('script');
            script.src = SDK_CONFIG.PRIMARY_CDN;

            script.onload = () => {
                if (window.OlaMaps) {
                    console.log('✅ OLA Maps SDK loaded successfully from primary CDN');
                    this.isSDKLoaded = true;
                    resolve(window.OlaMaps);
                } else {
                    console.error('❌ OLA Maps not available after script load');
                    this.tryFallbackCDN(resolve, reject);
                }
            };

            script.onerror = () => {
                console.warn('⚠️ Primary CDN failed, trying fallback...');
                this.tryFallbackCDN(resolve, reject);
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Try fallback CDN if primary fails
     */
    tryFallbackCDN(resolve, reject) {
        const fallbackScript = document.createElement('script');
        fallbackScript.src = SDK_CONFIG.FALLBACK_CDN;

        fallbackScript.onload = () => {
            if (window.OlaMaps) {
                console.log('✅ OLA Maps SDK loaded from fallback CDN');
                this.isSDKLoaded = true;
                resolve(window.OlaMaps);
            } else {
                reject(new Error('OLA Maps SDK failed to load from both CDNs'));
            }
        };

        fallbackScript.onerror = () => {
            reject(new Error('OLA Maps SDK failed to load from both CDNs'));
        };

        document.head.appendChild(fallbackScript);
    }

    /**
     * Initialize map instance
     * @param {HTMLElement} container - Map container element
     * @param {Object} options - Map initialization options
     * @returns {Promise<Object>} Map instance
     */
    async initializeMap(container, options = {}) {
        try {
            // Validate container
            if (!container) {
                throw new Error('Map container element is required');
            }

            // Load SDK if not already loaded
            if (!this.isSDKLoaded) {
                await this.loadOlaMapsSDK();
            }

            console.log('✅ OLA Maps SDK ready, initializing map...');

            // Use hardcoded API key
            const olaMaps = new window.OlaMaps({
                apiKey: 'dIrHt1iSmxED4qrHKnABwVRfKkzYQUez0BFR73kQ'
            });

            // Validate and set defaults for center and zoom
            let center = options.center || OLA_MAPS_CONFIG.DEFAULT_CENTER;
            let zoom = options.zoom || OLA_MAPS_CONFIG.DEFAULT_ZOOM;

            // Validate center coordinates
            if (!Array.isArray(center) || center.length !== 2) {
                console.warn('⚠️ Invalid center coordinates, using default');
                center = OLA_MAPS_CONFIG.DEFAULT_CENTER;
            }

            // Ensure center coordinates are numbers
            center = [parseFloat(center[0]), parseFloat(center[1])];
            if (isNaN(center[0]) || isNaN(center[1])) {
                console.warn('⚠️ Invalid center coordinate values, using default');
                center = OLA_MAPS_CONFIG.DEFAULT_CENTER;
            }

            // Validate zoom
            zoom = parseFloat(zoom);
            if (isNaN(zoom) || zoom < 1 || zoom > 20) {
                console.warn('⚠️ Invalid zoom level, using default');
                zoom = OLA_MAPS_CONFIG.DEFAULT_ZOOM;
            }

            // Use simple, reliable style URL
            const styleUrl = "https://api.olamaps.io/tiles/vector/v1/styles/default-light-standard/style.json";

            // Create map options with validated parameters
            const mapOptions = {
                style: styleUrl,
                container: container,
                center: center,
                zoom: zoom,
                // Add additional safety parameters
                bearing: 0,
                pitch: 0,
                antialias: true,
                optimizeForTerrain: false
            };

            console.log('🗺️ Validated map options:', mapOptions);

            let map;
            try {
                console.log('🗺️ Initializing map with options:', mapOptions);
                map = olaMaps.init(mapOptions);
                console.log('✅ Map initialized successfully');
            } catch (initError) {
                console.error('❌ Map initialization failed:', initError);
                throw new Error(`Map initialization failed: ${initError.message}`);
            }

            return new Promise((resolve, reject) => {
                let hasResolved = false;

                // Add load event listener
                map.on('load', () => {
                    if (!hasResolved) {
                        hasResolved = true;
                        console.log('✅ Map loaded successfully');
                        resolve(map);
                    }
                });

                // Add error event listener with better error handling
                map.on('error', (error) => {
                    if (!hasResolved) {
                        hasResolved = true;
                        console.error('❌ Map load error:', error);

                        // Try to provide more specific error information
                        const errorMessage = error?.error?.message || error?.message || 'Unknown map error';
                        reject(new Error(`Map failed to load: ${errorMessage}`));
                    }
                });

                // Add style load event as backup
                map.on('style.load', () => {
                    if (!hasResolved) {
                        hasResolved = true;
                        console.log('✅ Map style loaded successfully');
                        resolve(map);
                    }
                });

                // Shorter timeout for faster feedback
                setTimeout(() => {
                    if (!hasResolved) {
                        hasResolved = true;
                        console.warn('⚠️ Map load timeout, but resolving anyway');
                        resolve(map);
                    }
                }, 8000); // Reduced from 10 seconds
            });

        } catch (error) {
            console.error('❌ Map initialization error:', error);
            throw error;
        }
    }
}

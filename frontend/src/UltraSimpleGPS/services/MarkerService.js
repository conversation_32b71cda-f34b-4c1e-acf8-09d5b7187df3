/**
 * Marker Service
 * 
 * Service class for managing all types of markers on the map
 */

import { 
    STATUS_MARKER_COLORS, 
    STATUS_MARKER_ICONS, 
    MARKER_SIZES, 
    MARKER_BORDERS,
    MARKER_SELECTORS,
    START_END_MARKERS,
    DELIVERY_MARKERS,
    TIMELINE_EVENT_CONFIG
} from '../constants';

class MarkerService {
    constructor() {
        this.trackedMarkers = [];
        this.eventListeners = [];
        this.customMarkerSettings = {};
    }

    /**
     * Set custom marker settings
     * @param {Object} settings - Custom marker settings
     */
    setCustomMarkerSettings(settings) {
        this.customMarkerSettings = settings;
        console.log('🎨 Updated custom marker settings:', settings);
    }

    /**
     * Get status marker color (with custom settings support)
     * @param {string} status - Status type
     * @returns {string} Color hex code
     */
    getStatusMarkerColor(status) {
        // Check if user has custom settings for this marker type
        if (this.customMarkerSettings[status]) {
            return this.customMarkerSettings[status].color;
        }

        // Default colors
        return STATUS_MARKER_COLORS[status] || '#666666';
    }

    /**
     * Get status marker icon (with custom settings support)
     * @param {string} status - Status type
     * @returns {string} Icon HTML or text
     */
    getStatusMarkerIcon(status) {
        console.log(`🎨 Getting icon for status: ${status}`, {
            customSettings: this.customMarkerSettings[status],
            hasCustomSettings: !!this.customMarkerSettings[status]
        });

        // Check if user has custom settings for this marker type
        if (this.customMarkerSettings[status]) {
            if (this.customMarkerSettings[status].icon_image_url && this.customMarkerSettings[status].icon_image_url.trim() !== '') {
                const imageUrl = this.customMarkerSettings[status].icon_image_url;
                console.log(`🖼️ Using custom image for ${status}:`, imageUrl);
                // Return HTML for custom image with better error handling and styling
                return `<img src="${imageUrl}"
                    style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%; display: block;"
                    onerror="console.error('Failed to load marker image: ${imageUrl}'); this.style.display='none'; this.parentElement.innerHTML='❌';"
                    onload="console.log('✅ Marker image loaded successfully: ${imageUrl}');"
                    alt="Custom marker" />`;
            } else if (this.customMarkerSettings[status].icon_text && this.customMarkerSettings[status].icon_text.trim() !== '') {
                console.log(`📝 Using custom text for ${status}:`, this.customMarkerSettings[status].icon_text);
                return this.customMarkerSettings[status].icon_text;
            }
        }

        // Default icons
        return STATUS_MARKER_ICONS[status] || '📍';
    }

    /**
     * Get timeline event title
     * @param {Object} event - Timeline event
     * @returns {string} Event title
     */
    getTimelineEventTitle(event) {
        const eventType = event.event_type || event.type || 'unknown';
        
        const titles = {
            'checked_in': 'Checked In',
            'checked_out': 'Checked Out',
            'break_start': 'Break Started',
            'break_end': 'Break Ended',
            'delivered': 'Delivery Completed',
            'failed': 'Delivery Failed',
            'rescheduled': 'Delivery Rescheduled'
        };
        
        return titles[eventType] || 'Timeline Event';
    }

    /**
     * Add status markers to map
     * @param {Object} mapInstance - Map instance
     * @param {Array} statusMarkersData - Status markers data
     */
    addStatusMarkers(mapInstance, statusMarkersData) {
        if (!mapInstance || !statusMarkersData || statusMarkersData.length === 0) {
            console.log('⚠️ Cannot add status markers: missing map instance or markers');
            return;
        }

        console.log(`📍 Adding ${statusMarkersData.length} status markers to map`);
        console.log('📍 Status markers data:', statusMarkersData);

        const mapContainer = mapInstance.getContainer();
        const markerElements = [];
        let markersAdded = 0;

        statusMarkersData.forEach((statusMarker, index) => {
            if (!statusMarker.location || statusMarker.location.length < 2) {
                console.log(`⚠️ Skipping status marker ${index}: invalid location`);
                return;
            }

            // Validate coordinates with comprehensive checks
            if (!statusMarker.location || !Array.isArray(statusMarker.location) || statusMarker.location.length < 2) {
                console.warn(`⚠️ Skipping status marker ${index}: invalid location array`, {
                    location: statusMarker.location
                });
                return;
            }

            const [longitude, latitude] = statusMarker.location;

            // Check for null, undefined, or non-numeric values
            if (longitude == null || latitude == null ||
                longitude === '' || latitude === '' ||
                typeof longitude === 'undefined' || typeof latitude === 'undefined') {
                console.warn(`⚠️ Skipping status marker ${index}: null/undefined coordinates`, {
                    longitude, latitude, location: statusMarker.location
                });
                return;
            }

            const lng = parseFloat(longitude);
            const lat = parseFloat(latitude);

            if (isNaN(lng) || isNaN(lat) || !isFinite(lng) || !isFinite(lat)) {
                console.warn(`⚠️ Skipping status marker ${index}: invalid coordinates`, {
                    longitude, latitude, parsed: [lng, lat]
                });
                return;
            }

            // Check for reasonable coordinate ranges
            if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
                console.warn(`⚠️ Skipping status marker ${index}: coordinates out of range`, {
                    longitude: lng, latitude: lat
                });
                return;
            }

            try {
                // Get custom settings for this marker type
                const customSetting = this.customMarkerSettings[statusMarker.status];
                const markerSize = customSetting?.size || MARKER_SIZES.STATUS.DEFAULT;
                const borderColor = customSetting?.border_color || MARKER_BORDERS.DEFAULT_COLOR;
                const borderWidth = customSetting?.border_width || MARKER_BORDERS.DEFAULT_WIDTH;

                // Create marker element
                const markerElement = document.createElement('div');
                markerElement.className = `custom-marker status-marker status-marker-${index}`;
                markerElement.style.cssText = `
                    position: absolute;
                    width: ${markerSize}px;
                    height: ${markerSize}px;
                    background-color: ${this.getStatusMarkerColor(statusMarker.status)};
                    border: ${borderWidth}px solid ${borderColor};
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    font-weight: bold;
                    color: white;
                    cursor: pointer;
                    z-index: 1000;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                `;

                markerElement.innerHTML = this.getStatusMarkerIcon(statusMarker.status);

                // Add click handler for marker info
                const clickHandler = () => {
                    const timestamp = new Date(statusMarker.timestamp).toLocaleString();
                    alert(`Status: ${statusMarker.status.toUpperCase()}\n${statusMarker.note || 'No additional notes'}\nTime: ${timestamp}`);
                };

                markerElement.addEventListener('click', clickHandler);

                // Add to global tracking system
                this.trackedMarkers.push(markerElement);
                this.eventListeners.push(() => {
                    markerElement.removeEventListener('click', clickHandler);
                });

                // Add to container
                mapContainer.appendChild(markerElement);
                markerElements.push({ element: markerElement, coordinates: [lng, lat] });
                markersAdded++;

                console.log(`✅ Added status marker ${index}: ${statusMarker.status} at [${lat}, ${lng}]`);

            } catch (error) {
                console.error(`❌ Error adding status marker ${index}:`, error);
            }
        });

        // Position all markers
        const updateStatusMarkerPositions = () => {
            markerElements.forEach(({ element, coordinates }) => {
                try {
                    if (element && element.parentNode && coordinates && coordinates.length === 2) {
                        // Validate coordinates
                        const [lng, lat] = coordinates;
                        if (typeof lng === 'number' && typeof lat === 'number' &&
                            !isNaN(lng) && !isNaN(lat) &&
                            lng !== null && lat !== null) {

                            const pixel = mapInstance.project(coordinates);
                            if (pixel && typeof pixel.x === 'number' && typeof pixel.y === 'number' &&
                                !isNaN(pixel.x) && !isNaN(pixel.y)) {
                                element.style.left = pixel.x + 'px';
                                element.style.top = pixel.y + 'px';
                            }
                        } else {
                            console.warn('⚠️ Invalid coordinates for status marker:', coordinates);
                        }
                    }
                } catch (error) {
                    console.error('❌ Error positioning status marker:', error);
                }
            });
        };

        // Initial positioning
        updateStatusMarkerPositions();

        // Add event listeners for map movement
        mapInstance.on('move', updateStatusMarkerPositions);
        mapInstance.on('zoom', updateStatusMarkerPositions);

        console.log(`✅ Added ${markersAdded} status markers to map`);
    }

    /**
     * Add timeline event markers to map
     * @param {Object} mapInstance - Map instance
     * @param {Array} events - Timeline events
     */
    addTimelineEventMarkers(mapInstance, events) {
        if (!mapInstance || !events || events.length === 0) {
            console.log('⚠️ Cannot add timeline markers: missing map instance or events');
            return;
        }

        // Clear existing markers first
        this.nuclearClearAllMarkers();

        console.log(`📍 Adding ${events.length} timeline event markers to map`);

        const mapContainer = mapInstance.getContainer();
        const markerElements = [];
        let markersAdded = 0;

        events.forEach((event, index) => {
            // Validate coordinates
            const longitude = parseFloat(event.longitude);
            const latitude = parseFloat(event.latitude);

            if (!event.latitude || !event.longitude ||
                isNaN(longitude) || isNaN(latitude) ||
                longitude === null || latitude === null) {
                console.warn(`⚠️ Skipping timeline event ${index}: invalid coordinates`, {
                    longitude: event.longitude,
                    latitude: event.latitude,
                    parsed: [longitude, latitude]
                });
                return;
            }

            try {
                // Get event type/status for custom marker settings
                const eventType = event.event_type || event.status || event.type || 'default';
                console.log(`🎨 Creating timeline marker for event type: ${eventType}`, event);

                // Get custom settings for this event type
                const customSetting = this.customMarkerSettings[eventType];
                const markerSize = customSetting?.size || MARKER_SIZES.TIMELINE.DEFAULT;
                const markerColor = this.getStatusMarkerColor(eventType);
                const markerIcon = this.getStatusMarkerIcon(eventType);
                const borderColor = customSetting?.border_color || MARKER_BORDERS.DEFAULT_COLOR;
                const borderWidth = customSetting?.border_width || 2;

                console.log(`🎨 Using custom settings for ${eventType}:`, {
                    size: markerSize,
                    color: markerColor,
                    icon: markerIcon,
                    borderColor,
                    borderWidth
                });

                // Create marker element with custom styling
                const markerElement = document.createElement('div');
                markerElement.className = `custom-marker timeline-marker timeline-marker-${index} timeline-marker-${eventType}`;
                markerElement.style.cssText = `
                    position: absolute;
                    width: ${markerSize}px;
                    height: ${markerSize}px;
                    background-color: ${markerColor};
                    border: ${borderWidth}px solid ${borderColor};
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: ${Math.max(10, markerSize * 0.4)}px;
                    cursor: pointer;
                    z-index: 1000;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                `;

                markerElement.innerHTML = markerIcon;

                // Add click handler for marker info
                const clickHandler = () => {
                    const locationName = event.address || 'Location not available';
                    const eventTitle = this.getTimelineEventTitle(event);
                    const eventTime = new Date(event.recorded_at).toLocaleString();
                    alert(`${eventTitle}\n\n📍 ${locationName}\n\n🕒 ${eventTime}`);
                };

                markerElement.addEventListener('click', clickHandler);

                // Add to global tracking system
                this.trackedMarkers.push(markerElement);
                this.eventListeners.push(() => {
                    markerElement.removeEventListener('click', clickHandler);
                });

                // Add to container
                mapContainer.appendChild(markerElement);
                markerElements.push({ element: markerElement, coordinates: [longitude, latitude] });
                markersAdded++;

                console.log(`✅ Added timeline marker ${index} at [${latitude}, ${longitude}]`);

            } catch (error) {
                console.error(`❌ Error adding timeline marker ${index}:`, error);
            }
        });

        // Position all markers
        const updateTimelineMarkerPositions = () => {
            markerElements.forEach(({ element, coordinates }) => {
                try {
                    // Check if element still exists in DOM before positioning
                    if (element && element.parentNode) {
                        const pixel = mapInstance.project(coordinates);
                        element.style.left = pixel.x + 'px';
                        element.style.top = pixel.y + 'px';
                    }
                } catch (error) {
                    console.error('❌ Error positioning timeline marker:', error);
                }
            });
        };

        // Initial positioning
        updateTimelineMarkerPositions();

        // Add event listeners for map movement
        mapInstance.on('move', updateTimelineMarkerPositions);
        mapInstance.on('zoom', updateTimelineMarkerPositions);

        console.log(`✅ Added ${markersAdded} timeline event markers to map`);
    }

    /**
     * Nuclear clear all markers - Bulletproof marker removal system
     * @returns {number} Number of markers removed
     */
    nuclearClearAllMarkers() {
        console.log('🚨 NUCLEAR CLEAR: Starting bulletproof marker clearing...');
        let totalRemoved = 0;

        // STEP 1: Clear tracked markers from our global tracking system
        if (this.trackedMarkers && this.trackedMarkers.length > 0) {
            console.log(`🚨 NUCLEAR CLEAR: Removing ${this.trackedMarkers.length} tracked markers...`);
            this.trackedMarkers.forEach((marker, index) => {
                try {
                    if (marker && marker.parentNode) {
                        marker.parentNode.removeChild(marker);
                        totalRemoved++;
                        console.log(`🚨 NUCLEAR CLEAR: Removed tracked marker ${index}`);
                    }
                } catch (error) {
                    console.error(`❌ Error removing tracked marker ${index}:`, error);
                }
            });
            this.trackedMarkers = []; // Clear the tracking array
        }

        // STEP 2: Remove all event listeners
        if (this.eventListeners && this.eventListeners.length > 0) {
            console.log(`🚨 NUCLEAR CLEAR: Removing ${this.eventListeners.length} event listeners...`);
            this.eventListeners.forEach((cleanup, index) => {
                try {
                    cleanup();
                    console.log(`🚨 NUCLEAR CLEAR: Removed event listener ${index}`);
                } catch (error) {
                    console.error(`❌ Error removing event listener ${index}:`, error);
                }
            });
            this.eventListeners = []; // Clear the listeners array
        }

        console.log(`🚨 NUCLEAR CLEAR: Completed - removed ${totalRemoved} markers total`);
        return totalRemoved;
    }

    /**
     * Count current markers on map
     * @param {Object} mapInstance - Map instance
     * @returns {number} Number of markers
     */
    countCurrentMarkers(mapInstance) {
        if (!mapInstance) return 0;

        const mapContainer = mapInstance.getContainer();
        const allMarkers = new Set();

        MARKER_SELECTORS.forEach(selector => {
            const markers = mapContainer.querySelectorAll(selector);
            if (markers.length > 0) {
                console.log(`🔍 Found ${markers.length} markers with selector: ${selector}`);
                markers.forEach(marker => allMarkers.add(marker));
            }
        });

        const totalCount = allMarkers.size;
        console.log(`🔍 TOTAL UNIQUE MARKERS ON MAP: ${totalCount}`);
        console.log(`🔍 TRACKED MARKERS: ${this.trackedMarkers.length}`);
        return totalCount;
    }
}

// Export singleton instance
export default new MarkerService();

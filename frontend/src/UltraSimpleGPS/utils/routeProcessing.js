/**
 * Route Processing Utilities
 * 
 * Functions for getting routes from OLA Maps API with fallback hierarchy
 * and processing polyline data
 */

import { API_ENDPOINTS } from '../constants';
import { calculateDistance } from './distanceCalculations';

/**
 * Get route between two coordinates using OLA Maps API with fallback hierarchy
 * @param {Object} startCoords - Start coordinates {latitude, longitude}
 * @param {Object} endCoords - End coordinates {latitude, longitude}
 * @returns {Promise<Object|null>} Route data or null
 */
export const getRouteWithOLA = async (startCoords, endCoords) => {
    console.log('🛣️ Getting route with OLA Maps:', { startCoords, endCoords });

    // Fallback hierarchy as requested:
    // 1. Directions API (if available)
    // 2. Directions Basic API
    // 3. Distance Matrix API
    // 4. Distance Matrix Basic API

    // Try Directions API first
    try {
        console.log('🎯 Trying Directions API...');
        const directionsData = await tryDirectionsAPI(startCoords, endCoords);
        if (directionsData) return directionsData;
    } catch (error) {
        console.log('⚠️ Directions API failed:', error.message);
    }

    // Try Directions Basic API
    try {
        console.log('🎯 Trying Directions Basic API...');
        const directionsBasicData = await tryDirectionsBasicAPI(startCoords, endCoords);
        if (directionsBasicData) return directionsBasicData;
    } catch (error) {
        console.log('⚠️ Directions Basic API failed:', error.message);
    }

    // Try Distance Matrix API
    try {
        console.log('🎯 Trying Distance Matrix API...');
        const distanceMatrixData = await tryDistanceMatrixAPI(startCoords, endCoords);
        if (distanceMatrixData) return distanceMatrixData;
    } catch (error) {
        console.log('⚠️ Distance Matrix API failed:', error.message);
    }

    // Try Distance Matrix Basic API
    try {
        console.log('🎯 Trying Distance Matrix Basic API...');
        const distanceMatrixBasicData = await tryDistanceMatrixBasicAPI(startCoords, endCoords);
        if (distanceMatrixBasicData) return distanceMatrixBasicData;
    } catch (error) {
        console.log('⚠️ Distance Matrix Basic API failed:', error.message);
    }

    // Final fallback: Create straight line route
    console.log('⚠️ All OLA Maps APIs failed, using straight line fallback');
    return createStraightLineRoute(startCoords, endCoords);
};

/**
 * Try Directions API
 */
const tryDirectionsAPI = async (startCoords, endCoords) => {
    const origin = `${startCoords.latitude},${startCoords.longitude}`;
    const destination = `${endCoords.latitude},${endCoords.longitude}`;
    const endpoint = `${API_ENDPOINTS.ROUTING_DIRECTIONS}?origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}`;

    try {
        const response = await fetch(endpoint, {
            headers: {
                'Content-Type': 'application/json',
                'X-Request-Id': `directions-${Date.now()}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                const data = result.data;
                if (data && data.routes && data.routes.length > 0) {
                    const route = data.routes[0];
                    return {
                        coordinates: route.geometry.coordinates,
                        distance: route.distance,
                        duration: route.duration,
                        source: 'ola_directions'
                    };
                }
            }
        }
    } catch (error) {
        console.log(`⚠️ Directions API endpoint failed: ${endpoint}`, error);
    }

    return null;
};

/**
 * Try Directions Basic API (handled by backend proxy now)
 */
const tryDirectionsBasicAPI = async (startCoords, endCoords) => {
    // Backend proxy handles both directions and directions-basic internally
    // So we skip this step to avoid duplicate calls
    console.log('🎯 Directions Basic API handled by backend proxy - skipping');
    return null;
};

/**
 * Try Distance Matrix API
 */
const tryDistanceMatrixAPI = async (startCoords, endCoords) => {
    const origins = `${startCoords.latitude},${startCoords.longitude}`;
    const destinations = `${endCoords.latitude},${endCoords.longitude}`;
    const endpoint = `${API_ENDPOINTS.ROUTING_DISTANCE_MATRIX}?origins=${encodeURIComponent(origins)}&destinations=${encodeURIComponent(destinations)}`;

    try {
        console.log('🔍 Calling Distance Matrix API via backend proxy:', endpoint);
        const response = await fetch(endpoint, {
            headers: {
                'Content-Type': 'application/json',
                'X-Request-Id': `distance-matrix-${Date.now()}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('📊 Distance Matrix API Response:', result);

            if (result.success && result.data) {
                const data = result.data;
                if (data && data.status === 'SUCCESS' && data.rows && data.rows.length > 0) {
                    const element = data.rows[0].elements[0];
                    console.log('📍 Route Element:', element);

                    if (element.status === 'OK' && element.polyline) {
                        console.log('🔍 Found polyline in response:', element.polyline.substring(0, 100) + '...');
                        // Decode polyline to get coordinates
                        const coordinates = decodePolyline(element.polyline);
                        console.log('📍 Decoded coordinates count:', coordinates.length);
                        console.log('📍 First few coordinates:', coordinates.slice(0, 3));

                        if (coordinates.length > 0) {
                            const routeData = {
                                coordinates: coordinates,
                                distance: element.distance, // in meters
                                duration: element.duration, // in seconds
                                source: 'ola_distance_matrix'
                            };
                            console.log('✅ Distance Matrix route data:', routeData);
                            return routeData;
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.log('⚠️ Distance Matrix API failed:', error);
    }
    return null;
};

/**
 * Try Distance Matrix Basic API (handled by backend proxy now)
 */
const tryDistanceMatrixBasicAPI = async (startCoords, endCoords) => {
    // Backend proxy handles both distance matrix and distance matrix basic internally
    // So we skip this step to avoid duplicate calls
    console.log('🎯 Distance Matrix Basic API handled by backend proxy - skipping');
    return null;
};

/**
 * Decode polyline string to coordinates array (Google Polyline Algorithm)
 * @param {string} polyline - Encoded polyline string
 * @returns {Array} Array of [longitude, latitude] coordinates
 */
export const decodePolyline = (polyline) => {
    try {
        console.log('🔍 Decoding polyline:', polyline.substring(0, 50) + '...');
        const coordinates = [];
        let index = 0;
        let lat = 0;
        let lng = 0;

        while (index < polyline.length) {
            // Decode latitude
            let b, shift = 0, result = 0;
            do {
                b = polyline.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            const deltaLat = ((result & 1) ? ~(result >> 1) : (result >> 1));
            lat += deltaLat;

            // Decode longitude
            shift = 0;
            result = 0;
            do {
                b = polyline.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            const deltaLng = ((result & 1) ? ~(result >> 1) : (result >> 1));
            lng += deltaLng;

            // Convert to decimal degrees and push as [longitude, latitude]
            const decodedLat = lat / 1e5;
            const decodedLng = lng / 1e5;
            coordinates.push([decodedLng, decodedLat]);
        }

        console.log(`✅ Decoded ${coordinates.length} coordinates from polyline`);
        return coordinates;
    } catch (error) {
        console.error('❌ Error decoding polyline:', error);
        return [];
    }
};

/**
 * Create a simple straight line route as fallback
 * @param {Object} startCoords - Start coordinates
 * @param {Object} endCoords - End coordinates
 * @returns {Object} Route data
 */
export const createStraightLineRoute = (startCoords, endCoords) => {
    const coordinates = [
        [startCoords.longitude, startCoords.latitude],
        [endCoords.longitude, endCoords.latitude]
    ];

    // Calculate approximate distance using Haversine formula
    const distance = calculateDistance(startCoords, endCoords);

    return {
        coordinates: coordinates,
        distance: distance * 1000, // Convert to meters
        duration: Math.round(distance / 30 * 3600), // Assume 30 km/h average speed
        source: 'straight_line'
    };
};

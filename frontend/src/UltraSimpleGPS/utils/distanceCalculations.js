/**
 * Distance Calculation Utilities
 * 
 * Pure functions for calculating distances, bearings, and related geographic calculations
 */

import { EARTH_RADIUS, CONVERSION_FACTORS } from '../constants';

/**
 * Calculate distance between two points using Haversine formula
 * @param {Object} point1 - First point with latitude and longitude
 * @param {Object} point2 - Second point with latitude and longitude
 * @param {string} unit - Unit for result ('km', 'miles', 'meters')
 * @returns {number|null} Distance in specified unit
 */
export const calculateDistanceBetweenPoints = (point1, point2, unit = 'km') => {
    if (!point1 || !point2 || !point1.latitude || !point1.longitude || !point2.latitude || !point2.longitude) {
        return null;
    }

    const R = unit === 'km' ? EARTH_RADIUS.KM : unit === 'miles' ? EARTH_RADIUS.MILES : EARTH_RADIUS.METERS;
    const lat1 = point1.latitude * Math.PI / 180;
    const lat2 = point2.latitude * Math.PI / 180;
    const deltaLat = (point2.latitude - point1.latitude) * Math.PI / 180;
    const deltaLon = (point2.longitude - point1.longitude) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
             Math.cos(lat1) * Math.cos(lat2) *
             Math.sin(deltaLon/2) * Math.sin(deltaLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in specified unit
};

/**
 * Calculate distance between two GPS coordinate arrays
 * @param {Array} point1 - [longitude, latitude]
 * @param {Array} point2 - [longitude, latitude]
 * @returns {number} Distance in kilometers
 */
export const calculateDistance = (point1, point2) => {
    const lat1 = point1[1] || point1.latitude;
    const lon1 = point1[0] || point1.longitude;
    const lat2 = point2[1] || point2.latitude;
    const lon2 = point2[0] || point2.longitude;

    const R = EARTH_RADIUS.KM;
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
};

/**
 * Calculate total distance from array of GPS points
 * @param {Array} gpsPoints - Array of [longitude, latitude] points
 * @returns {number} Total distance in kilometers
 */
export const calculateTotalDistanceFromGpsPoints = (gpsPoints) => {
    if (!gpsPoints || gpsPoints.length < 2) return 0;

    let totalDistance = 0;
    for (let i = 1; i < gpsPoints.length; i++) {
        const prev = gpsPoints[i - 1];
        const curr = gpsPoints[i];

        // GPS points are in format [longitude, latitude]
        const point1 = { latitude: prev[1], longitude: prev[0] };
        const point2 = { latitude: curr[1], longitude: curr[0] };

        const distance = calculateDistanceBetweenPoints(point1, point2, 'km');
        if (distance !== null) {
            totalDistance += distance;
        }
    }
    return totalDistance;
};

/**
 * Calculate distances between consecutive timeline events
 * @param {Array} events - Array of timeline events with latitude/longitude
 * @returns {Array} Array of distance calculations
 */
export const calculateTimelineDistances = (events) => {
    if (!events || events.length < 2) return [];

    const distances = [];

    // Sequential distances (each event to next)
    for (let i = 0; i < events.length - 1; i++) {
        const currentEvent = events[i];
        const nextEvent = events[i + 1];

        const distance = calculateDistanceBetweenPoints(
            { latitude: currentEvent.latitude, longitude: currentEvent.longitude },
            { latitude: nextEvent.latitude, longitude: nextEvent.longitude },
            'km'
        );

        if (distance !== null) {
            distances.push({
                fromIndex: i,
                toIndex: i + 1,
                fromEvent: currentEvent,
                toEvent: nextEvent,
                distance: distance,
                unit: 'km'
            });
        }
    }

    return distances;
};

/**
 * Calculate direct distance between two timeline events
 * @param {Object} fromEvent - Starting event
 * @param {Object} toEvent - Ending event
 * @returns {Object|null} Distance calculation result
 */
export const calculateDirectDistance = (fromEvent, toEvent) => {
    if (!fromEvent || !toEvent) return null;

    const distance = calculateDistanceBetweenPoints(
        { latitude: fromEvent.latitude, longitude: fromEvent.longitude },
        { latitude: toEvent.latitude, longitude: toEvent.longitude },
        'km'
    );

    if (distance !== null) {
        return {
            fromEvent,
            toEvent,
            distance,
            unit: 'km'
        };
    }

    return null;
};

/**
 * Convert distance between units
 * @param {number} meters - Distance in meters
 * @param {string} unit - Target unit ('km' or 'miles')
 * @returns {number} Converted distance
 */
export const convertDistance = (meters, unit) => {
    if (unit === 'miles') {
        return meters * CONVERSION_FACTORS.METERS_TO_MILES;
    }
    return meters / CONVERSION_FACTORS.METERS_TO_KM;
};

/**
 * Calculate bearing between two points
 * @param {Array} point1 - [longitude, latitude]
 * @param {Array} point2 - [longitude, latitude]
 * @returns {number} Bearing in degrees (0-360)
 */
export const calculateBearing = (point1, point2) => {
    const lat1 = point1[1] * Math.PI / 180;
    const lat2 = point2[1] * Math.PI / 180;
    const deltaLon = (point2[0] - point1[0]) * Math.PI / 180;

    const y = Math.sin(deltaLon) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLon);

    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
};

/**
 * Calculate precise bearing using multiple points for accuracy
 * @param {Array} points - Array of GPS points
 * @param {number} currentIndex - Current point index
 * @param {number} lookAheadDistance - Number of points to look ahead/behind
 * @returns {number} Bearing in degrees (0-360)
 */
export const calculatePreciseBearing = (points, currentIndex, lookAheadDistance = 5) => {
    if (!points || points.length < 2) return 0;

    // Use multiple points for more accurate bearing calculation
    const startIndex = Math.max(0, currentIndex - 2);
    const endIndex = Math.min(points.length - 1, currentIndex + lookAheadDistance);

    if (startIndex >= endIndex) return 0;

    const startPoint = points[startIndex];
    const endPoint = points[endIndex];

    const lat1 = startPoint[1] * Math.PI / 180;
    const lat2 = endPoint[1] * Math.PI / 180;
    const deltaLon = (endPoint[0] - startPoint[0]) * Math.PI / 180;

    const y = Math.sin(deltaLon) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLon);

    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
};

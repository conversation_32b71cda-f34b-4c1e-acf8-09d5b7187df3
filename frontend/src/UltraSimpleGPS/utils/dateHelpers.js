/**
 * Date Helper Utilities
 * 
 * Utility functions for date formatting and manipulation
 * Note: Main timezone functions are imported from existing utils/timezone.js
 */

import { 
    formatDateForDisplay, 
    navigateDate as navigateDateTZ, 
    getTimezoneHeaders 
} from '../../utils/timezone';

// Re-export timezone utilities for convenience
export { formatDateForDisplay, getTimezoneHeaders };

/**
 * Navigate date with timezone awareness
 * @param {string} currentDate - Current date string
 * @param {string} direction - 'prev' or 'next'
 * @returns {Object} Navigation result with newDate, newDateDisplay, newDateObj
 */
export const navigateDate = (currentDate, direction) => {
    return navigateDateTZ(currentDate, direction);
};

/**
 * Validate date format for API
 * @param {string} date - Date string to validate
 * @returns {string} Normalized date string
 */
export const validateAndNormalizeDate = (date) => {
    if (!date) return 'today';
    
    // Normalize common date formats
    if (date === 'Today') return 'today';
    if (date === 'Yesterday') return 'yesterday';
    
    return date;
};

/**
 * Check if date is today
 * @param {string} date - Date string
 * @returns {boolean} True if date is today
 */
export const isToday = (date) => {
    const normalizedDate = validateAndNormalizeDate(date);
    return normalizedDate === 'today' || normalizedDate === 'Today';
};

/**
 * Check if date is yesterday
 * @param {string} date - Date string
 * @returns {boolean} True if date is yesterday
 */
export const isYesterday = (date) => {
    const normalizedDate = validateAndNormalizeDate(date);
    return normalizedDate === 'yesterday' || normalizedDate === 'Yesterday';
};

/**
 * Format date for display with fallback
 * @param {string} date - Date string
 * @returns {string} Formatted date string
 */
export const formatDateForDisplayWithFallback = (date) => {
    try {
        if (isToday(date)) {
            return 'Today';
        }
        
        if (isYesterday(date)) {
            return 'Yesterday';
        }
        
        // For other dates, use timezone-aware formatting
        const dateObj = new Date(date + 'T00:00:00');
        return formatDateForDisplay(dateObj);
    } catch (error) {
        console.error('Error formatting date:', error);
        return date || 'Unknown Date';
    }
};

/**
 * Calculate duration between two timestamps
 * @param {string|Date} startTime - Start timestamp
 * @param {string|Date} endTime - End timestamp
 * @returns {string} Formatted duration string
 */
export const calculateDuration = (startTime, endTime) => {
    try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        const durationMs = end - start;
        
        if (durationMs < 0) return '0m';
        
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    } catch (error) {
        console.error('Error calculating duration:', error);
        return '0m';
    }
};

/**
 * Calculate time difference in milliseconds
 * @param {string|Date} time1 - First timestamp
 * @param {string|Date} time2 - Second timestamp
 * @returns {number} Time difference in milliseconds
 */
export const calculateTimeDifference = (time1, time2) => {
    try {
        const date1 = new Date(time1);
        const date2 = new Date(time2);
        return Math.abs(date2 - date1);
    } catch (error) {
        console.error('Error calculating time difference:', error);
        return 0;
    }
};

/**
 * Format timestamp for display
 * @param {string|Date} timestamp - Timestamp to format
 * @returns {string} Formatted timestamp
 */
export const formatTimestamp = (timestamp) => {
    try {
        const date = new Date(timestamp);
        return date.toLocaleString();
    } catch (error) {
        console.error('Error formatting timestamp:', error);
        return 'Invalid Date';
    }
};

/**
 * Get current date string for API
 * @returns {string} Current date string
 */
export const getCurrentDateString = () => {
    return 'today';
};

/**
 * Get yesterday date string for API
 * @returns {string} Yesterday date string
 */
export const getYesterdayDateString = () => {
    return 'yesterday';
};

/**
 * Check if a date string represents a relative date (today/yesterday)
 * @param {string} date - Date string
 * @returns {boolean} True if relative date
 */
export const isRelativeDate = (date) => {
    const normalizedDate = validateAndNormalizeDate(date);
    return normalizedDate === 'today' || normalizedDate === 'yesterday';
};

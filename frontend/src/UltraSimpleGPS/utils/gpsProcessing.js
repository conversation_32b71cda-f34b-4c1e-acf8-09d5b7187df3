/**
 * GPS Processing Utilities
 * 
 * Functions for cleaning GPS data, removing noise, and snapping to roads
 */

import { calculateDistance } from './distanceCalculations';
import { PERFORMANCE_CONFIG } from '../constants';

/**
 * Clean and snap GPS points to roads - Remove noise and snap to roads
 * @param {Array} rawPoints - Array of raw GPS points [longitude, latitude]
 * @param {number} tolerance - Tolerance for Douglas-<PERSON> algorithm
 * @returns {Array} Cleaned GPS points
 */
export const cleanAndSnapGPSPoints = (rawPoints, tolerance = PERFORMANCE_CONFIG.GPS_CLEANING_TOLERANCE) => {
    if (!rawPoints || rawPoints.length < 3) return rawPoints;

    console.log(`🧹 Starting GPS cleaning for ${rawPoints.length} raw points...`);

    // Step 1: Remove obvious GPS noise and outliers
    const removeOutliers = (points) => {
        if (points.length < 3) return points;

        const cleanedPoints = [points[0]]; // Always keep first point

        for (let i = 1; i < points.length - 1; i++) {
            const prev = points[i - 1];
            const current = points[i];
            const next = points[i + 1];

            // Calculate distances
            const distToPrev = calculateDistance(prev, current);
            const distToNext = calculateDistance(current, next);
            const directDist = calculateDistance(prev, next);

            // Remove point if it creates unrealistic detour (GPS noise) or unwanted straight lines
            const detourRatio = (distToPrev + distToNext) / directDist;
            const maxDistancePerPoint = (PERFORMANCE_CONFIG.MAX_SPEED_KMH * 1000 / 3600) * PERFORMANCE_CONFIG.TIME_GAP_SECONDS; // meters

            // Additional check for maximum distance jumps to prevent unwanted straight lines
            const maxJumpDistance = PERFORMANCE_CONFIG.MAX_DISTANCE_JUMP_METERS || 500;
            const distToPrevMeters = distToPrev * 1000; // Convert to meters
            const distToNextMeters = distToNext * 1000; // Convert to meters

            // Keep point if it's reasonable and doesn't create unwanted straight lines
            if (detourRatio < PERFORMANCE_CONFIG.DETOUR_RATIO_THRESHOLD &&
                distToPrev < maxDistancePerPoint &&
                distToNext < maxDistancePerPoint &&
                distToPrevMeters < maxJumpDistance &&
                distToNextMeters < maxJumpDistance) {
                cleanedPoints.push(current);
            }
        }

        cleanedPoints.push(points[points.length - 1]); // Always keep last point
        return cleanedPoints;
    };

    // Step 2: Douglas-Peucker algorithm for line simplification
    const douglasPeucker = (points, tolerance) => {
        if (points.length <= 2) return points;

        // Find the point with maximum distance from line between first and last
        let maxDistance = 0;
        let maxIndex = 0;
        const firstPoint = points[0];
        const lastPoint = points[points.length - 1];

        for (let i = 1; i < points.length - 1; i++) {
            const distance = perpendicularDistance(points[i], firstPoint, lastPoint);
            if (distance > maxDistance) {
                maxDistance = distance;
                maxIndex = i;
            }
        }

        // If max distance is greater than tolerance, recursively simplify
        if (maxDistance > tolerance) {
            const leftPoints = douglasPeucker(points.slice(0, maxIndex + 1), tolerance);
            const rightPoints = douglasPeucker(points.slice(maxIndex), tolerance);
            return leftPoints.slice(0, -1).concat(rightPoints);
        } else {
            return [firstPoint, lastPoint];
        }
    };

    // Step 3: Calculate perpendicular distance from point to line
    const perpendicularDistance = (point, lineStart, lineEnd) => {
        const [x, y] = point;
        const [x1, y1] = lineStart;
        const [x2, y2] = lineEnd;

        const A = x - x1;
        const B = y - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        const param = dot / lenSq;
        let xx, yy;

        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = x - xx;
        const dy = y - yy;
        return Math.sqrt(dx * dx + dy * dy);
    };

    // Step 4: Remove obvious straight-line jumps (unwanted straight lines)
    const removeStraightLineJumps = (points) => {
        if (points.length < 3) return points;

        const filteredPoints = [points[0]]; // Always keep first point

        for (let i = 1; i < points.length - 1; i++) {
            const prev = points[i - 1];
            const current = points[i];
            const next = points[i + 1];

            // Calculate distance from previous to current
            const distToCurrent = calculateDistance(prev, current) * 1000; // meters

            // If distance is too large, it's likely a GPS jump creating unwanted straight line
            const maxJumpDistance = PERFORMANCE_CONFIG.MAX_DISTANCE_JUMP_METERS || 500;

            if (distToCurrent < maxJumpDistance) {
                filteredPoints.push(current);
            }
        }

        filteredPoints.push(points[points.length - 1]); // Always keep last point
        return filteredPoints;
    };

    // Apply cleaning steps
    const cleanedPoints = removeOutliers(rawPoints);
    const jumpFilteredPoints = removeStraightLineJumps(cleanedPoints);
    const simplifiedPoints = douglasPeucker(jumpFilteredPoints, tolerance);

    return simplifiedPoints;
};

/**
 * Validate GPS point
 * @param {Array} point - GPS point [longitude, latitude]
 * @returns {boolean} True if valid
 */
export const isValidGPSPoint = (point) => {
    if (!point || !Array.isArray(point) || point.length < 2) return false;
    
    const [longitude, latitude] = point;
    
    return (
        typeof longitude === 'number' &&
        typeof latitude === 'number' &&
        longitude >= -180 && longitude <= 180 &&
        latitude >= -90 && latitude <= 90 &&
        !isNaN(longitude) && !isNaN(latitude)
    );
};

/**
 * Filter out invalid GPS points
 * @param {Array} points - Array of GPS points
 * @returns {Array} Filtered valid points
 */
export const filterValidGPSPoints = (points) => {
    if (!points || !Array.isArray(points)) return [];
    
    const validPoints = points.filter(isValidGPSPoint);
    
    if (validPoints.length !== points.length) {
        console.log(`🔍 Filtered GPS points: ${points.length} → ${validPoints.length} (removed ${points.length - validPoints.length} invalid points)`);
    }
    
    return validPoints;
};

/**
 * Calculate bounds for GPS points
 * @param {Array} points - Array of GPS points [longitude, latitude]
 * @returns {Array} Bounds [[minLng, minLat], [maxLng, maxLat]]
 */
export const calculateGPSBounds = (points) => {
    if (!points || points.length === 0) return null;
    
    const validPoints = filterValidGPSPoints(points);
    if (validPoints.length === 0) return null;
    
    const lngs = validPoints.map(p => p[0]);
    const lats = validPoints.map(p => p[1]);
    
    return [
        [Math.min(...lngs), Math.min(...lats)],
        [Math.max(...lngs), Math.max(...lats)]
    ];
};

/**
 * Interpolate point between two GPS points
 * @param {Array} point1 - First GPS point [longitude, latitude]
 * @param {Array} point2 - Second GPS point [longitude, latitude]
 * @param {number} ratio - Interpolation ratio (0-1)
 * @returns {Array} Interpolated point [longitude, latitude]
 */
export const interpolateGPSPoint = (point1, point2, ratio) => {
    if (!isValidGPSPoint(point1) || !isValidGPSPoint(point2)) return null;
    if (ratio < 0 || ratio > 1) return null;
    
    const [lng1, lat1] = point1;
    const [lng2, lat2] = point2;
    
    return [
        lng1 + (lng2 - lng1) * ratio,
        lat1 + (lat2 - lat1) * ratio
    ];
};

/**
 * Smooth GPS points using moving average
 * @param {Array} points - Array of GPS points
 * @param {number} windowSize - Size of smoothing window
 * @returns {Array} Smoothed GPS points
 */
export const smoothGPSPoints = (points, windowSize = 3) => {
    if (!points || points.length <= windowSize) return points;
    
    const smoothedPoints = [];
    const halfWindow = Math.floor(windowSize / 2);
    
    for (let i = 0; i < points.length; i++) {
        const start = Math.max(0, i - halfWindow);
        const end = Math.min(points.length - 1, i + halfWindow);
        
        let avgLng = 0;
        let avgLat = 0;
        let count = 0;
        
        for (let j = start; j <= end; j++) {
            if (isValidGPSPoint(points[j])) {
                avgLng += points[j][0];
                avgLat += points[j][1];
                count++;
            }
        }
        
        if (count > 0) {
            smoothedPoints.push([avgLng / count, avgLat / count]);
        } else {
            smoothedPoints.push(points[i]);
        }
    }
    
    return smoothedPoints;
};

/**
 * Remove duplicate consecutive GPS points
 * @param {Array} points - Array of GPS points
 * @param {number} threshold - Minimum distance threshold in meters
 * @returns {Array} Deduplicated GPS points
 */
export const removeDuplicateGPSPoints = (points, threshold = 1) => {
    if (!points || points.length <= 1) return points;
    
    const deduplicatedPoints = [points[0]];
    
    for (let i = 1; i < points.length; i++) {
        const prevPoint = deduplicatedPoints[deduplicatedPoints.length - 1];
        const currentPoint = points[i];
        
        const distance = calculateDistance(prevPoint, currentPoint) * 1000; // Convert to meters
        
        if (distance >= threshold) {
            deduplicatedPoints.push(currentPoint);
        }
    }
    
    if (deduplicatedPoints.length !== points.length) {
        console.log(`🔍 Removed duplicate GPS points: ${points.length} → ${deduplicatedPoints.length} (removed ${points.length - deduplicatedPoints.length} duplicates)`);
    }
    
    return deduplicatedPoints;
};

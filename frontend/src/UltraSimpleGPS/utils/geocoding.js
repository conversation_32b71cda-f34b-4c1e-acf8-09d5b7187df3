/**
 * Geocoding Utilities
 * 
 * Functions for converting addresses to coordinates and vice versa
 * using OSM (free) and OLA Maps (fallback) services
 */

import { EXTERNAL_APIS, OLA_MAPS_CONFIG } from '../constants';

/**
 * Geocode address using OpenStreetMap Nominatim (free service)
 * @param {string} address - Address to geocode
 * @returns {Promise<Object|null>} Geocoding result or null
 */
export const geocodeWithOSM = async (address) => {
    try {
        console.log('🌍 Geocoding with OSM:', address);
        const encodedAddress = encodeURIComponent(address);
        const response = await fetch(`${EXTERNAL_APIS.OSM_GEOCODING}?format=json&q=${encodedAddress}&limit=1`);

        if (!response.ok) {
            throw new Error(`OSM geocoding failed: ${response.status}`);
        }

        const data = await response.json();
        if (data && data.length > 0) {
            const result = {
                latitude: parseFloat(data[0].lat),
                longitude: parseFloat(data[0].lon),
                display_name: data[0].display_name,
                source: 'osm'
            };
            console.log('✅ OSM geocoding success:', result);
            return result;
        }

        console.log('⚠️ OSM geocoding: No results found');
        return null;
    } catch (error) {
        console.error('❌ OSM geocoding error:', error);
        throw error;
    }
};

/**
 * Geocode address using OLA Maps API
 * @param {string} address - Address to geocode
 * @returns {Promise<Object|null>} Geocoding result or null
 */
export const geocodeWithOLA = async (address) => {
    try {
        console.log('🗺️ Geocoding with OLA Maps:', address);
        const response = await fetch(`${EXTERNAL_APIS.OLA_MAPS.GEOCODING}?address=${encodeURIComponent(address)}&api_key=${OLA_MAPS_CONFIG.API_KEY}`, {
            headers: {
                'X-Request-Id': `geocode-${Date.now()}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`OLA geocoding failed: ${response.status}`);
        }

        const data = await response.json();
        if (data && data.geocodingResults && data.geocodingResults.length > 0) {
            const result = data.geocodingResults[0];
            const geocoded = {
                latitude: result.geometry.location.lat,
                longitude: result.geometry.location.lng,
                display_name: result.name || result.formatted_address,
                source: 'ola'
            };
            console.log('✅ OLA geocoding success:', geocoded);
            return geocoded;
        }

        console.log('⚠️ OLA geocoding: No results found');
        return null;
    } catch (error) {
        console.error('❌ OLA geocoding error:', error);
        throw error;
    }
};

/**
 * Geocode address with fallback (OSM first, then OLA Maps)
 * @param {string} address - Address to geocode
 * @returns {Promise<Object|null>} Geocoding result or null
 */
export const geocodeAddress = async (address) => {
    if (!address || address.trim() === '') {
        throw new Error('Address is required for geocoding');
    }

    try {
        // Try OSM first (free)
        return await geocodeWithOSM(address);
    } catch (osmError) {
        console.log('⚠️ OSM failed, trying OLA Maps fallback...');
        try {
            // Fallback to OLA Maps
            return await geocodeWithOLA(address);
        } catch (olaError) {
            console.error('❌ Both geocoding services failed:', { osmError, olaError });
            return null;
        }
    }
};

/**
 * Geocode multiple timeline events
 * @param {Array} timelineEvents - Array of timeline events
 * @returns {Promise<Array>} Array of geocoded events
 */
export const geocodeTimelineEvents = async (timelineEvents) => {
    console.log('🌍 Geocoding timeline events...');
    const geocodedEvents = [];

    for (const event of timelineEvents) {
        try {
            // Skip if already has address or no coordinates
            if (event.address || !event.latitude || !event.longitude) {
                geocodedEvents.push(event);
                continue;
            }

            // Create address string from coordinates for reverse geocoding
            const addressQuery = `${event.latitude},${event.longitude}`;
            
            // Try to get location name
            const geocoded = await geocodeAddress(addressQuery);
            
            if (geocoded && geocoded.display_name) {
                geocodedEvents.push({
                    ...event,
                    address: geocoded.display_name,
                    geocoded_source: geocoded.source
                });
                console.log(`✅ Geocoded event ${event.id}: ${geocoded.display_name}`);
            } else {
                // Fallback to coordinates if geocoding fails
                geocodedEvents.push({
                    ...event,
                    address: `${event.latitude.toFixed(6)}, ${event.longitude.toFixed(6)}`,
                    geocoded_source: 'coordinates'
                });
                console.log(`⚠️ Geocoding failed for event ${event.id}, using coordinates`);
            }

            // Small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
            console.error(`❌ Error geocoding event ${event.id}:`, error);
            // Add event with coordinates as fallback
            geocodedEvents.push({
                ...event,
                address: event.latitude && event.longitude 
                    ? `${event.latitude.toFixed(6)}, ${event.longitude.toFixed(6)}`
                    : 'Location unavailable',
                geocoded_source: 'error'
            });
        }
    }

    console.log(`✅ Geocoded ${geocodedEvents.length} timeline events`);
    return geocodedEvents;
};

/**
 * Reverse geocode coordinates to get address
 * @param {number} latitude - Latitude
 * @param {number} longitude - Longitude
 * @returns {Promise<string>} Address string
 */
export const reverseGeocode = async (latitude, longitude) => {
    try {
        const result = await geocodeAddress(`${latitude},${longitude}`);
        return result ? result.display_name : `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    } catch (error) {
        console.error('❌ Reverse geocoding error:', error);
        return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
};

/**
 * Save geocoded name to database for future use
 * @param {string} eventId - Timeline event ID
 * @param {string} geocodedName - The geocoded location name
 * @returns {Promise<boolean>} Success status
 */
export const saveGeocodedName = async (eventId, geocodedName) => {
    try {
        console.log('💾 Saving geocoded name to database:', eventId, geocodedName);

        const response = await fetch('/api/geocoding/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event_id: eventId,
                geocoded_name: geocodedName
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to save geocoded name: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Geocoded name saved successfully:', result);
        return true;

    } catch (error) {
        console.error('❌ Failed to save geocoded name:', error);
        return false;
    }
};

/**
 * Get location name for timeline event
 * @param {Object} event - Timeline event
 * @returns {string} Location name or coordinates
 */
export const getLocationName = (event) => {
    if (event.address) {
        return event.address;
    }

    if (event.latitude && event.longitude) {
        return `${event.latitude.toFixed(6)}, ${event.longitude.toFixed(6)}`;
    }

    return 'Location not available';
};

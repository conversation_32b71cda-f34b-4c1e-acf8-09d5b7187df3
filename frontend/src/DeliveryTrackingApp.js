import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import MarkerClusteringMap from './MarkerClusteringMap';
import UltraSimpleGPS from './UltraSimpleGPS';
import TemperatureDashboard from './TemperatureMonitoring/TemperatureDashboard';
import DeliveryPredictionsDashboard from './DeliveryPredictions/DeliveryPredictionsDashboard';

const DeliveryTrackingApp = () => {
    const { agentId: urlAgentId } = useParams();
    const navigate = useNavigate();

    const [currentView, setCurrentView] = useState('clustering'); // 'clustering', 'tracking', 'temperature', or 'predictions'
    const [selectedAgentId, setSelectedAgentId] = useState(null);
    const [selectedAgentData, setSelectedAgentData] = useState(null);
    const [agentProfile, setAgentProfile] = useState(null);

    // In production, this would come from authentication/user context
    const viewerId = "00000000-0000-0000-0000-000000000001"; // Default viewer ID for demo

    // Initialize view based on URL
    useEffect(() => {
        if (urlAgentId) {
            console.log('🔗 URL contains agent ID:', urlAgentId);
            setSelectedAgentId(urlAgentId);
            setCurrentView('tracking');
        } else {
            setCurrentView('clustering');
            setSelectedAgentId(null);
            setSelectedAgentData(null);
            setAgentProfile(null);
        }
    }, [urlAgentId]);

    // Handle agent selection from marker clustering
    const handleAgentSelect = (agentId, agentData = null) => {
        console.log('🎯 Agent selected from clustering:', agentId, agentData);
        setSelectedAgentId(agentId);
        setSelectedAgentData(agentData);
        setCurrentView('tracking');
        // Navigate to the agent-specific URL
        navigate(`/agent/${agentId}`);
    };

    // Handle back to clustering view
    const handleBackToClustering = () => {
        console.log('🔙 Returning to clustering view');
        setCurrentView('clustering');
        setSelectedAgentId(null);
        setSelectedAgentData(null);
        setAgentProfile(null);
        // Navigate back to home URL
        navigate('/');
    };

    // Handle agent profile load
    const handleAgentProfileLoad = (profile) => {
        console.log('👤 Agent profile loaded:', profile);
        console.log('👤 Agent name from profile:', profile?.name);
        setAgentProfile(profile);
    };

    // Generate profile photo URL for agent
    const generateProfilePhotoUrl = (agentId) => {
        if (!agentId) return 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face';
        const photoIndex = Math.abs(agentId.split('-')[0].charCodeAt(0)) % 10;
        return `https://images.unsplash.com/photo-150700321116${photoIndex}?w=100&h=100&fit=crop&crop=face`;
    };

    // Get agent initials for fallback display
    const getAgentInitials = (agentId, name) => {
        if (name) {
            return name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('');
        }
        if (!agentId) return 'AG';
        const parts = agentId.split('-');
        if (parts.length >= 2) {
            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
        }
        return agentId.substring(0, 2).toUpperCase();
    };

    return (
        <div style={{ width: '100%', height: '100vh', position: 'relative' }}>
            {currentView === 'clustering' ? (
                // Marker Clustering View - Clean map view without enterprise buttons
                <MarkerClusteringMap onUserSelect={handleAgentSelect} />
            ) : currentView === 'temperature' ? (
                // Temperature Monitoring View
                <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    {/* Back Button */}
                    <button
                        onClick={handleBackToClustering}
                        style={{
                            position: 'absolute',
                            top: '20px',
                            left: '20px',
                            zIndex: 1000,
                            backgroundColor: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '14px 18px',
                            cursor: 'pointer',
                            fontSize: '15px',
                            fontWeight: '600',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            height: '48px',
                            minWidth: '200px'
                        }}
                    >
                        ← Back to Map Overview
                    </button>

                    {/* Temperature Monitoring Button */}
                    <button
                        onClick={() => setCurrentView('tracking')}
                        style={{
                            position: 'absolute',
                            top: '20px',
                            left: '232px',
                            zIndex: 1000,
                            backgroundColor: '#28a745',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '14px 18px',
                            cursor: 'pointer',
                            fontSize: '15px',
                            fontWeight: '600',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            height: '48px',
                            minWidth: '200px'
                        }}
                    >
                        📍 Switch to GPS Tracking
                    </button>

                    {/* Temperature Dashboard Component */}
                    <TemperatureDashboard
                        agentId={selectedAgentId}
                    />
                </div>
            ) : currentView === 'predictions' ? (
                // Delivery Predictions View
                <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    {/* Back Button */}
                    <button
                        onClick={handleBackToClustering}
                        style={{
                            position: 'absolute',
                            top: '20px',
                            left: '20px',
                            zIndex: 1000,
                            backgroundColor: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '14px 18px',
                            cursor: 'pointer',
                            fontSize: '15px',
                            fontWeight: '600',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            height: '48px',
                            minWidth: '200px'
                        }}
                    >
                        ← Back to Map Overview
                    </button>

                    {/* Switch to Other Views */}
                    <button
                        onClick={() => setCurrentView('temperature')}
                        style={{
                            position: 'absolute',
                            top: '20px',
                            left: '232px',
                            zIndex: 1000,
                            backgroundColor: '#dc3545',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '14px 18px',
                            cursor: 'pointer',
                            fontSize: '15px',
                            fontWeight: '600',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            height: '48px',
                            minWidth: '200px'
                        }}
                    >
                        🌡️ Temperature Monitor
                    </button>

                    <button
                        onClick={() => setCurrentView('tracking')}
                        style={{
                            position: 'absolute',
                            top: '20px',
                            left: '444px',
                            zIndex: 1000,
                            backgroundColor: '#28a745',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '14px 18px',
                            cursor: 'pointer',
                            fontSize: '15px',
                            fontWeight: '600',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            height: '48px',
                            minWidth: '200px'
                        }}
                    >
                        📍 GPS Tracking
                    </button>

                    {/* Delivery Predictions Dashboard Component */}
                    <DeliveryPredictionsDashboard
                        agentId={selectedAgentId}
                    />
                </div>
            ) : (
                // Individual Agent Tracking View
                <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    {/* Back Button */}
                    <button
                        onClick={handleBackToClustering}
                        style={{
                            position: 'absolute',
                            top: '20px',
                            left: '20px',
                            zIndex: 1000,
                            backgroundColor: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '14px 18px',
                            cursor: 'pointer',
                            fontSize: '15px',
                            fontWeight: '600',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            height: '48px',
                            minWidth: '200px'
                        }}
                    >
                        ← Back to Map Overview
                    </button>

                    {/* Agent Info Header */}
                    <div style={{
                        position: 'absolute',
                        top: '20px',
                        left: '232px',
                        zIndex: 1000,
                        backgroundColor: 'white',
                        padding: '8px 16px',
                        borderRadius: '8px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        fontSize: '14px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                        height: '48px',
                        minWidth: '250px'
                    }}>
                        {/* Profile Picture */}
                        <div style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '50%',
                            overflow: 'hidden',
                            backgroundColor: '#4285F4',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontWeight: 'bold',
                            fontSize: '12px',
                            flexShrink: 0
                        }}>
                            <img
                                src={generateProfilePhotoUrl(selectedAgentId)}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                    display: 'block'
                                }}
                                onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.parentElement.innerHTML = getAgentInitials(selectedAgentId, agentProfile?.name || selectedAgentData?.name);
                                }}
                                alt="Agent Profile"
                            />
                        </div>

                        {/* Agent Name and Info */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                            <div style={{
                                fontWeight: 'bold',
                                fontSize: '15px',
                                color: '#333',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                            }}>
                                {agentProfile?.name || selectedAgentData?.name || `Agent ${selectedAgentId?.substring(0, 8)}...`}
                            </div>
                            <div style={{ fontSize: '11px', color: '#666' }}>
                                📍 Agent Tracking
                            </div>
                        </div>
                    </div>

                    {/* Individual Agent Tracking Component */}
                    <UltraSimpleGPS
                        agentId={urlAgentId || selectedAgentId || "3b35ff6d-d482-4cb5-bbc1-ed78774c59b2"}
                        viewerId={viewerId}
                        onAgentProfileLoad={handleAgentProfileLoad}
                    />
                </div>
            )}
        </div>
    );
};

export default DeliveryTrackingApp;

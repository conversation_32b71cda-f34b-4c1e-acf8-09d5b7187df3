// 🌍 ENTERPRISE-G<PERSON>DE TIMEZONE UTILITIES
// Handles user timezone detection and UTC conversion for global applications

/**
 * Get user's timezone information
 */
export const getUserTimezone = () => {
    try {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const offset = new Date().getTimezoneOffset();
        const offsetHours = Math.abs(offset / 60);
        const offsetMinutes = Math.abs(offset % 60);
        const offsetSign = offset <= 0 ? '+' : '-';
        
        return {
            timezone,
            offset,
            offsetString: `${offsetSign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`,
            name: timezone.split('/').pop().replace('_', ' ')
        };
    } catch (error) {
        console.warn('Failed to detect timezone, using UTC:', error);
        return {
            timezone: 'UTC',
            offset: 0,
            offsetString: '+00:00',
            name: 'UTC'
        };
    }
};

/**
 * Get current date in user's timezone
 */
export const getCurrentDateInUserTimezone = () => {
    const now = new Date();
    const userTimezone = getUserTimezone();
    
    // Create date in user's timezone
    const userDate = new Date(now.toLocaleString("en-US", {timeZone: userTimezone.timezone}));
    
    console.log('🌍 TIMEZONE INFO:', {
        userTimezone: userTimezone.timezone,
        serverTime: now.toISOString(),
        userTime: userDate.toISOString(),
        offset: userTimezone.offsetString
    });
    
    return userDate;
};

/**
 * Get yesterday's date in user's timezone
 */
export const getYesterdayInUserTimezone = () => {
    const today = getCurrentDateInUserTimezone();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday;
};

/**
 * Format date for API (YYYY-MM-DD in user's timezone)
 */
export const formatDateForAPI = (date) => {
    const userTimezone = getUserTimezone();
    const today = getCurrentDateInUserTimezone();
    const yesterday = getYesterdayInUserTimezone();

    // Normalize dates to compare only date part (not time)
    const normalizeDate = (d) => {
        const normalized = new Date(d.toLocaleString("en-US", {timeZone: userTimezone.timezone}));
        return new Date(normalized.getFullYear(), normalized.getMonth(), normalized.getDate());
    };
    
    const normalizedDate = normalizeDate(date);
    const normalizedToday = normalizeDate(today);
    const normalizedYesterday = normalizeDate(yesterday);

    if (normalizedDate.getTime() === normalizedToday.getTime()) {
        return 'today';
    } else if (normalizedDate.getTime() === normalizedYesterday.getTime()) {
        return 'yesterday';
    } else {
        // Format in user's timezone
        const userDate = new Date(date.toLocaleString("en-US", {timeZone: userTimezone.timezone}));
        const year = userDate.getFullYear();
        const month = String(userDate.getMonth() + 1).padStart(2, '0');
        const day = String(userDate.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
};

/**
 * Format date for display (user-friendly format in user's timezone)
 */
export const formatDateForDisplay = (date) => {
    // Handle special string cases
    if (date === 'today') {
        return 'Today';
    }
    if (date === 'yesterday') {
        return 'Yesterday';
    }

    // Convert string to Date object if needed
    let dateObj;
    if (typeof date === 'string') {
        dateObj = new Date(date);
        // Check if it's a valid date
        if (isNaN(dateObj.getTime())) {
            return date; // Return original string if invalid date
        }
    } else if (date instanceof Date) {
        dateObj = date;
    } else {
        return 'Invalid Date';
    }

    const userTimezone = getUserTimezone();
    const today = getCurrentDateInUserTimezone();
    const yesterday = getYesterdayInUserTimezone();

    // Normalize dates to compare only date part (not time)
    const normalizeDate = (d) => {
        const normalized = new Date(d.toLocaleString("en-US", {timeZone: userTimezone.timezone}));
        return new Date(normalized.getFullYear(), normalized.getMonth(), normalized.getDate());
    };

    const normalizedDate = normalizeDate(dateObj);
    const normalizedToday = normalizeDate(today);
    const normalizedYesterday = normalizeDate(yesterday);

    if (normalizedDate.getTime() === normalizedToday.getTime()) {
        return 'Today';
    } else if (normalizedDate.getTime() === normalizedYesterday.getTime()) {
        return 'Yesterday';
    } else {
        // Format in user's timezone with nice display
        const options = {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: normalizedDate.getFullYear() !== normalizedToday.getFullYear() ? 'numeric' : undefined,
            timeZone: userTimezone.timezone
        };
        return dateObj.toLocaleDateString('en-US', options);
    }
};

/**
 * Parse current date string to Date object in user's timezone
 */
export const parseCurrentDate = (currentDate) => {
    const userTimezone = getUserTimezone();
    const today = getCurrentDateInUserTimezone();
    
    if (currentDate === 'today' || currentDate === 'Today') {
        return today;
    } else if (currentDate === 'yesterday' || currentDate === 'Yesterday') {
        return getYesterdayInUserTimezone();
    } else {
        // Try to parse custom date format in user's timezone
        const parsedDate = new Date(currentDate + 'T00:00:00');
        if (isNaN(parsedDate.getTime())) {
            console.warn(`Invalid date format: ${currentDate}, defaulting to today`);
            return today;
        }
        
        // Convert to user's timezone
        const userDate = new Date(parsedDate.toLocaleString("en-US", {timeZone: userTimezone.timezone}));
        return userDate;
    }
};

/**
 * Navigate to previous/next date in user's timezone
 */
export const navigateDate = (currentDate, direction) => {
    const currentDateObj = parseCurrentDate(currentDate);
    
    // Validate the current date object
    if (isNaN(currentDateObj.getTime())) {
        console.error('Invalid current date object, defaulting to today');
        return { newDate: 'today', newDateDisplay: 'Today' };
    }

    let newDateObj;
    const userTimezone = getUserTimezone();

    if (direction === 'prev') {
        // Go to previous day in user's timezone
        newDateObj = new Date(currentDateObj);
        newDateObj.setDate(currentDateObj.getDate() - 1);
    } else {
        // Go to next day in user's timezone
        newDateObj = new Date(currentDateObj);
        newDateObj.setDate(currentDateObj.getDate() + 1);
    }

    // Format new date
    const newDate = formatDateForAPI(newDateObj);
    const newDateDisplay = formatDateForDisplay(newDateObj);

    console.log(`🌍 TIMEZONE-AWARE NAVIGATION:`, {
        userTimezone: userTimezone.timezone,
        direction: direction,
        currentDate: currentDate,
        currentDateObj: currentDateObj.toDateString(),
        newDateObj: newDateObj.toDateString(),
        newDate: newDate,
        newDateDisplay: newDateDisplay
    });

    return { newDate, newDateDisplay, newDateObj };
};

/**
 * Get timezone info for API headers
 */
export const getTimezoneHeaders = () => {
    const userTimezone = getUserTimezone();
    return {
        'X-User-Timezone': userTimezone.timezone,
        'X-User-Timezone-Offset': userTimezone.offsetString
    };
};

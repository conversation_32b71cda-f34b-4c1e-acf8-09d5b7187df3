import React, { useState, useEffect } from 'react';
import './DeliveryPredictionsDashboard.css';

const DeliveryPredictionsDashboard = ({ agentId }) => {
    const [etaCalculation, setEtaCalculation] = useState(null);
    const [deliveryStats, setDeliveryStats] = useState(null);
    const [agentPerformance, setAgentPerformance] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // ETA calculation form state
    const [etaForm, setEtaForm] = useState({
        pickup_latitude: '',
        pickup_longitude: '',
        delivery_latitude: '',
        delivery_longitude: '',
        agent_id: agentId || '',
        time_of_day: '',
        day_of_week: ''
    });

    useEffect(() => {
        fetchDeliveryStats();
        if (agentId) {
            fetchAgentPerformance(agentId);
            setEtaForm(prev => ({ ...prev, agent_id: agentId }));
        }
    }, [agentId]);

    const fetchDeliveryStats = async () => {
        try {
            const params = new URLSearchParams();
            if (agentId) params.append('agent_id', agentId);
            params.append('days', '30');

            const response = await fetch(`/api/delivery-predictions/stats?${params}`);
            if (!response.ok) throw new Error('Failed to fetch delivery stats');

            const result = await response.json();
            setDeliveryStats(result.data);
        } catch (err) {
            console.error('Error fetching delivery stats:', err);
        }
    };

    const fetchAgentPerformance = async (agentId) => {
        try {
            const response = await fetch(`/api/delivery-predictions/agent-performance/${agentId}?days=30`);
            if (!response.ok) throw new Error('Failed to fetch agent performance');

            const result = await response.json();
            setAgentPerformance(result.data);
        } catch (err) {
            console.error('Error fetching agent performance:', err);
        }
    };

    const calculateETA = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            const response = await fetch('/api/delivery-predictions/eta', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    pickup_latitude: parseFloat(etaForm.pickup_latitude),
                    pickup_longitude: parseFloat(etaForm.pickup_longitude),
                    delivery_latitude: parseFloat(etaForm.delivery_latitude),
                    delivery_longitude: parseFloat(etaForm.delivery_longitude),
                    agent_id: etaForm.agent_id || undefined,
                    time_of_day: etaForm.time_of_day || undefined,
                    day_of_week: etaForm.day_of_week || undefined
                })
            });

            if (!response.ok) throw new Error('Failed to calculate ETA');

            const result = await response.json();
            setEtaCalculation(result.data);
        } catch (err) {
            setError(err.message);
            console.error('Error calculating ETA:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEtaForm(prev => ({ ...prev, [name]: value }));
    };

    const formatDuration = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    };

    const getConfidenceColor = (confidence) => {
        if (confidence >= 80) return '#28a745';
        if (confidence >= 60) return '#ffc107';
        return '#dc3545';
    };

    return (
        <div className="delivery-predictions-dashboard">
            <div className="dashboard-header">
                <h2>🚚 Delivery Time Predictions</h2>
                <div className="subtitle">E-commerce Delivery Optimization</div>
            </div>

            {/* ETA Calculator */}
            <div className="eta-calculator-section">
                <h3>📍 ETA Calculator</h3>
                <form onSubmit={calculateETA} className="eta-form">
                    <div className="form-row">
                        <div className="form-group">
                            <label>Pickup Latitude</label>
                            <input
                                type="number"
                                name="pickup_latitude"
                                value={etaForm.pickup_latitude}
                                onChange={handleInputChange}
                                step="any"
                                placeholder="28.6139"
                                required
                            />
                        </div>
                        <div className="form-group">
                            <label>Pickup Longitude</label>
                            <input
                                type="number"
                                name="pickup_longitude"
                                value={etaForm.pickup_longitude}
                                onChange={handleInputChange}
                                step="any"
                                placeholder="77.2090"
                                required
                            />
                        </div>
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label>Delivery Latitude</label>
                            <input
                                type="number"
                                name="delivery_latitude"
                                value={etaForm.delivery_latitude}
                                onChange={handleInputChange}
                                step="any"
                                placeholder="28.6339"
                                required
                            />
                        </div>
                        <div className="form-group">
                            <label>Delivery Longitude</label>
                            <input
                                type="number"
                                name="delivery_longitude"
                                value={etaForm.delivery_longitude}
                                onChange={handleInputChange}
                                step="any"
                                placeholder="77.2290"
                                required
                            />
                        </div>
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label>Time of Day (Optional)</label>
                            <select name="time_of_day" value={etaForm.time_of_day} onChange={handleInputChange}>
                                <option value="">Auto-detect</option>
                                <option value="morning">Morning (6-12 PM)</option>
                                <option value="afternoon">Afternoon (12-6 PM)</option>
                                <option value="evening">Evening (6-10 PM)</option>
                                <option value="night">Night (10 PM-6 AM)</option>
                            </select>
                        </div>
                        <div className="form-group">
                            <label>Day of Week (Optional)</label>
                            <select name="day_of_week" value={etaForm.day_of_week} onChange={handleInputChange}>
                                <option value="">Auto-detect</option>
                                <option value="monday">Monday</option>
                                <option value="tuesday">Tuesday</option>
                                <option value="wednesday">Wednesday</option>
                                <option value="thursday">Thursday</option>
                                <option value="friday">Friday</option>
                                <option value="saturday">Saturday</option>
                                <option value="sunday">Sunday</option>
                            </select>
                        </div>
                    </div>

                    <button type="submit" className="calculate-btn" disabled={loading}>
                        {loading ? 'Calculating...' : '🔮 Calculate ETA'}
                    </button>
                </form>

                {error && (
                    <div className="error-message">
                        Error: {error}
                    </div>
                )}

                {etaCalculation && (
                    <div className="eta-result">
                        <h4>📊 Delivery Prediction Results</h4>
                        <div className="result-grid">
                            <div className="result-card primary">
                                <div className="result-icon">⏱️</div>
                                <div className="result-content">
                                    <div className="result-value">{formatDuration(etaCalculation.estimated_duration_minutes)}</div>
                                    <div className="result-label">Estimated Duration</div>
                                </div>
                            </div>

                            <div className="result-card">
                                <div className="result-icon">🎯</div>
                                <div className="result-content">
                                    <div className="result-value" style={{ color: getConfidenceColor(etaCalculation.confidence_percentage) }}>
                                        {etaCalculation.confidence_percentage.toFixed(1)}%
                                    </div>
                                    <div className="result-label">Confidence</div>
                                </div>
                            </div>

                            <div className="result-card">
                                <div className="result-icon">📏</div>
                                <div className="result-content">
                                    <div className="result-value">{etaCalculation.distance_km.toFixed(2)} km</div>
                                    <div className="result-label">Distance</div>
                                </div>
                            </div>

                            <div className="result-card">
                                <div className="result-icon">🚗</div>
                                <div className="result-content">
                                    <div className="result-value">{etaCalculation.average_speed_kmh.toFixed(1)} km/h</div>
                                    <div className="result-label">Avg Speed</div>
                                </div>
                            </div>
                        </div>

                        <div className="eta-details">
                            <div className="detail-item">
                                <strong>Estimated Arrival:</strong> {new Date(etaCalculation.estimated_arrival).toLocaleString()}
                            </div>
                            <div className="detail-item">
                                <strong>Traffic Factor:</strong> {etaCalculation.traffic_factor.toFixed(2)}x
                            </div>
                            <div className="detail-item">
                                <strong>Historical Data Points:</strong> {etaCalculation.historical_data_count}
                            </div>
                            <div className="detail-item">
                                <strong>Factors Considered:</strong> {etaCalculation.factors.join(', ')}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Delivery Statistics */}
            {deliveryStats && (
                <div className="delivery-stats-section">
                    <h3>📈 Delivery Statistics (Last 30 Days)</h3>
                    <div className="stats-grid">
                        <div className="stat-card">
                            <div className="stat-icon">📦</div>
                            <div className="stat-content">
                                <div className="stat-value">{deliveryStats.total_deliveries}</div>
                                <div className="stat-label">Total Deliveries</div>
                            </div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon">⏰</div>
                            <div className="stat-content">
                                <div className="stat-value">{deliveryStats.average_delivery_time.toFixed(1)}m</div>
                                <div className="stat-label">Avg Delivery Time</div>
                            </div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon">✅</div>
                            <div className="stat-content">
                                <div className="stat-value">{deliveryStats.on_time_percentage.toFixed(1)}%</div>
                                <div className="stat-label">On-Time Rate</div>
                            </div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon">🏃</div>
                            <div className="stat-content">
                                <div className="stat-value">{deliveryStats.fastest_delivery}m</div>
                                <div className="stat-label">Fastest Delivery</div>
                            </div>
                        </div>
                    </div>

                    {/* Peak Hours Chart */}
                    <div className="peak-hours-chart">
                        <h4>📊 Peak Hours Performance</h4>
                        <div className="hours-bars">
                            {Object.entries(deliveryStats.peak_hours).map(([period, count]) => (
                                <div key={period} className="hour-bar">
                                    <div 
                                        className="bar-fill" 
                                        style={{ height: `${(count / 60) * 100}%` }}
                                    ></div>
                                    <div className="bar-label">{period}</div>
                                    <div className="bar-value">{count}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Agent Performance */}
            {agentPerformance && (
                <div className="agent-performance-section">
                    <h3>👤 Agent Performance</h3>
                    <div className="performance-grid">
                        <div className="performance-card">
                            <div className="performance-header">
                                <h4>Overall Score</h4>
                                <div className="efficiency-score">
                                    {agentPerformance.efficiency_score.toFixed(1)}%
                                </div>
                            </div>
                            <div className="performance-details">
                                <div className="detail-row">
                                    <span>Deliveries:</span>
                                    <span>{agentPerformance.total_deliveries}</span>
                                </div>
                                <div className="detail-row">
                                    <span>Avg Time:</span>
                                    <span>{agentPerformance.average_delivery_time.toFixed(1)}m</span>
                                </div>
                                <div className="detail-row">
                                    <span>On-Time Rate:</span>
                                    <span>{agentPerformance.on_time_rate.toFixed(1)}%</span>
                                </div>
                                <div className="detail-row">
                                    <span>Customer Rating:</span>
                                    <span>⭐ {agentPerformance.customer_rating.toFixed(1)}</span>
                                </div>
                            </div>
                        </div>

                        <div className="trend-card">
                            <h4>Recent Trends</h4>
                            <div className={`trend-indicator ${agentPerformance.recent_trends.improving ? 'improving' : 'declining'}`}>
                                <div className="trend-icon">
                                    {agentPerformance.recent_trends.improving ? '📈' : '📉'}
                                </div>
                                <div className="trend-text">
                                    {agentPerformance.recent_trends.improving ? 'Improving' : 'Declining'}
                                </div>
                                <div className="trend-percent">
                                    {agentPerformance.recent_trends.trend_percent.toFixed(1)}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DeliveryPredictionsDashboard;

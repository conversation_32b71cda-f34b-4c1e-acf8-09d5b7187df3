/* Delivery Predictions Dashboard Styles */

.delivery-predictions-dashboard {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.dashboard-header h2 {
    margin: 0 0 10px 0;
    color: #343a40;
    font-size: 28px;
    font-weight: 600;
}

.subtitle {
    color: #6c757d;
    font-size: 16px;
}

/* ETA Calculator Section */
.eta-calculator-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.eta-calculator-section h3 {
    color: #343a40;
    margin-bottom: 20px;
    font-size: 22px;
}

.eta-form {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.form-group input,
.form-group select {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.calculate-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    min-width: 200px;
    margin: 0 auto;
}

.calculate-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.calculate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid #f5c6cb;
}

/* ETA Results */
.eta-result {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 25px;
    margin-top: 20px;
    border: 2px solid #dee2e6;
}

.eta-result h4 {
    color: #343a40;
    margin-bottom: 20px;
    text-align: center;
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.result-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.result-card:hover {
    transform: translateY(-2px);
}

.result-card.primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.result-icon {
    font-size: 24px;
    margin-right: 15px;
}

.result-content {
    flex: 1;
}

.result-value {
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
}

.result-label {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 5px;
}

.eta-details {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.detail-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
}

.detail-item:last-child {
    border-bottom: none;
}

/* Delivery Statistics Section */
.delivery-stats-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.delivery-stats-section h3 {
    color: #343a40;
    margin-bottom: 20px;
    font-size: 22px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 32px;
    margin-right: 15px;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #343a40;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}

/* Peak Hours Chart */
.peak-hours-chart {
    margin-top: 30px;
}

.peak-hours-chart h4 {
    color: #343a40;
    margin-bottom: 20px;
}

.hours-bars {
    display: flex;
    justify-content: space-around;
    align-items: end;
    height: 150px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    gap: 10px;
}

.hour-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    height: 100%;
    position: relative;
}

.bar-fill {
    background: linear-gradient(to top, #007bff, #66b3ff);
    border-radius: 4px 4px 0 0;
    width: 100%;
    min-height: 10px;
    transition: all 0.3s ease;
}

.bar-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 8px;
    text-transform: capitalize;
}

.bar-value {
    font-size: 14px;
    font-weight: bold;
    color: #343a40;
    margin-top: 4px;
}

/* Agent Performance Section */
.agent-performance-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.agent-performance-section h3 {
    color: #343a40;
    margin-bottom: 20px;
    font-size: 22px;
}

.performance-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.performance-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 10px;
    padding: 25px;
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.performance-header h4 {
    margin: 0;
    font-size: 18px;
}

.efficiency-score {
    font-size: 36px;
    font-weight: bold;
}

.performance-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-row:last-child {
    border-bottom: none;
}

.trend-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
}

.trend-card h4 {
    color: #343a40;
    margin-bottom: 20px;
}

.trend-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.trend-icon {
    font-size: 48px;
}

.trend-text {
    font-size: 18px;
    font-weight: 600;
}

.trend-percent {
    font-size: 24px;
    font-weight: bold;
}

.trend-indicator.improving .trend-text,
.trend-indicator.improving .trend-percent {
    color: #28a745;
}

.trend-indicator.declining .trend-text,
.trend-indicator.declining .trend-percent {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .delivery-predictions-dashboard {
        padding: 15px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .result-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .performance-grid {
        grid-template-columns: 1fr;
    }
    
    .hours-bars {
        flex-direction: column;
        height: auto;
        gap: 15px;
    }
    
    .hour-bar {
        flex-direction: row;
        height: 40px;
    }
    
    .bar-fill {
        height: 100%;
        border-radius: 0 4px 4px 0;
    }
}

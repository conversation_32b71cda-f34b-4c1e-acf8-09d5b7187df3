.marker-customization-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.marker-customization-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.marker-type-selector {
    margin-bottom: 20px;
}

.marker-type-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.marker-type-selector select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.2s;
}

.marker-type-selector select:focus {
    outline: none;
    border-color: #667eea;
}

.marker-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.marker-preview h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
}

.marker-preview-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    overflow: hidden;
}

.settings-form {
    display: grid;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="file"] {
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
}

.form-group input[type="color"] {
    width: 60px;
    height: 40px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.form-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e1e5e9;
    outline: none;
    cursor: pointer;
}

.form-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.form-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.form-group button {
    margin-top: 8px;
    padding: 8px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.form-group button:hover {
    background: #c82333;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    gap: 12px;
}

.reset-btn {
    padding: 12px 24px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.2s;
}

.reset-btn:hover {
    background: #5a6268;
}

.save-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: opacity 0.2s;
}

.save-btn:hover:not(:disabled) {
    opacity: 0.9;
}

.save-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
    .marker-customization-modal {
        width: 95%;
        margin: 10px;
    }
    
    .modal-header {
        padding: 15px;
    }
    
    .modal-header h2 {
        font-size: 1.3rem;
    }
    
    .modal-content {
        padding: 15px;
    }
    
    .modal-footer {
        padding: 15px;
        flex-direction: column;
    }
    
    .modal-footer button {
        width: 100%;
    }
}

import React, { useState, useEffect } from 'react';
import './TemperatureDashboard.css';

const TemperatureDashboard = ({ agentId, tripId }) => {
    const [dashboardData, setDashboardData] = useState(null);
    const [temperatureHistory, setTemperatureHistory] = useState([]);
    const [alerts, setAlerts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedTrip, setSelectedTrip] = useState(null);

    // Fetch dashboard data
    useEffect(() => {
        fetchDashboardData();
        const interval = setInterval(fetchDashboardData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, [agentId]);

    // Fetch temperature history when trip is selected
    useEffect(() => {
        if (selectedTrip) {
            fetchTemperatureHistory(selectedTrip);
        }
    }, [selectedTrip]);

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();
            if (agentId) params.append('agent_id', agentId);

            const response = await fetch(`/api/temperature/dashboard?${params}`);
            if (!response.ok) throw new Error('Failed to fetch dashboard data');

            const result = await response.json();
            setDashboardData(result.data);
            
            // Fetch alerts
            await fetchAlerts();
        } catch (err) {
            setError(err.message);
            console.error('Error fetching dashboard data:', err);
        } finally {
            setLoading(false);
        }
    };

    const fetchTemperatureHistory = async (tripId) => {
        try {
            const response = await fetch(`/api/temperature/history/${tripId}?hours=24`);
            if (!response.ok) throw new Error('Failed to fetch temperature history');

            const result = await response.json();
            setTemperatureHistory(result.data.temperature_readings || []);
        } catch (err) {
            console.error('Error fetching temperature history:', err);
        }
    };

    const fetchAlerts = async () => {
        try {
            const params = new URLSearchParams();
            if (agentId) params.append('agent_id', agentId);
            if (tripId) params.append('trip_id', tripId);
            params.append('unresolved_only', 'true');

            const response = await fetch(`/api/temperature/alerts?${params}`);
            if (!response.ok) throw new Error('Failed to fetch alerts');

            const result = await response.json();
            setAlerts(result.data.alerts || []);
        } catch (err) {
            console.error('Error fetching alerts:', err);
        }
    };

    const resolveAlert = async (alertId, resolutionNote) => {
        try {
            const response = await fetch(`/api/temperature/alerts/${alertId}/resolve`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ resolution_note: resolutionNote })
            });

            if (!response.ok) throw new Error('Failed to resolve alert');
            
            // Refresh alerts
            await fetchAlerts();
        } catch (err) {
            console.error('Error resolving alert:', err);
        }
    };

    const getTemperatureStatus = (temp, requirements) => {
        if (!requirements || !temp) return 'unknown';
        
        const minTemp = requirements.min_temperature;
        const maxTemp = requirements.max_temperature;
        
        if (temp < minTemp) return 'too-cold';
        if (temp > maxTemp) return 'too-hot';
        return 'normal';
    };

    const getSeverityColor = (severity) => {
        switch (severity) {
            case 'critical': return '#dc3545';
            case 'high': return '#fd7e14';
            case 'medium': return '#ffc107';
            case 'low': return '#28a745';
            default: return '#6c757d';
        }
    };

    if (loading) {
        return (
            <div className="temperature-dashboard loading">
                <div className="loading-spinner">Loading temperature data...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="temperature-dashboard error">
                <div className="error-message">Error: {error}</div>
                <button onClick={fetchDashboardData} className="retry-button">
                    Retry
                </button>
            </div>
        );
    }

    return (
        <div className="temperature-dashboard">
            <div className="dashboard-header">
                <h2>🌡️ Temperature Monitoring Dashboard</h2>
                <div className="last-updated">
                    Last updated: {dashboardData?.last_updated ? 
                        new Date(dashboardData.last_updated).toLocaleTimeString() : 'Never'}
                </div>
            </div>

            {/* Summary Cards */}
            <div className="summary-cards">
                <div className="summary-card">
                    <div className="card-icon">🚚</div>
                    <div className="card-content">
                        <div className="card-value">
                            {dashboardData?.summary?.total_temperature_sensitive_trips || 0}
                        </div>
                        <div className="card-label">Temperature-Sensitive Trips</div>
                    </div>
                </div>

                <div className="summary-card">
                    <div className="card-icon">⚠️</div>
                    <div className="card-content">
                        <div className="card-value">
                            {dashboardData?.summary?.total_alerts || 0}
                        </div>
                        <div className="card-label">Total Alerts</div>
                    </div>
                </div>

                <div className="summary-card critical">
                    <div className="card-icon">🚨</div>
                    <div className="card-content">
                        <div className="card-value">
                            {dashboardData?.summary?.critical_alerts || 0}
                        </div>
                        <div className="card-label">Critical Alerts</div>
                    </div>
                </div>

                <div className="summary-card unresolved">
                    <div className="card-icon">⏰</div>
                    <div className="card-content">
                        <div className="card-value">
                            {dashboardData?.summary?.unresolved_alerts || 0}
                        </div>
                        <div className="card-label">Unresolved Alerts</div>
                    </div>
                </div>
            </div>

            {/* Active Trips */}
            <div className="active-trips-section">
                <h3>Active Temperature-Sensitive Trips</h3>
                <div className="trips-grid">
                    {dashboardData?.trips?.map(trip => (
                        <div 
                            key={trip.trip_id} 
                            className={`trip-card ${selectedTrip === trip.trip_id ? 'selected' : ''}`}
                            onClick={() => setSelectedTrip(trip.trip_id)}
                        >
                            <div className="trip-header">
                                <div className="trip-id">Trip: {trip.trip_id.substring(0, 8)}...</div>
                                <div className="agent-name">{trip.agent_name}</div>
                            </div>
                            
                            <div className="temperature-info">
                                {trip.current_temperature !== undefined ? (
                                    <div className={`temperature-reading ${getTemperatureStatus(trip.current_temperature, trip.temperature_requirements)}`}>
                                        <span className="temp-value">{trip.current_temperature.toFixed(1)}°C</span>
                                        {trip.current_humidity && (
                                            <span className="humidity-value">{trip.current_humidity.toFixed(1)}%</span>
                                        )}
                                    </div>
                                ) : (
                                    <div className="no-reading">No recent reading</div>
                                )}
                            </div>

                            <div className="temperature-requirements">
                                {trip.temperature_requirements && (
                                    <div className="temp-range">
                                        Range: {trip.temperature_requirements.min_temperature}°C to {trip.temperature_requirements.max_temperature}°C
                                    </div>
                                )}
                            </div>

                            <div className="alerts-summary">
                                {trip.unresolved_alerts > 0 && (
                                    <div className="alert-badge unresolved">
                                        {trip.unresolved_alerts} unresolved
                                    </div>
                                )}
                                {trip.critical_alerts > 0 && (
                                    <div className="alert-badge critical">
                                        {trip.critical_alerts} critical
                                    </div>
                                )}
                            </div>

                            <div className="trip-status">
                                Status: <span className="status-badge">{trip.trip_status}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Temperature History Chart */}
            {selectedTrip && temperatureHistory.length > 0 && (
                <div className="temperature-history-section">
                    <h3>Temperature History - Trip {selectedTrip.substring(0, 8)}...</h3>
                    <div className="temperature-chart">
                        <TemperatureChart data={temperatureHistory} />
                    </div>
                </div>
            )}

            {/* Active Alerts */}
            {alerts.length > 0 && (
                <div className="alerts-section">
                    <h3>Active Temperature Alerts</h3>
                    <div className="alerts-list">
                        {alerts.map(alert => (
                            <div key={alert.id} className="alert-item">
                                <div className="alert-header">
                                    <div 
                                        className="alert-severity" 
                                        style={{ backgroundColor: getSeverityColor(alert.severity) }}
                                    >
                                        {alert.severity.toUpperCase()}
                                    </div>
                                    <div className="alert-type">{alert.alert_type.replace('_', ' ').toUpperCase()}</div>
                                    <div className="alert-time">
                                        {new Date(alert.created_at).toLocaleString()}
                                    </div>
                                </div>
                                
                                <div className="alert-details">
                                    <div className="temperature-value">
                                        Temperature: {alert.temperature_c.toFixed(1)}°C
                                    </div>
                                    {alert.threshold_min && alert.threshold_max && (
                                        <div className="threshold-range">
                                            Allowed range: {alert.threshold_min}°C - {alert.threshold_max}°C
                                        </div>
                                    )}
                                    <div className="agent-info">Agent: {alert.agent_name}</div>
                                </div>

                                <div className="alert-actions">
                                    <button 
                                        className="resolve-button"
                                        onClick={() => {
                                            const note = prompt('Resolution note (optional):');
                                            if (note !== null) {
                                                resolveAlert(alert.id, note);
                                            }
                                        }}
                                    >
                                        Resolve Alert
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

// Simple temperature chart component
const TemperatureChart = ({ data }) => {
    if (!data || data.length === 0) return null;

    const maxTemp = Math.max(...data.map(d => d.temperature_c));
    const minTemp = Math.min(...data.map(d => d.temperature_c));
    const tempRange = maxTemp - minTemp || 1;

    return (
        <div className="simple-chart">
            <div className="chart-container">
                {data.map((reading, index) => {
                    const height = ((reading.temperature_c - minTemp) / tempRange) * 100;
                    return (
                        <div 
                            key={index}
                            className="chart-bar"
                            style={{ height: `${height}%` }}
                            title={`${reading.temperature_c.toFixed(1)}°C at ${new Date(reading.recorded_at).toLocaleTimeString()}`}
                        />
                    );
                })}
            </div>
            <div className="chart-labels">
                <span>Min: {minTemp.toFixed(1)}°C</span>
                <span>Max: {maxTemp.toFixed(1)}°C</span>
            </div>
        </div>
    );
};

export default TemperatureDashboard;

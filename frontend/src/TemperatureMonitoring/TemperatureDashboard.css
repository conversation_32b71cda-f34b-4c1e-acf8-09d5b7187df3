/* Temperature Monitoring Dashboard Styles */

.temperature-dashboard {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.temperature-dashboard.loading,
.temperature-dashboard.error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    font-size: 18px;
    color: #6c757d;
    text-align: center;
}

.error-message {
    color: #dc3545;
    font-size: 16px;
    margin-bottom: 15px;
}

.retry-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.retry-button:hover {
    background-color: #0056b3;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.dashboard-header h2 {
    margin: 0;
    color: #343a40;
    font-size: 28px;
    font-weight: 600;
}

.last-updated {
    color: #6c757d;
    font-size: 14px;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.summary-card.critical {
    border-left: 4px solid #dc3545;
}

.summary-card.unresolved {
    border-left: 4px solid #ffc107;
}

.card-icon {
    font-size: 32px;
    margin-right: 15px;
}

.card-content {
    flex: 1;
}

.card-value {
    font-size: 32px;
    font-weight: bold;
    color: #343a40;
    line-height: 1;
}

.card-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}

/* Active Trips Section */
.active-trips-section {
    margin-bottom: 30px;
}

.active-trips-section h3 {
    color: #343a40;
    margin-bottom: 20px;
    font-size: 22px;
}

.trips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.trip-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.trip-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.trip-card.selected {
    border-color: #007bff;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
}

.trip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.trip-id {
    font-weight: bold;
    color: #343a40;
    font-size: 16px;
}

.agent-name {
    color: #6c757d;
    font-size: 14px;
}

.temperature-info {
    margin-bottom: 15px;
}

.temperature-reading {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 8px;
    font-weight: bold;
}

.temperature-reading.normal {
    background-color: #d4edda;
    color: #155724;
}

.temperature-reading.too-cold {
    background-color: #cce7ff;
    color: #004085;
}

.temperature-reading.too-hot {
    background-color: #f8d7da;
    color: #721c24;
}

.temperature-reading.unknown {
    background-color: #e2e3e5;
    color: #383d41;
}

.temp-value {
    font-size: 18px;
}

.humidity-value {
    font-size: 14px;
    opacity: 0.8;
}

.no-reading {
    color: #6c757d;
    font-style: italic;
    padding: 10px;
    text-align: center;
}

.temperature-requirements {
    margin-bottom: 15px;
}

.temp-range {
    font-size: 12px;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 5px 10px;
    border-radius: 5px;
}

.alerts-summary {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.alert-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.alert-badge.unresolved {
    background-color: #fff3cd;
    color: #856404;
}

.alert-badge.critical {
    background-color: #f8d7da;
    color: #721c24;
}

.trip-status {
    font-size: 14px;
    color: #6c757d;
}

.status-badge {
    background-color: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

/* Temperature History Section */
.temperature-history-section {
    margin-bottom: 30px;
}

.temperature-history-section h3 {
    color: #343a40;
    margin-bottom: 20px;
    font-size: 22px;
}

.temperature-chart {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.simple-chart {
    height: 200px;
}

.chart-container {
    display: flex;
    align-items: end;
    height: 150px;
    gap: 2px;
    margin-bottom: 10px;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, #007bff, #66b3ff);
    border-radius: 2px 2px 0 0;
    min-height: 5px;
    transition: all 0.2s ease;
}

.chart-bar:hover {
    background: linear-gradient(to top, #0056b3, #4da6ff);
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6c757d;
}

/* Alerts Section */
.alerts-section {
    margin-bottom: 30px;
}

.alerts-section h3 {
    color: #343a40;
    margin-bottom: 20px;
    font-size: 22px;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.alert-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ffc107;
}

.alert-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.alert-severity {
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.alert-type {
    font-weight: bold;
    color: #343a40;
}

.alert-time {
    color: #6c757d;
    font-size: 14px;
    margin-left: auto;
}

.alert-details {
    margin-bottom: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.temperature-value,
.threshold-range,
.agent-info {
    font-size: 14px;
    color: #495057;
}

.temperature-value {
    font-weight: bold;
}

.alert-actions {
    display: flex;
    gap: 10px;
}

.resolve-button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.resolve-button:hover {
    background-color: #218838;
}

/* Responsive Design */
@media (max-width: 768px) {
    .temperature-dashboard {
        padding: 15px;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .trips-grid {
        grid-template-columns: 1fr;
    }
    
    .alert-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .alert-details {
        grid-template-columns: 1fr;
    }
}

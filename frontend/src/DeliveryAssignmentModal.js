import React, { useState, useEffect } from 'react';
import './DeliveryAssignmentModal.css';

const DeliveryAssignmentModal = ({ isOpen, onClose, agent, onAssignmentSuccess }) => {
    const [customers, setCustomers] = useState([]);
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        customer_id: '',
        pickup_location: {
            latitude: 28.6139,
            longitude: 77.2090,
            address: 'Connaught Place, New Delhi'
        },
        drop_location: {
            latitude: 28.5355,
            longitude: 77.3910,
            address: 'Noida Sector 62'
        },
        item_details: {
            description: '',
            weight: '',
            value: ''
        },
        delivery_fee: 150,
        scheduled_date: new Date().toISOString().split('T')[0],
        notes: ''
    });

    // Fetch customers when modal opens
    useEffect(() => {
        if (isOpen) {
            fetchCustomers();
        }
    }, [isOpen]);

    const fetchCustomers = async () => {
        try {
            const response = await fetch('/api/users/customers');
            const data = await response.json();
            if (data.success) {
                setCustomers(data.data.customers);
            }
        } catch (error) {
            console.error('Failed to fetch customers:', error);
        }
    };

    const handleInputChange = (field, value) => {
        if (field.includes('.')) {
            const [parent, child] = field.split('.');
            setFormData(prev => ({
                ...prev,
                [parent]: {
                    ...prev[parent],
                    [child]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [field]: value
            }));
        }
    };

    const handleLocationChange = (locationType, field, value) => {
        setFormData(prev => ({
            ...prev,
            [locationType]: {
                ...prev[locationType],
                [field]: value
            }
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            const payload = {
                agent_id: agent.agent_id,
                customer_id: formData.customer_id,
                pickup_location: formData.pickup_location,
                drop_location: formData.drop_location,
                item_details: formData.item_details,
                delivery_fee: parseFloat(formData.delivery_fee),
                scheduled_date: formData.scheduled_date,
                notes: formData.notes
            };

            const response = await fetch('/api/trip/assign-delivery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            const data = await response.json();
            
            if (data.success) {
                alert('Delivery assigned successfully!');
                onAssignmentSuccess && onAssignmentSuccess(data.data);
                onClose();
                // Reset form
                setFormData({
                    customer_id: '',
                    pickup_location: {
                        latitude: 28.6139,
                        longitude: 77.2090,
                        address: 'Connaught Place, New Delhi'
                    },
                    drop_location: {
                        latitude: 28.5355,
                        longitude: 77.3910,
                        address: 'Noida Sector 62'
                    },
                    item_details: {
                        description: '',
                        weight: '',
                        value: ''
                    },
                    delivery_fee: 150,
                    scheduled_date: new Date().toISOString().split('T')[0],
                    notes: ''
                });
            } else {
                alert('Failed to assign delivery: ' + data.message);
            }
        } catch (error) {
            console.error('Error assigning delivery:', error);
            alert('Error assigning delivery. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content delivery-assignment-modal">
                <div className="modal-header">
                    <h2>🚚 Assign Delivery to {agent?.name}</h2>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>

                <form onSubmit={handleSubmit} className="assignment-form">
                    {/* Customer Selection */}
                    <div className="form-group">
                        <label>👤 Customer</label>
                        <select 
                            value={formData.customer_id} 
                            onChange={(e) => handleInputChange('customer_id', e.target.value)}
                            required
                        >
                            <option value="">Select Customer</option>
                            {customers.map(customer => (
                                <option key={customer.id} value={customer.id}>
                                    {customer.name} ({customer.email})
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Pickup Location */}
                    <div className="form-group">
                        <label>📍 Pickup Location</label>
                        <input
                            type="text"
                            placeholder="Address"
                            value={formData.pickup_location.address}
                            onChange={(e) => handleLocationChange('pickup_location', 'address', e.target.value)}
                            required
                        />
                        <div className="location-coords">
                            <input
                                type="number"
                                step="any"
                                placeholder="Latitude"
                                value={formData.pickup_location.latitude}
                                onChange={(e) => handleLocationChange('pickup_location', 'latitude', parseFloat(e.target.value))}
                                required
                            />
                            <input
                                type="number"
                                step="any"
                                placeholder="Longitude"
                                value={formData.pickup_location.longitude}
                                onChange={(e) => handleLocationChange('pickup_location', 'longitude', parseFloat(e.target.value))}
                                required
                            />
                        </div>
                    </div>

                    {/* Drop Location */}
                    <div className="form-group">
                        <label>🎯 Drop Location</label>
                        <input
                            type="text"
                            placeholder="Address"
                            value={formData.drop_location.address}
                            onChange={(e) => handleLocationChange('drop_location', 'address', e.target.value)}
                            required
                        />
                        <div className="location-coords">
                            <input
                                type="number"
                                step="any"
                                placeholder="Latitude"
                                value={formData.drop_location.latitude}
                                onChange={(e) => handleLocationChange('drop_location', 'latitude', parseFloat(e.target.value))}
                                required
                            />
                            <input
                                type="number"
                                step="any"
                                placeholder="Longitude"
                                value={formData.drop_location.longitude}
                                onChange={(e) => handleLocationChange('drop_location', 'longitude', parseFloat(e.target.value))}
                                required
                            />
                        </div>
                    </div>

                    {/* Item Details */}
                    <div className="form-group">
                        <label>📦 Item Details</label>
                        <input
                            type="text"
                            placeholder="Description"
                            value={formData.item_details.description}
                            onChange={(e) => handleInputChange('item_details.description', e.target.value)}
                            required
                        />
                        <div className="item-details">
                            <input
                                type="text"
                                placeholder="Weight (e.g., 2kg)"
                                value={formData.item_details.weight}
                                onChange={(e) => handleInputChange('item_details.weight', e.target.value)}
                            />
                            <input
                                type="number"
                                placeholder="Value (₹)"
                                value={formData.item_details.value}
                                onChange={(e) => handleInputChange('item_details.value', e.target.value)}
                            />
                        </div>
                    </div>

                    {/* Delivery Fee & Date */}
                    <div className="form-row">
                        <div className="form-group">
                            <label>💰 Delivery Fee (₹)</label>
                            <input
                                type="number"
                                step="0.01"
                                value={formData.delivery_fee}
                                onChange={(e) => handleInputChange('delivery_fee', e.target.value)}
                                required
                            />
                        </div>
                        <div className="form-group">
                            <label>📅 Scheduled Date</label>
                            <input
                                type="date"
                                value={formData.scheduled_date}
                                onChange={(e) => handleInputChange('scheduled_date', e.target.value)}
                                required
                            />
                        </div>
                    </div>

                    {/* Notes */}
                    <div className="form-group">
                        <label>📝 Notes</label>
                        <textarea
                            placeholder="Special instructions..."
                            value={formData.notes}
                            onChange={(e) => handleInputChange('notes', e.target.value)}
                            rows="3"
                        />
                    </div>

                    {/* Submit Button */}
                    <div className="form-actions">
                        <button type="button" onClick={onClose} className="cancel-button">
                            Cancel
                        </button>
                        <button type="submit" disabled={loading} className="assign-button">
                            {loading ? '⏳ Assigning...' : '🚚 Assign Delivery'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default DeliveryAssignmentModal;

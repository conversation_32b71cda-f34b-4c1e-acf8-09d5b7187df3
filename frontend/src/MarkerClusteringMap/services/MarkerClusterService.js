/**
 * MarkerClusterService - Handles marker clustering and map operations
 */

class MarkerClusterService {
    constructor() {
        this.currentMarkersRef = null;
    }

    /**
     * Generate initials from name
     * @param {string} name - Agent name
     * @returns {string} Initials
     */
    getInitials(name) {
        if (!name) return 'NA';
        return name
            .split(' ')
            .map(word => word.charAt(0).toUpperCase())
            .slice(0, 2)
            .join('');
    }

    /**
     * Clear current markers from map
     * @param {Object} mapInstance - Leaflet map instance
     */
    clearCurrentMarkers(mapInstance) {
        if (this.currentMarkersRef && mapInstance) {
            console.log('🧹 Clearing existing agent markers...');
            mapInstance.removeLayer(this.currentMarkersRef);
            this.currentMarkersRef = null;
            console.log('✅ Cleared existing agent markers');
        }
    }

    /**
     * Create marker cluster group
     * @returns {Object} Leaflet marker cluster group
     */
    createMarkerClusterGroup() {
        return window.L.markerClusterGroup({
            chunkedLoading: true,
            maxClusterRadius: 80
        });
    }

    /**
     * Create custom marker for agent
     * @param {Object} agent - Agent data
     * @returns {Object} Leaflet marker
     */
    createAgentMarker(agent) {
        const initials = this.getInitials(agent.name);
        const backgroundColor = `hsl(${Math.abs(agent.agent_id.split('-')[0].charCodeAt(0)) % 360}, 60%, 70%)`;

        // Create custom marker icon
        const customIcon = window.L.divIcon({
            className: 'custom-agent-marker',
            html: `
                <div style="
                    width: 40px;
                    height: 40px;
                    background-color: ${backgroundColor};
                    border: 3px solid white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    font-size: 12px;
                    color: white;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    position: relative;
                ">
                    ${initials}
                    <div style="
                        position: absolute;
                        bottom: -2px;
                        right: -2px;
                        width: 12px;
                        height: 12px;
                        background-color: ${agent.status === 'active' ? '#10b981' : '#ef4444'};
                        border: 2px solid white;
                        border-radius: 50%;
                    "></div>
                </div>
            `,
            iconSize: [40, 40],
            iconAnchor: [20, 20]
        });

        return window.L.marker(agent.current_location, { icon: customIcon });
    }

    /**
     * Create popup content for agent marker
     * @param {Object} agent - Agent data
     * @returns {string} HTML popup content
     */
    createPopupContent(agent) {
        const statusColor = agent.status === 'active' ? '#10b981' : '#ef4444';
        const statusText = agent.status === 'active' ? 'Online' : 'Offline';

        return `
            <div style="min-width: 200px; font-family: Arial, sans-serif;">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <img src="${agent.profile_photo}" 
                         style="width: 40px; height: 40px; border-radius: 50%; margin-right: 12px; object-fit: cover;"
                         onerror="this.style.display='none';" />
                    <div>
                        <div style="font-weight: bold; font-size: 14px;">${agent.name}</div>
                        <div style="font-size: 12px; color: ${statusColor};">● ${statusText}</div>
                    </div>
                </div>
                <div style="font-size: 12px; margin-bottom: 8px;">
                    📞 ${agent.phone}
                </div>
                <div style="font-size: 12px; margin-bottom: 8px;">
                    📍 ${agent.city}
                </div>
                <div style="font-size: 12px; margin-bottom: 8px;">
                    🔋 Battery: ${agent.battery_level}%
                </div>
                <div style="font-size: 12px; margin-bottom: 12px;">
                    📦 Deliveries: ${agent.total_deliveries} total, ${agent.pending_deliveries} pending
                </div>
                <div style="font-size: 12px; margin-bottom: 12px;">
                    🛵 Vehicle: ${agent.vehicle_type}
                </div>
                <div style="display: flex; gap: 8px;">
                    <button onclick="window.selectAgent('${agent.agent_id}')"
                            style="flex: 1; padding: 8px; background-color: #007bff; color: white;
                                   border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        View Tracking
                    </button>
                    <button onclick="window.assignDelivery('${agent.agent_id}')"
                            style="flex: 1; padding: 8px; background-color: #10b981; color: white;
                                   border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🚚 Assign
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Add agents to map as clustered markers
     * @param {Object} mapInstance - Leaflet map instance
     * @param {Array} agents - Array of agent data
     * @param {Function} onAgentSelect - Callback for agent selection
     * @returns {Object} Marker cluster group
     */
    addAgentsToMap(mapInstance, agents, onAgentSelect) {
        // Clear existing markers first
        this.clearCurrentMarkers(mapInstance);

        // Create marker cluster group
        const markers = this.createMarkerClusterGroup();

        // Add markers for each agent
        agents.forEach(agent => {
            const marker = this.createAgentMarker(agent);
            const popupContent = this.createPopupContent(agent);

            marker.bindPopup(popupContent);
            marker.on('click', () => {
                console.log('🔍 Selected agent:', agent);
                if (onAgentSelect) onAgentSelect(agent);
            });

            markers.addLayer(marker);
        });

        // Add cluster group to map
        mapInstance.addLayer(markers);
        this.currentMarkersRef = markers;

        console.log(`📍 Added ${agents.length} agent markers to clustering map`);
        return markers;
    }

    /**
     * Initialize Leaflet map
     * @param {HTMLElement} container - Map container element
     * @returns {Object} Leaflet map instance
     */
    initializeMap(container) {
        // Check if map container already has a map instance
        if (container._leaflet_id) {
            console.log('⚠️ Map container already has a Leaflet instance, cleaning up...');
            container.innerHTML = '';
            delete container._leaflet_id;
        }

        // Create Leaflet map
        const map = window.L.map(container).setView([20.5937, 78.9629], 5);

        // Add OpenStreetMap tiles
        window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        console.log('✅ Leaflet Map initialized');
        return map;
    }
}

export default new MarkerClusterService();

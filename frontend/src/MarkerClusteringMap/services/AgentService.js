/**
 * AgentService - Handles agent data fetching and transformation
 */

class AgentService {
    constructor() {
        this.API_BASE_URL = process.env.REACT_APP_API_URL || '';
    }

    /**
     * Fetch agent data from backend API
     * @returns {Promise<Array>} Array of transformed agent data
     */
    async fetchAgentData() {
        try {
            console.log('🔍 Fetching real agent data from new backend...');
            console.log('🔗 API_BASE_URL:', this.API_BASE_URL);
            const fullUrl = `${this.API_BASE_URL}/api/tracking/active-agents?within_minutes=120`;
            console.log('🌐 Full URL:', fullUrl);

            const response = await fetch(fullUrl);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ API response received:', result);

            if (!result.success || !result.data) {
                throw new Error('Invalid API response format');
            }

            // Transform backend data to frontend format
            const transformedAgents = result.data.agents.map(agent => ({
                agent_id: agent.agent_id,
                name: agent.name || 'Unknown Agent',
                phone: agent.phone || 'N/A',
                status: 'active', // All agents from active-agents endpoint are active
                current_location: [agent.location.latitude, agent.location.longitude], // [lat, lng] for Leaflet
                profile_photo: `https://images.unsplash.com/photo-150700321116${Math.abs(agent.agent_id.split('-')[0].charCodeAt(0)) % 10}?w=100&h=100&fit=crop&crop=face`,
                vehicle_type: agent.role === 'delivery' ? 'bike' : 'car',
                battery_level: Math.round(agent.battery_pct || 0),
                total_deliveries: 0, // Will be updated with real data
                pending_deliveries: 0, // Will be updated with real data
                failed_deliveries: 0,
                success_rate: 0,
                city: agent.location.city || 'Unknown'
            }));

            // Fetch delivery counts for all agents
            await this.updateDeliveryCounts(transformedAgents);

            console.log('✅ Transformed agent data:', transformedAgents);
            return transformedAgents;

        } catch (error) {
            console.error('❌ Error fetching agent data:', error);
            throw error;
        }
    }

    /**
     * Update agents with delivery counts
     * @param {Array} agents - Array of agent objects to update
     */
    async updateDeliveryCounts(agents) {
        try {
            const countsResponse = await fetch(`${this.API_BASE_URL}/api/tracking/agents-delivery-counts`);
            if (countsResponse.ok) {
                const countsResult = await countsResponse.json();
                if (countsResult.success && countsResult.data.delivery_counts.agents) {
                    // Create a map of agent delivery counts for quick lookup
                    const deliveryCounts = {};
                    countsResult.data.delivery_counts.agents.forEach(agent => {
                        deliveryCounts[agent.agent_id] = {
                            total_deliveries: agent.delivered_count || 0,
                            pending_deliveries: agent.pending_count || 0,
                            failed_deliveries: agent.failed_count || 0,
                            success_rate: agent.success_rate || 0
                        };
                    });

                    // Update agents with real delivery data
                    agents.forEach(agent => {
                        const counts = deliveryCounts[agent.agent_id];
                        if (counts) {
                            agent.total_deliveries = counts.total_deliveries;
                            agent.pending_deliveries = counts.pending_deliveries;
                            agent.failed_deliveries = counts.failed_deliveries;
                            agent.success_rate = counts.success_rate;
                        }
                    });

                    console.log('✅ Updated agents with real delivery counts');
                }
            }
        } catch (countsError) {
            console.warn('⚠️ Failed to fetch delivery counts, using defaults:', countsError);
        }
    }

    /**
     * Filter agents based on status
     * @param {Array} agents - Array of agents
     * @param {string} statusFilter - 'all', 'present', 'absent'
     * @returns {Array} Filtered agents
     */
    filterAgentsByStatus(agents, statusFilter) {
        if (statusFilter === 'present') {
            return agents.filter(agent => agent.status === 'active');
        } else if (statusFilter === 'absent') {
            return agents.filter(agent => agent.status === 'offline');
        }
        return agents; // 'all'
    }

    /**
     * Filter agents by search term
     * @param {Array} agents - Array of agents
     * @param {string} searchTerm - Search term
     * @returns {Array} Filtered agents
     */
    filterAgentsBySearch(agents, searchTerm) {
        if (!searchTerm) return agents;
        
        const term = searchTerm.toLowerCase();
        return agents.filter(agent =>
            agent.name.toLowerCase().includes(term) ||
            agent.phone.includes(searchTerm) ||
            (agent.city && agent.city.toLowerCase().includes(term))
        );
    }
}

export default new AgentService();

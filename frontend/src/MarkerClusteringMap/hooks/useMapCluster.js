/**
 * useMapCluster - Custom hook for managing map clustering
 */

import { useState, useRef, useEffect, useCallback } from 'react';
import MarkerClusterService from '../services/MarkerClusterService';

export const useMapCluster = () => {
    const mapContainerRef = useRef(null);
    const mapInstanceRef = useRef(null);
    const [isMapInitializing, setIsMapInitializing] = useState(false);

    /**
     * Initialize map
     */
    const initializeMap = useCallback(async (agents, onAgentSelect) => {
        if (!mapContainerRef.current || mapInstanceRef.current || isMapInitializing) return;

        try {
            setIsMapInitializing(true);
            console.log('🗺️ Initializing Leaflet Map with Clustering...');

            // Initialize map
            const map = MarkerClusterService.initializeMap(mapContainerRef.current);
            mapInstanceRef.current = map;

            // Add agents to map
            if (agents && agents.length > 0) {
                MarkerClusterService.addAgentsToMap(map, agents, onAgentSelect);
            }

            console.log('✅ Leaflet Map with Clustering loaded');
        } catch (error) {
            console.error('❌ Error initializing map:', error);
        } finally {
            setIsMapInitializing(false);
        }
    }, [isMapInitializing]);

    /**
     * Update map with new agent data
     */
    const updateMapWithAgents = useCallback((agents, onAgentSelect) => {
        if (mapInstanceRef.current && agents) {
            MarkerClusterService.addAgentsToMap(mapInstanceRef.current, agents, onAgentSelect);
        }
    }, []);

    /**
     * Clear all markers
     */
    const clearMarkers = useCallback(() => {
        if (mapInstanceRef.current) {
            MarkerClusterService.clearCurrentMarkers(mapInstanceRef.current);
        }
    }, []);

    // Setup global window functions for popup buttons
    useEffect(() => {
        window.selectAgent = (agentId) => {
            console.log('🎯 Global selectAgent called with:', agentId);
            // This will be handled by the parent component
            if (window.onAgentSelectFromPopup) {
                window.onAgentSelectFromPopup(agentId);
            }
        };

        window.assignDelivery = (agentId) => {
            console.log('🚚 Global assignDelivery called with:', agentId);
            // This will be handled by the parent component
            if (window.onAssignDeliveryFromPopup) {
                window.onAssignDeliveryFromPopup(agentId);
            }
        };

        return () => {
            delete window.selectAgent;
            delete window.assignDelivery;
        };
    }, []);

    return {
        mapContainerRef,
        mapInstanceRef,
        isMapInitializing,
        initializeMap,
        updateMapWithAgents,
        clearMarkers
    };
};

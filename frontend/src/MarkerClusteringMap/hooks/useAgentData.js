/**
 * useAgentData - Custom hook for managing agent data
 */

import { useState, useEffect, useCallback } from 'react';
import AgentService from '../services/AgentService';

export const useAgentData = () => {
    const [agents, setAgents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    /**
     * Fetch agent data
     */
    const fetchAgentData = useCallback(async () => {
        console.log('🔄 Fetching agent data...');
        setLoading(true);
        setError(null);

        try {
            const agentData = await AgentService.fetchAgentData();
            setAgents(agentData);
            return agentData;
        } catch (error) {
            console.error('❌ Error fetching agent data:', error);
            setError(error.message);
            setAgents([]);
            return [];
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Refresh agent data
     */
    const refreshAgentData = useCallback(async () => {
        return await fetchAgentData();
    }, [fetchAgentData]);

    /**
     * Filter agents by status
     */
    const filterAgentsByStatus = useCallback((statusFilter) => {
        return AgentService.filterAgentsByStatus(agents, statusFilter);
    }, [agents]);

    /**
     * Filter agents by search term
     */
    const filterAgentsBySearch = useCallback((filteredAgents, searchTerm) => {
        return AgentService.filterAgentsBySearch(filteredAgents, searchTerm);
    }, []);

    /**
     * Get filtered agents based on status and search
     */
    const getFilteredAgents = useCallback((statusFilter, searchTerm) => {
        const statusFiltered = filterAgentsByStatus(statusFilter);
        return filterAgentsBySearch(statusFiltered, searchTerm);
    }, [filterAgentsByStatus, filterAgentsBySearch]);

    // Load initial data
    useEffect(() => {
        fetchAgentData();
    }, [fetchAgentData]);

    return {
        agents,
        loading,
        error,
        fetchAgentData,
        refreshAgentData,
        getFilteredAgents
    };
};

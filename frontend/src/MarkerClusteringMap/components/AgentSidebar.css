/* Enterprise Agent Sidebar Styling - Enhanced */
.enterprise-sidebar {
    width: 400px;
    height: 100%;
    background: white;
    border-right: 1px solid rgba(24, 144, 255, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Responsive sidebar */
@media (max-width: 1024px) {
    .enterprise-sidebar {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .enterprise-sidebar {
        width: 100%;
        max-width: 400px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1001;
        box-shadow: 0 0 40px rgba(0, 0, 0, 0.2);
    }
}

/* Header Section */
.sidebar-header {
    padding: 24px;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(114, 46, 209, 0.05) 100%);
    border-bottom: 1px solid rgba(24, 144, 255, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    font-size: 20px;
}

.header-title h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: #1890ff;
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.refresh-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
}

.refresh-btn.loading {
    background: #ccc;
    cursor: not-allowed;
}

.refresh-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Search Section */
.search-container {
    margin-bottom: 16px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    font-size: 14px;
    color: #8c8c8c;
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 36px;
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    font-size: 14px;
    background: #fafafa;
    transition: all 0.2s ease;
    font-family: inherit;
}

.search-input:focus {
    outline: none;
    border-color: #1890ff;
    background: white;
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
}

/* Status Filters */
.status-filters {
    display: flex;
    gap: 8px;
}

.status-filter {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 10px 12px;
    background: white;
    color: var(--filter-color);
    border: 2px solid var(--filter-color);
    border-radius: 8px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 600;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-filter:hover {
    background: var(--filter-color);
    color: white;
    transform: translateY(-1px);
}

.status-filter.active {
    background: var(--filter-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.filter-icon {
    font-size: 12px;
}

/* Error Display */
.error-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border-bottom: 1px solid rgba(255, 77, 79, 0.2);
    font-size: 14px;
    font-weight: 500;
}

.error-icon {
    font-size: 16px;
}

/* Agent List */
.agent-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.agent-list::-webkit-scrollbar {
    width: 6px;
}

.agent-list::-webkit-scrollbar-track {
    background: #f0f0f0;
}

.agent-list::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
}

.agent-list::-webkit-scrollbar-thumb:hover {
    background: #bfbfbf;
}

/* Loading and Empty States */
.loading-state, .empty-state {
    padding: 60px 24px;
    text-align: center;
    color: #8c8c8c;
}

.loading-spinner {
    font-size: 32px;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;
}

.loading-text, .empty-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #595959;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-subtext {
    font-size: 12px;
    color: #bfbfbf;
}

/* Compact Agent Cards - Show More Agents */
.agent-card {
    padding: 12px;
    margin: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    background: white;
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.agent-card:hover {
    background: white;
    border-color: rgba(24, 144, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.agent-card.selected {
    background: rgba(24, 144, 255, 0.02);
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.agent-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.agent-avatar {
    position: relative;
    margin-right: 12px;
}

.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.status-dot {
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 10px;
    height: 10px;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-dot.online {
    background: #52c41a;
}

.status-dot.offline {
    background: #ff4d4f;
}

.agent-info {
    flex: 1;
}

.agent-name {
    font-weight: 600;
    font-size: 14px;
    color: #262626;
    margin-bottom: 2px;
}

.agent-phone {
    font-size: 11px;
    color: #8c8c8c;
    font-weight: 400;
}

.agent-status-badge {
    font-size: 12px;
}

.status-indicator {
    font-size: 10px;
}

/* Compact Agent Details */
.agent-details {
    display: flex;
    gap: 12px;
    margin-bottom: 6px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: #595959;
}

.detail-icon {
    font-size: 10px;
}

.detail-text {
    font-weight: 500;
}

/* Compact Agent Metrics */
.agent-metrics {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 4px 8px;
    background: rgba(24, 144, 255, 0.03);
    border-radius: 4px;
}

.metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 0;
}

.metric-value {
    font-size: 12px;
    font-weight: 600;
    color: #1890ff;
    line-height: 1;
}

.metric-label {
    font-size: 8px;
    color: #8c8c8c;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.3px;
    line-height: 1;
    margin-top: 1px;
}

.metric-divider {
    color: #d9d9d9;
    font-weight: 300;
    font-size: 10px;
}

/* Compact Agent Actions */
.agent-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 8px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 10px;
    font-weight: 600;
    transition: all 0.2s ease;
    text-transform: none;
    letter-spacing: 0;
    font-family: inherit;
}

.action-btn.primary {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
    box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
}

.action-btn.success {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: white;
    box-shadow: 0 1px 4px rgba(82, 196, 26, 0.2);
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-btn.primary:hover {
    box-shadow: 0 3px 8px rgba(24, 144, 255, 0.3);
}

.action-btn.success:hover {
    box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
}

.btn-icon {
    font-size: 14px;
}

.btn-text {
    font-size: 13px;
    font-weight: 600;
}

/* Mobile responsive actions */
@media (max-width: 768px) {
    .agent-actions {
        flex-direction: column;
        gap: 8px;
    }

    .action-btn {
        flex: none;
        width: 100%;
    }
}

/* Enterprise Footer */
.sidebar-footer {
    padding: 16px 20px;
    border-top: 1px solid rgba(24, 144, 255, 0.1);
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(114, 46, 209, 0.02) 100%);
}

.enterprise-metrics {
    margin-bottom: 12px;
}

.metrics-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.metrics-row:last-child {
    margin-bottom: 0;
}

.metric-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 10px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(24, 144, 255, 0.08);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.metric-card:hover {
    background: rgba(24, 144, 255, 0.02);
    border-color: rgba(24, 144, 255, 0.15);
    transform: translateY(-1px);
}

.metric-icon {
    font-size: 14px;
    width: 20px;
    text-align: center;
}

.metric-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.metric-value {
    font-size: 14px;
    font-weight: 700;
    color: #1890ff;
    line-height: 1.2;
}

.metric-label {
    font-size: 9px;
    color: #8c8c8c;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.3px;
    line-height: 1.2;
}

.footer-summary {
    text-align: center;
    padding-top: 8px;
    border-top: 1px solid rgba(24, 144, 255, 0.05);
}

.summary-text {
    font-size: 11px;
    color: #595959;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enterprise-sidebar {
        width: 100%;
        max-width: 350px;
    }
    
    .agent-actions {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
}

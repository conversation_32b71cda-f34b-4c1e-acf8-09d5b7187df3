/**
 * AgentSidebar - Sidebar component for agent list and controls
 */

import React from 'react';
import './AgentSidebar.css';

const AgentSidebar = ({
    agents,
    filteredAgents,
    selectedAgent,
    searchTerm,
    statusFilter,
    loading,
    error,
    onSearchChange,
    onStatusFilterChange,
    onAgentSelect,
    onAssignDelivery,
    onRefresh
}) => {
    const getInitials = (name) => {
        if (!name) return 'NA';
        return name
            .split(' ')
            .map(word => word.charAt(0).toUpperCase())
            .slice(0, 2)
            .join('');
    };

    const getStatusCounts = () => {
        const total = agents.length;
        const present = agents.filter(agent => agent.status === 'active').length;
        const absent = agents.filter(agent => agent.status === 'offline').length;
        return { total, present, absent };
    };

    const getEnterpriseMetrics = () => {
        const activeAgents = agents.filter(agent => agent.status === 'active');
        const totalDeliveries = agents.reduce((sum, agent) => sum + (agent.total_deliveries || 0), 0);
        const pendingDeliveries = agents.reduce((sum, agent) => sum + (agent.pending_deliveries || 0), 0);
        const avgBattery = activeAgents.length > 0
            ? Math.round(activeAgents.reduce((sum, agent) => sum + (agent.battery_level || 0), 0) / activeAgents.length)
            : 0;
        const completionRate = totalDeliveries > 0
            ? Math.round(((totalDeliveries - pendingDeliveries) / totalDeliveries) * 100)
            : 0;

        return {
            totalDeliveries,
            pendingDeliveries,
            avgBattery,
            completionRate,
            activeAgents: activeAgents.length
        };
    };

    const statusCounts = getStatusCounts();
    const enterpriseMetrics = getEnterpriseMetrics();

    return (
        <div className="enterprise-sidebar">
            {/* Header */}
            <div className="sidebar-header">
                <div className="header-content">
                    <div className="header-title">
                        <div className="title-icon">🗺️</div>
                        <h2>Agent Cluster</h2>
                    </div>
                    <button
                        onClick={onRefresh}
                        disabled={loading}
                        className={`refresh-btn ${loading ? 'loading' : ''}`}
                    >
                        <span className="refresh-icon">🔄</span>
                        {loading ? 'Refreshing...' : 'Refresh'}
                    </button>
                </div>

                {/* Search */}
                <div className="search-container">
                    <div className="search-input-wrapper">
                        <span className="search-icon">🔍</span>
                        <input
                            type="text"
                            placeholder="Search agents..."
                            value={searchTerm}
                            onChange={(e) => onSearchChange(e.target.value)}
                            className="search-input"
                        />
                    </div>
                </div>

                {/* Status Filter */}
                <div className="status-filters">
                    {[
                        { key: 'all', label: `All (${statusCounts.total})`, color: '#6c757d', icon: '👥' },
                        { key: 'present', label: `Present (${statusCounts.present})`, color: '#10b981', icon: '✅' },
                        { key: 'absent', label: `Absent (${statusCounts.absent})`, color: '#ef4444', icon: '❌' }
                    ].map(filter => (
                        <button
                            key={filter.key}
                            onClick={() => onStatusFilterChange(filter.key)}
                            className={`status-filter ${statusFilter === filter.key ? 'active' : ''}`}
                            style={{
                                '--filter-color': filter.color
                            }}
                        >
                            <span className="filter-icon">{filter.icon}</span>
                            <span className="filter-label">{filter.label}</span>
                        </button>
                    ))}
                </div>
            </div>

            {/* Error Display */}
            {error && (
                <div className="error-display">
                    <span className="error-icon">⚠️</span>
                    <span className="error-message">{error}</span>
                </div>
            )}

            {/* Agent List */}
            <div className="agent-list">
                {loading ? (
                    <div className="loading-state">
                        <div className="loading-spinner">🔄</div>
                        <div className="loading-text">Loading agents...</div>
                    </div>
                ) : filteredAgents.length === 0 ? (
                    <div className="empty-state">
                        <div className="empty-icon">👥</div>
                        <div className="empty-text">No agents found</div>
                        <div className="empty-subtext">Try adjusting your search or filter</div>
                    </div>
                ) : (
                    filteredAgents.map(agent => {
                        const initials = getInitials(agent.name);
                        const backgroundColor = `hsl(${Math.abs(agent.agent_id.split('-')[0].charCodeAt(0)) % 360}, 60%, 70%)`;
                        const isSelected = selectedAgent?.agent_id === agent.agent_id;

                        return (
                            <div
                                key={agent.agent_id}
                                onClick={() => onAgentSelect(agent)}
                                className={`agent-card ${isSelected ? 'selected' : ''}`}
                            >
                                <div className="agent-header">
                                    <div className="agent-avatar">
                                        <div
                                            className="avatar-circle"
                                            style={{ backgroundColor }}
                                        >
                                            {initials}
                                        </div>
                                        <div
                                            className={`status-dot ${agent.status === 'active' ? 'online' : 'offline'}`}
                                        ></div>
                                    </div>
                                    <div className="agent-info">
                                        <div className="agent-name">{agent.name}</div>
                                        <div className="agent-phone">{agent.phone}</div>
                                    </div>
                                    <div className="agent-status-badge">
                                        <span className={`status-indicator ${agent.status}`}>
                                            {agent.status === 'active' ? '🟢' : '🔴'}
                                        </span>
                                    </div>
                                </div>

                                <div className="agent-details">
                                    <div className="detail-item">
                                        <span className="detail-icon">📍</span>
                                        <span className="detail-text">{agent.city}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-icon">🔋</span>
                                        <span className="detail-text">{agent.battery_level}%</span>
                                    </div>
                                </div>

                                <div className="agent-metrics">
                                    <div className="metric-item">
                                        <span className="metric-value">{agent.total_deliveries}</span>
                                        <span className="metric-label">Total</span>
                                    </div>
                                    <div className="metric-divider">•</div>
                                    <div className="metric-item">
                                        <span className="metric-value">{agent.pending_deliveries}</span>
                                        <span className="metric-label">Pending</span>
                                    </div>
                                </div>

                                <div className="agent-actions">
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onAgentSelect(agent);
                                        }}
                                        className="action-btn primary"
                                    >
                                        <span className="btn-icon">📍</span>
                                        <span className="btn-text">View Tracking</span>
                                    </button>
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onAssignDelivery(agent);
                                        }}
                                        className="action-btn success"
                                    >
                                        <span className="btn-icon">🚚</span>
                                        <span className="btn-text">Assign Delivery</span>
                                    </button>
                                </div>
                            </div>
                        );
                    })
                )}
            </div>

            {/* Enterprise Footer Metrics */}
            <div className="sidebar-footer">
                <div className="enterprise-metrics">
                    <div className="metrics-row">
                        <div className="metric-card">
                            <div className="metric-icon">🚚</div>
                            <div className="metric-content">
                                <span className="metric-value">{enterpriseMetrics.totalDeliveries}</span>
                                <span className="metric-label">Total Deliveries</span>
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-icon">⏳</div>
                            <div className="metric-content">
                                <span className="metric-value">{enterpriseMetrics.pendingDeliveries}</span>
                                <span className="metric-label">Pending</span>
                            </div>
                        </div>
                    </div>
                    <div className="metrics-row">
                        <div className="metric-card">
                            <div className="metric-icon">✅</div>
                            <div className="metric-content">
                                <span className="metric-value">{enterpriseMetrics.completionRate}%</span>
                                <span className="metric-label">Completion Rate</span>
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-icon">🔋</div>
                            <div className="metric-content">
                                <span className="metric-value">{enterpriseMetrics.avgBattery}%</span>
                                <span className="metric-label">Avg Battery</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="footer-summary">
                    <span className="summary-text">
                        {enterpriseMetrics.activeAgents} of {statusCounts.total} agents active
                    </span>
                </div>
            </div>
        </div>
    );
};

export default AgentSidebar;

import React, { useState, useEffect } from 'react';
import './MarkerCustomization.css';

const MarkerCustomization = ({ viewerId, agentId, onClose, onSave }) => {
    const [markerSettings, setMarkerSettings] = useState({});
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [selectedMarkerType, setSelectedMarkerType] = useState('delivered');

    const markerTypes = [
        { value: 'requested', label: 'Requested', defaultColor: '#2196F3' },
        { value: 'accepted', label: 'Accepted', defaultColor: '#4CAF50' },
        { value: 'picked_up', label: 'Picked Up', defaultColor: '#FF9800' },
        { value: 'in_transit', label: 'In Transit', defaultColor: '#9C27B0' },
        { value: 'out_for_delivery', label: 'Out for Delivery', defaultColor: '#FF5722' },
        { value: 'delivered', label: 'Delivered', defaultColor: '#4CAF50' },
        { value: 'failed', label: 'Failed', defaultColor: '#F44336' },
        { value: 'rescheduled', label: 'Rescheduled', defaultColor: '#9C27B0' },
        { value: 'cancelled', label: 'Cancelled', defaultColor: '#757575' },
        { value: 'returned', label: 'Returned', defaultColor: '#795548' },
        { value: 'gps_start', label: 'GPS Route Start', defaultColor: '#00aa44' },
        { value: 'gps_end', label: 'GPS Route End', defaultColor: '#F44336' }
    ];

    const defaultIcons = {
        'requested': '?',
        'accepted': '✓',
        'picked_up': '↑',
        'in_transit': '→',
        'out_for_delivery': '🚚',
        'delivered': '✓',
        'failed': '✗',
        'rescheduled': '⏰',
        'cancelled': '✗',
        'returned': '↩',
        'gps_start': '🏠',
        'gps_end': '👤'
    };

    useEffect(() => {
        loadMarkerSettings();
    }, [viewerId]); // Changed from agentId to viewerId since settings are viewer-specific

    const loadMarkerSettings = async () => {
        try {
            setLoading(true);
            console.log('🔄 Loading viewer-specific marker settings for viewer:', viewerId, '(viewing agent:', agentId, ')');
            const response = await fetch(`/api/markers/settings/${viewerId}`);

            if (response.ok) {
                const data = await response.json();
                console.log('📊 Viewer marker settings response:', data);
                if (data.success && data.data.settings) {
                    const settingsMap = {};
                    data.data.settings.forEach(setting => {
                        settingsMap[setting.marker_type] = setting;
                    });
                    setMarkerSettings(settingsMap);
                    console.log('✅ Viewer-specific marker settings loaded:', settingsMap);
                } else {
                    console.log('ℹ️ No existing viewer marker settings found');
                    setMarkerSettings({});
                }
            } else {
                console.error('❌ Failed to load viewer marker settings:', response.status);
                setMarkerSettings({});
            }
        } catch (error) {
            console.error('❌ Error loading viewer marker settings:', error);
            setMarkerSettings({});
        } finally {
            setLoading(false);
        }
    };

    const getCurrentSetting = (markerType) => {
        const existing = markerSettings[markerType];
        const defaultType = markerTypes.find(t => t.value === markerType);
        
        return {
            marker_type: markerType,
            icon_text: existing?.icon_text || defaultIcons[markerType] || '•',
            icon_image_url: existing?.icon_image_url || null,
            color: existing?.color || defaultType?.defaultColor || '#666666',
            size: existing?.size || 28,
            border_color: existing?.border_color || '#FFFFFF',
            border_width: existing?.border_width || 2,
            enabled: existing?.enabled !== false
        };
    };

    const updateSetting = (markerType, field, value) => {
        setMarkerSettings(prev => ({
            ...prev,
            [markerType]: {
                ...getCurrentSetting(markerType),
                [field]: value
            }
        }));
    };

    // Save marker setting with image URL immediately after upload
    const saveMarkerSettingsWithImage = async (markerType, imageUrl) => {
        try {
            const currentSetting = getCurrentSetting(markerType);

            const requestData = {
                marker_type: markerType,
                icon_text: '', // Clear text when image is set
                icon_image_url: imageUrl, // Use the provided image URL
                color: currentSetting.color || '#666666',
                size: currentSetting.size || 28,
                border_color: currentSetting.border_color || '#FFFFFF',
                border_width: currentSetting.border_width || 2,
                enabled: true
            };

            console.log(`💾 Auto-saving ${markerType} with image URL:`, imageUrl);
            console.log(`💾 Full request data:`, requestData);

            const response = await fetch(`/api/markers/settings/${viewerId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorData = await response.text();
                console.error(`❌ Failed to auto-save ${markerType} with image:`, errorData);
                throw new Error(`Failed to save ${markerType} setting: ${errorData}`);
            }

            const result = await response.json();
            console.log(`✅ Successfully auto-saved ${markerType} with image:`, result);
        } catch (error) {
            console.error('❌ Error auto-saving marker setting with image:', error);
            alert('Error saving marker setting: ' + error.message);
        }
    };

    const handleImageUpload = async (markerType, file) => {
        try {
            console.log('🖼️ Uploading image for marker type:', markerType);
            const formData = new FormData();
            formData.append('image', file);

            const response = await fetch(`/api/markers/upload/${viewerId}`, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Image upload response:', data);
                if (data.success && data.data.image_url) {
                    const imageUrl = data.data.image_url;
                    console.log('✅ Image URL received:', imageUrl);

                    // Update local state first
                    updateSetting(markerType, 'icon_image_url', imageUrl);
                    updateSetting(markerType, 'icon_text', ''); // Clear text when image is set

                    // Wait a moment for state to update, then save to database
                    setTimeout(async () => {
                        try {
                            console.log('💾 Saving marker setting with image URL to database...');
                            await saveMarkerSettingsWithImage(markerType, imageUrl);

                            // Trigger auto-refresh after successful save
                            console.log('🔄 Triggering auto-refresh after image upload and save');
                            if (onSave) {
                                onSave();
                            }
                        } catch (error) {
                            console.error('❌ Failed to save marker setting with image:', error);
                            alert('Failed to save marker setting: ' + error.message);
                        }
                    }, 100); // Small delay to ensure state update
                } else {
                    console.error('❌ Upload failed:', data);
                    alert('Image upload failed: ' + (data.message || 'Unknown error'));
                }
            } else {
                console.error('❌ Upload request failed:', response.status);
                alert('Image upload failed: ' + response.statusText);
            }
        } catch (error) {
            console.error('❌ Error uploading image:', error);
            alert('Error uploading image: ' + error.message);
        }
    };

    const saveSettings = async () => {
        try {
            setSaving(true);

            for (const [markerType, setting] of Object.entries(markerSettings)) {
                // Get current setting with defaults
                const currentSetting = getCurrentSetting(markerType);

                // Format the data to match backend expectations
                const requestData = {
                    marker_type: markerType,
                    icon_text: setting.icon_text || currentSetting.icon_text || defaultIcons[markerType] || '•',
                    icon_image_url: setting.icon_image_url || null,
                    color: setting.color || currentSetting.color || '#666666',
                    size: setting.size || currentSetting.size || 28,
                    border_color: setting.border_color || currentSetting.border_color || null,
                    border_width: setting.border_width || currentSetting.border_width || null,
                    enabled: setting.enabled !== false
                };

                // Ensure either icon_text or icon_image_url is provided (backend requirement)
                if (!requestData.icon_text && !requestData.icon_image_url) {
                    requestData.icon_text = defaultIcons[markerType] || '•';
                }

                console.log(`🔧 Saving ${markerType} setting:`, requestData);

                const response = await fetch(`/api/markers/settings/${viewerId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.text();
                    console.error(`❌ Failed to save ${markerType}:`, errorData);
                    throw new Error(`Failed to save ${markerType} setting: ${errorData}`);
                }

                const result = await response.json();
                console.log(`✅ Successfully saved ${markerType}:`, result);
            }

            alert('Marker settings saved successfully!');
            console.log('🔄 Calling onSave callback to refresh markers');
            if (onSave) {
                onSave();
            } else {
                console.warn('⚠️ No onSave callback provided');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            alert('Error saving settings: ' + error.message);
        } finally {
            setSaving(false);
        }
    };

    const resetToDefaults = () => {
        if (window.confirm('Reset all marker settings to defaults?')) {
            setMarkerSettings({});
        }
    };

    if (loading) {
        return (
            <div className="marker-customization-overlay">
                <div className="marker-customization-modal">
                    <div className="loading">Loading marker settings...</div>
                </div>
            </div>
        );
    }

    const currentSetting = getCurrentSetting(selectedMarkerType);

    return (
        <div className="marker-customization-overlay">
            <div className="marker-customization-modal">
                <div className="modal-header">
                    <h2>🎨 My Personal Marker Library</h2>
                    <p style={{fontSize: '12px', color: '#666', margin: '5px 0 0 0'}}>
                        These settings apply to ALL agents you view
                    </p>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>

                <div className="modal-content">
                    <div className="marker-type-selector">
                        <label>Select Marker Type:</label>
                        <select 
                            value={selectedMarkerType} 
                            onChange={(e) => setSelectedMarkerType(e.target.value)}
                        >
                            {markerTypes.map(type => (
                                <option key={type.value} value={type.value}>
                                    {type.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="marker-preview">
                        <h3>Preview:</h3>
                        <div 
                            className="marker-preview-circle"
                            style={{
                                width: `${currentSetting.size}px`,
                                height: `${currentSetting.size}px`,
                                backgroundColor: currentSetting.color,
                                border: `${currentSetting.border_width}px solid ${currentSetting.border_color}`,
                                fontSize: `${Math.max(10, currentSetting.size * 0.4)}px`
                            }}
                        >
                            {currentSetting.icon_image_url && currentSetting.icon_image_url.trim() !== '' ? (
                                <img
                                    src={currentSetting.icon_image_url}
                                    alt="Custom marker"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                        borderRadius: '50%'
                                    }}
                                    onError={(e) => {
                                        console.error('❌ Failed to load image:', currentSetting.icon_image_url);
                                        e.target.style.display = 'none';
                                    }}
                                    onLoad={() => {
                                        console.log('✅ Image loaded successfully:', currentSetting.icon_image_url);
                                    }}
                                />
                            ) : (
                                currentSetting.icon_text || '•'
                            )}
                        </div>
                    </div>

                    <div className="settings-form">
                        <div className="form-group">
                            <label>Icon Text:</label>
                            <input
                                type="text"
                                value={currentSetting.icon_text || ''}
                                onChange={(e) => {
                                    updateSetting(selectedMarkerType, 'icon_text', e.target.value);
                                    // Clear image when text is entered
                                    if (e.target.value.trim() !== '') {
                                        updateSetting(selectedMarkerType, 'icon_image_url', '');
                                    }
                                }}
                                placeholder="Enter emoji or text"
                                maxLength="3"
                                disabled={currentSetting.icon_image_url && currentSetting.icon_image_url.trim() !== ''}
                            />
                            {currentSetting.icon_image_url && currentSetting.icon_image_url.trim() !== '' && (
                                <small style={{ color: '#666', fontSize: '12px' }}>
                                    Text input disabled while custom image is set
                                </small>
                            )}
                        </div>

                        <div className="form-group">
                            <label>Custom Image:</label>
                            <input
                                type="file"
                                accept="image/*"
                                onChange={(e) => {
                                    if (e.target.files[0]) {
                                        handleImageUpload(selectedMarkerType, e.target.files[0]);
                                    }
                                }}
                            />
                            {currentSetting.icon_image_url && currentSetting.icon_image_url.trim() !== '' && (
                                <button
                                    type="button"
                                    onClick={() => {
                                        updateSetting(selectedMarkerType, 'icon_image_url', '');
                                        // Restore default icon text when image is removed
                                        if (!currentSetting.icon_text || currentSetting.icon_text.trim() === '') {
                                            updateSetting(selectedMarkerType, 'icon_text', defaultIcons[selectedMarkerType] || '•');
                                        }
                                    }}
                                    style={{
                                        marginTop: '8px',
                                        padding: '6px 12px',
                                        backgroundColor: '#dc3545',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                    }}
                                >
                                    Remove Image
                                </button>
                            )}
                        </div>

                        <div className="form-group">
                            <label>Color:</label>
                            <input
                                type="color"
                                value={currentSetting.color}
                                onChange={(e) => updateSetting(selectedMarkerType, 'color', e.target.value)}
                            />
                        </div>

                        <div className="form-group">
                            <label>Size: {currentSetting.size}px</label>
                            <input
                                type="range"
                                min="16"
                                max="64"
                                value={currentSetting.size}
                                onChange={(e) => updateSetting(selectedMarkerType, 'size', parseInt(e.target.value))}
                            />
                        </div>

                        <div className="form-group">
                            <label>Border Color:</label>
                            <input
                                type="color"
                                value={currentSetting.border_color}
                                onChange={(e) => updateSetting(selectedMarkerType, 'border_color', e.target.value)}
                            />
                        </div>

                        <div className="form-group">
                            <label>Border Width: {currentSetting.border_width}px</label>
                            <input
                                type="range"
                                min="0"
                                max="10"
                                value={currentSetting.border_width}
                                onChange={(e) => updateSetting(selectedMarkerType, 'border_width', parseInt(e.target.value))}
                            />
                        </div>
                    </div>
                </div>

                <div className="modal-footer">
                    <button onClick={resetToDefaults} className="reset-btn">
                        Reset to Defaults
                    </button>
                    <button onClick={saveSettings} disabled={saving} className="save-btn">
                        {saving ? 'Saving...' : 'Save Settings'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default MarkerCustomization;

import React, { useState, useEffect } from 'react';
import './DeliveryAssignmentModal.css';

const DeliveryAssignmentModal = ({ isOpen, onClose, onAssign, selectedAgent, selectedLocation }) => {
    const [taskTypes, setTaskTypes] = useState([]);
    const [customers, setCustomers] = useState([]);
    const [formData, setFormData] = useState({
        customer_id: '',
        task_type: 'pick_only',
        task_description: '',
        target_location: { longitude: 0, latitude: 0 },
        geofence_radius: 400,
        delivery_fee: '',
        notes: '',
        scheduled_date: '',
        pickup_locations: [{ longitude: 0, latitude: 0, description: '' }],
        drop_locations: [{ longitude: 0, latitude: 0, description: '' }],
        custom_scenario: 'multi_stop',
        custom_tasks: [{
            type: 'visit',
            location: { longitude: 0, latitude: 0 },
            description: '',
            duration_minutes: 15,
            requirements: '',
            priority: 'medium'
        }]
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        if (isOpen) {
            fetchTaskTypes();
            fetchCustomers();
            
            // Set location if provided
            if (selectedLocation) {
                setFormData(prev => ({
                    ...prev,
                    target_location: selectedLocation
                }));
            }
        }
    }, [isOpen, selectedLocation]);

    const fetchTaskTypes = async () => {
        try {
            const response = await fetch('/api/delivery-assignments/task-types');
            const data = await response.json();
            if (data.success) {
                setTaskTypes(data.data.task_types);
                setFormData(prev => ({
                    ...prev,
                    geofence_radius: data.data.default_geofence_radius
                }));
            }
        } catch (error) {
            console.error('Failed to fetch task types:', error);
        }
    };

    const fetchCustomers = async () => {
        try {
            const response = await fetch('/api/users/customers');
            const data = await response.json();
            if (data.success) {
                setCustomers(data.data.customers || []);
            }
        } catch (error) {
            console.error('Failed to fetch customers:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;

        if (name === 'longitude' || name === 'latitude') {
            setFormData(prev => ({
                ...prev,
                target_location: {
                    ...prev.target_location,
                    [name]: parseFloat(value) || 0
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleLocationChange = (type, index, field, value) => {
        setFormData(prev => ({
            ...prev,
            [type]: prev[type].map((location, i) =>
                i === index ? { ...location, [field]: field === 'description' ? value : parseFloat(value) || 0 } : location
            )
        }));
    };

    const addLocation = (type) => {
        setFormData(prev => ({
            ...prev,
            [type]: [...prev[type], { longitude: 0, latitude: 0, description: '' }]
        }));
    };

    const removeLocation = (type, index) => {
        if (formData[type].length > 1) {
            setFormData(prev => ({
                ...prev,
                [type]: prev[type].filter((_, i) => i !== index)
            }));
        }
    };

    const handleCustomTaskChange = (index, field, value) => {
        setFormData(prev => ({
            ...prev,
            custom_tasks: prev.custom_tasks.map((task, i) =>
                i === index ? { ...task, [field]: field.includes('location.') ?
                    { ...task.location, [field.split('.')[1]]: parseFloat(value) || 0 } :
                    value
                } : task
            )
        }));
    };

    const addCustomTask = () => {
        setFormData(prev => ({
            ...prev,
            custom_tasks: [...prev.custom_tasks, {
                type: 'visit',
                location: { longitude: 0, latitude: 0 },
                description: '',
                duration_minutes: 15,
                requirements: '',
                priority: 'medium'
            }]
        }));
    };

    const removeCustomTask = (index) => {
        if (formData.custom_tasks.length > 1) {
            setFormData(prev => ({
                ...prev,
                custom_tasks: prev.custom_tasks.filter((_, i) => i !== index)
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            let assignmentData;

            if (formData.task_type === 'full_delivery') {
                // For full delivery, create multiple assignments for the workflow
                const assignments = [];

                // Create pickup assignments
                formData.pickup_locations.forEach((location, index) => {
                    if (location.longitude !== 0 || location.latitude !== 0) {
                        assignments.push({
                            agent_id: selectedAgent.agent_id,
                            customer_id: formData.customer_id,
                            task_type: 'pick_only',
                            task_description: `${formData.task_description} - Pickup #${index + 1}: ${location.description}`,
                            target_location: { longitude: location.longitude, latitude: location.latitude },
                            geofence_radius: parseInt(formData.geofence_radius),
                            delivery_fee: index === 0 ? (formData.delivery_fee ? parseFloat(formData.delivery_fee) : null) : null,
                            notes: `${formData.notes || ''} | Pickup Location: ${location.description}`,
                            scheduled_date: formData.scheduled_date ? formData.scheduled_date + ':00' : null
                        });
                    }
                });

                // Create drop assignments
                formData.drop_locations.forEach((location, index) => {
                    if (location.longitude !== 0 || location.latitude !== 0) {
                        assignments.push({
                            agent_id: selectedAgent.agent_id,
                            customer_id: formData.customer_id,
                            task_type: 'drop_only',
                            task_description: `${formData.task_description} - Drop #${index + 1}: ${location.description}`,
                            target_location: { longitude: location.longitude, latitude: location.latitude },
                            geofence_radius: parseInt(formData.geofence_radius),
                            delivery_fee: null,
                            notes: `${formData.notes || ''} | Drop Location: ${location.description}`,
                            scheduled_date: formData.scheduled_date ? formData.scheduled_date + ':00' : null
                        });
                    }
                });

                // Create all assignments
                const results = [];
                for (const assignment of assignments) {
                    const response = await fetch('/api/delivery-assignments', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(assignment)
                    });

                    const data = await response.json();
                    if (data.success) {
                        results.push(data.data);
                    } else {
                        throw new Error(data.error || 'Failed to create assignment');
                    }
                }

                onAssign(results);
                onClose();
                resetForm();
                return;
            } else if (formData.task_type === 'custom') {
                // For custom tasks, create assignments based on custom workflow
                const assignments = [];

                formData.custom_tasks.forEach((task, index) => {
                    if (task.location.longitude !== 0 || task.location.latitude !== 0) {
                        const taskTypeMap = {
                            'pickup': 'pick_only',
                            'delivery': 'drop_only',
                            'visit': 'store_visit',
                            'inspection': 'store_visit',
                            'maintenance': 'store_visit',
                            'survey': 'store_visit',
                            'photo': 'store_visit',
                            'signature': 'store_visit',
                            'payment': 'store_visit',
                            'wait': 'store_visit',
                            'custom': 'custom'
                        };

                        assignments.push({
                            agent_id: selectedAgent.agent_id,
                            customer_id: formData.customer_id,
                            task_type: taskTypeMap[task.type] || 'custom',
                            task_description: `${formData.custom_scenario.replace('_', ' ').toUpperCase()} - Task #${index + 1}: ${task.type.replace('_', ' ').toUpperCase()} | ${task.description}`,
                            target_location: task.location,
                            geofence_radius: parseInt(formData.geofence_radius),
                            delivery_fee: index === 0 ? (formData.delivery_fee ? parseFloat(formData.delivery_fee) : null) : null,
                            notes: `${formData.notes || ''} | Priority: ${task.priority.toUpperCase()} | Duration: ${task.duration_minutes}min | Requirements: ${task.requirements}`,
                            scheduled_date: formData.scheduled_date ? formData.scheduled_date + ':00' : null
                        });
                    }
                });

                // Create all custom assignments
                const results = [];
                for (const assignment of assignments) {
                    const response = await fetch('/api/delivery-assignments', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(assignment)
                    });

                    const data = await response.json();
                    if (data.success) {
                        results.push(data.data);
                    } else {
                        throw new Error(data.error || 'Failed to create custom assignment');
                    }
                }

                onAssign(results);
                onClose();
                resetForm();
                return;
            } else {
                // Single assignment for other task types
                assignmentData = {
                    agent_id: selectedAgent.agent_id,
                    customer_id: formData.customer_id,
                    task_type: formData.task_type,
                    task_description: formData.task_description,
                    target_location: formData.target_location,
                    geofence_radius: parseInt(formData.geofence_radius),
                    delivery_fee: formData.delivery_fee ? parseFloat(formData.delivery_fee) : null,
                    notes: formData.notes || null,
                    scheduled_date: formData.scheduled_date ? formData.scheduled_date + ':00' : null
                };
            }

            const response = await fetch('/api/delivery-assignments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(assignmentData)
            });

            const data = await response.json();
            
            if (data.success) {
                onAssign(data.data);
                onClose();
                resetForm();
            } else {
                setError(data.error || 'Failed to create assignment');
            }
        } catch (error) {
            setError('Network error: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setFormData({
            customer_id: '',
            task_type: 'pick_only',
            task_description: '',
            target_location: { longitude: 0, latitude: 0 },
            geofence_radius: 400,
            delivery_fee: '',
            notes: '',
            scheduled_date: '',
            pickup_locations: [{ longitude: 0, latitude: 0, description: '' }],
            drop_locations: [{ longitude: 0, latitude: 0, description: '' }],
            custom_scenario: 'multi_stop',
            custom_tasks: [{
                type: 'visit',
                location: { longitude: 0, latitude: 0 },
                description: '',
                duration_minutes: 15,
                requirements: '',
                priority: 'medium'
            }]
        });
        setError('');
    };

    const handleClose = () => {
        resetForm();
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content delivery-assignment-modal">
                <div className="modal-header">
                    <h2>🎯 Assign Delivery Task</h2>
                    <button className="close-button" onClick={handleClose}>×</button>
                </div>

                <div className="modal-body">
                    {selectedAgent && (
                        <div className="agent-info">
                            <strong>Agent:</strong> {selectedAgent.name} ({selectedAgent.role})
                        </div>
                    )}

                    {error && <div className="error-message">{error}</div>}

                    <form onSubmit={handleSubmit}>
                        <div className="form-row">
                            <div className="form-group">
                                <label>Customer *</label>
                                <select
                                    name="customer_id"
                                    value={formData.customer_id}
                                    onChange={handleInputChange}
                                    required
                                >
                                    <option value="">Select Customer</option>
                                    {customers.map(customer => (
                                        <option key={customer.id} value={customer.id}>
                                            {customer.name} ({customer.phone})
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="form-group">
                                <label>Task Type *</label>
                                <select
                                    name="task_type"
                                    value={formData.task_type}
                                    onChange={handleInputChange}
                                    required
                                >
                                    {taskTypes.map(type => (
                                        <option key={type.value} value={type.value}>
                                            {type.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        <div className="form-group">
                            <label>Task Description *</label>
                            <textarea
                                name="task_description"
                                value={formData.task_description}
                                onChange={handleInputChange}
                                placeholder="Describe the task details..."
                                required
                                rows="3"
                            />
                        </div>

                        {/* Enhanced Full Delivery Workflow */}
                        {formData.task_type === 'full_delivery' && (
                            <>
                                {/* Pickup Locations */}
                                <div className="workflow-section">
                                    <div className="workflow-header">
                                        <h4>📦 Pickup Locations</h4>
                                        <button
                                            type="button"
                                            onClick={() => addLocation('pickup_locations')}
                                            className="add-location-btn"
                                        >
                                            + Add Pickup
                                        </button>
                                    </div>
                                    {formData.pickup_locations.map((location, index) => (
                                        <div key={index} className="location-item">
                                            <div className="location-header">
                                                <span>Pickup #{index + 1}</span>
                                                {formData.pickup_locations.length > 1 && (
                                                    <button
                                                        type="button"
                                                        onClick={() => removeLocation('pickup_locations', index)}
                                                        className="remove-btn"
                                                    >
                                                        ×
                                                    </button>
                                                )}
                                            </div>
                                            <div className="form-row">
                                                <div className="form-group">
                                                    <label>Longitude</label>
                                                    <input
                                                        type="number"
                                                        value={location.longitude}
                                                        onChange={(e) => handleLocationChange('pickup_locations', index, 'longitude', e.target.value)}
                                                        step="any"
                                                    />
                                                </div>
                                                <div className="form-group">
                                                    <label>Latitude</label>
                                                    <input
                                                        type="number"
                                                        value={location.latitude}
                                                        onChange={(e) => handleLocationChange('pickup_locations', index, 'latitude', e.target.value)}
                                                        step="any"
                                                    />
                                                </div>
                                            </div>
                                            <div className="form-group">
                                                <label>Description</label>
                                                <input
                                                    type="text"
                                                    value={location.description}
                                                    onChange={(e) => handleLocationChange('pickup_locations', index, 'description', e.target.value)}
                                                    placeholder="e.g., Warehouse A, Electronics Store..."
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* Drop Locations */}
                                <div className="workflow-section">
                                    <div className="workflow-header">
                                        <h4>🚚 Drop Locations</h4>
                                        <button
                                            type="button"
                                            onClick={() => addLocation('drop_locations')}
                                            className="add-location-btn"
                                        >
                                            + Add Drop
                                        </button>
                                    </div>
                                    {formData.drop_locations.map((location, index) => (
                                        <div key={index} className="location-item">
                                            <div className="location-header">
                                                <span>Drop #{index + 1}</span>
                                                {formData.drop_locations.length > 1 && (
                                                    <button
                                                        type="button"
                                                        onClick={() => removeLocation('drop_locations', index)}
                                                        className="remove-btn"
                                                    >
                                                        ×
                                                    </button>
                                                )}
                                            </div>
                                            <div className="form-row">
                                                <div className="form-group">
                                                    <label>Longitude</label>
                                                    <input
                                                        type="number"
                                                        value={location.longitude}
                                                        onChange={(e) => handleLocationChange('drop_locations', index, 'longitude', e.target.value)}
                                                        step="any"
                                                    />
                                                </div>
                                                <div className="form-group">
                                                    <label>Latitude</label>
                                                    <input
                                                        type="number"
                                                        value={location.latitude}
                                                        onChange={(e) => handleLocationChange('drop_locations', index, 'latitude', e.target.value)}
                                                        step="any"
                                                    />
                                                </div>
                                            </div>
                                            <div className="form-group">
                                                <label>Description</label>
                                                <input
                                                    type="text"
                                                    value={location.description}
                                                    onChange={(e) => handleLocationChange('drop_locations', index, 'description', e.target.value)}
                                                    placeholder="e.g., Customer Home, Office Building..."
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </>
                        )}

                        {/* Enhanced Custom Task Builder */}
                        {formData.task_type === 'custom' && (
                            <div className="custom-task-builder">
                                <div className="scenario-selector">
                                    <h4>🎯 Custom Scenario</h4>
                                    <select
                                        value={formData.custom_scenario}
                                        onChange={(e) => setFormData(prev => ({ ...prev, custom_scenario: e.target.value }))}
                                        className="scenario-select"
                                    >
                                        <option value="multi_stop">🚶 Multi-Stop Route</option>
                                        <option value="inspection">🔍 Inspection & Audit</option>
                                        <option value="maintenance">🔧 Maintenance & Repair</option>
                                        <option value="survey">📋 Survey & Data Collection</option>
                                        <option value="emergency">🚨 Emergency Response</option>
                                        <option value="delivery_plus">📦 Delivery + Services</option>
                                        <option value="time_sensitive">⏰ Time-Critical Tasks</option>
                                        <option value="custom_workflow">⚙️ Custom Workflow</option>
                                    </select>
                                </div>

                                <div className="scenario-description">
                                    {formData.custom_scenario === 'multi_stop' && (
                                        <p>📍 Create a route with multiple stops, each with specific tasks and time requirements.</p>
                                    )}
                                    {formData.custom_scenario === 'inspection' && (
                                        <p>🔍 Set up inspection points with checklists, photo requirements, and compliance checks.</p>
                                    )}
                                    {formData.custom_scenario === 'maintenance' && (
                                        <p>🔧 Schedule maintenance tasks with equipment checks, repairs, and status updates.</p>
                                    )}
                                    {formData.custom_scenario === 'survey' && (
                                        <p>📋 Design data collection points with forms, measurements, and reporting requirements.</p>
                                    )}
                                    {formData.custom_scenario === 'emergency' && (
                                        <p>🚨 Create emergency response workflow with priority levels and escalation procedures.</p>
                                    )}
                                    {formData.custom_scenario === 'delivery_plus' && (
                                        <p>📦 Combine delivery with additional services like installation, setup, or training.</p>
                                    )}
                                    {formData.custom_scenario === 'time_sensitive' && (
                                        <p>⏰ Set up time-critical tasks with strict deadlines and priority handling.</p>
                                    )}
                                    {formData.custom_scenario === 'custom_workflow' && (
                                        <p>⚙️ Build completely custom workflow with your own task types and requirements.</p>
                                    )}
                                </div>

                                <div className="custom-tasks-section">
                                    <div className="workflow-header">
                                        <h4>📋 Task Sequence</h4>
                                        <button
                                            type="button"
                                            onClick={addCustomTask}
                                            className="add-task-btn"
                                        >
                                            + Add Task
                                        </button>
                                    </div>

                                    {formData.custom_tasks.map((task, index) => (
                                        <div key={index} className="custom-task-item">
                                            <div className="task-header">
                                                <span>Task #{index + 1}</span>
                                                <div className="task-controls">
                                                    <select
                                                        value={task.priority}
                                                        onChange={(e) => handleCustomTaskChange(index, 'priority', e.target.value)}
                                                        className="priority-select"
                                                    >
                                                        <option value="low">🟢 Low</option>
                                                        <option value="medium">🟡 Medium</option>
                                                        <option value="high">🟠 High</option>
                                                        <option value="critical">🔴 Critical</option>
                                                    </select>
                                                    {formData.custom_tasks.length > 1 && (
                                                        <button
                                                            type="button"
                                                            onClick={() => removeCustomTask(index)}
                                                            className="remove-btn"
                                                        >
                                                            ×
                                                        </button>
                                                    )}
                                                </div>
                                            </div>

                                            <div className="form-row">
                                                <div className="form-group">
                                                    <label>Task Type</label>
                                                    <select
                                                        value={task.type}
                                                        onChange={(e) => handleCustomTaskChange(index, 'type', e.target.value)}
                                                    >
                                                        <option value="visit">🏢 Visit Location</option>
                                                        <option value="pickup">📦 Pickup Item</option>
                                                        <option value="delivery">🚚 Deliver Item</option>
                                                        <option value="inspection">🔍 Inspect/Audit</option>
                                                        <option value="maintenance">🔧 Maintenance</option>
                                                        <option value="survey">📋 Survey/Data</option>
                                                        <option value="photo">📸 Photo Documentation</option>
                                                        <option value="signature">✍️ Get Signature</option>
                                                        <option value="payment">💳 Collect Payment</option>
                                                        <option value="wait">⏳ Wait/Standby</option>
                                                        <option value="custom">⚙️ Custom Action</option>
                                                    </select>
                                                </div>
                                                <div className="form-group">
                                                    <label>Duration (minutes)</label>
                                                    <input
                                                        type="number"
                                                        value={task.duration_minutes}
                                                        onChange={(e) => handleCustomTaskChange(index, 'duration_minutes', parseInt(e.target.value) || 15)}
                                                        min="5"
                                                        max="480"
                                                    />
                                                </div>
                                            </div>

                                            <div className="form-row">
                                                <div className="form-group">
                                                    <label>Longitude</label>
                                                    <input
                                                        type="number"
                                                        value={task.location.longitude}
                                                        onChange={(e) => handleCustomTaskChange(index, 'location.longitude', e.target.value)}
                                                        step="any"
                                                    />
                                                </div>
                                                <div className="form-group">
                                                    <label>Latitude</label>
                                                    <input
                                                        type="number"
                                                        value={task.location.latitude}
                                                        onChange={(e) => handleCustomTaskChange(index, 'location.latitude', e.target.value)}
                                                        step="any"
                                                    />
                                                </div>
                                            </div>

                                            <div className="form-group">
                                                <label>Task Description</label>
                                                <textarea
                                                    value={task.description}
                                                    onChange={(e) => handleCustomTaskChange(index, 'description', e.target.value)}
                                                    placeholder="Describe what needs to be done at this location..."
                                                    rows="2"
                                                />
                                            </div>

                                            <div className="form-group">
                                                <label>Special Requirements</label>
                                                <textarea
                                                    value={task.requirements}
                                                    onChange={(e) => handleCustomTaskChange(index, 'requirements', e.target.value)}
                                                    placeholder="Equipment needed, access requirements, safety protocols..."
                                                    rows="2"
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Simple Location for other task types */}
                        {formData.task_type !== 'full_delivery' && (
                            <div className="form-row">
                                <div className="form-group">
                                    <label>Longitude *</label>
                                    <input
                                        type="number"
                                        name="longitude"
                                        value={formData.target_location.longitude}
                                        onChange={handleInputChange}
                                        step="any"
                                        required
                                    />
                                </div>

                                <div className="form-group">
                                    <label>Latitude *</label>
                                    <input
                                        type="number"
                                        name="latitude"
                                        value={formData.target_location.latitude}
                                        onChange={handleInputChange}
                                        step="any"
                                        required
                                    />
                                </div>
                            </div>
                        )}

                        <div className="form-row">
                            <div className="form-group">
                                <label>Geofence Radius (meters) *</label>
                                <input
                                    type="number"
                                    name="geofence_radius"
                                    value={formData.geofence_radius}
                                    onChange={handleInputChange}
                                    min="50"
                                    max="2000"
                                    required
                                />
                                <small>Range: 50-2000 meters</small>
                            </div>

                            <div className="form-group">
                                <label>Delivery Fee</label>
                                <input
                                    type="number"
                                    name="delivery_fee"
                                    value={formData.delivery_fee}
                                    onChange={handleInputChange}
                                    step="0.01"
                                    min="0"
                                    placeholder="0.00"
                                />
                            </div>
                        </div>

                        <div className="form-group">
                            <label>Scheduled Date</label>
                            <input
                                type="datetime-local"
                                name="scheduled_date"
                                value={formData.scheduled_date}
                                onChange={handleInputChange}
                            />
                        </div>

                        <div className="form-group">
                            <label>Notes</label>
                            <textarea
                                name="notes"
                                value={formData.notes}
                                onChange={handleInputChange}
                                placeholder="Additional notes or instructions..."
                                rows="2"
                            />
                        </div>

                        <div className="form-actions">
                            <button type="button" onClick={handleClose} disabled={loading}>
                                Cancel
                            </button>
                            <button type="submit" disabled={loading} className="primary">
                                {loading ? 'Creating...' : '🎯 Assign Task'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default DeliveryAssignmentModal;

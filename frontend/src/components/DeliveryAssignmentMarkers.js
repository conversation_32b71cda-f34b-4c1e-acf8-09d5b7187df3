import React, { useEffect, useState } from 'react';

const DeliveryAssignmentMarkers = ({ map, agentId, currentLocation }) => {
    const [assignments, setAssignments] = useState([]);
    const [markers, setMarkers] = useState([]);
    const [geofenceCircles, setGeofenceCircles] = useState([]);

    useEffect(() => {
        if (agentId) {
            fetchAssignments();
        }
    }, [agentId]);

    useEffect(() => {
        if (map && assignments.length > 0) {
            updateMapMarkers();
        }
        return () => {
            clearMapMarkers();
        };
    }, [map, assignments]);

    useEffect(() => {
        if (currentLocation && assignments.length > 0) {
            checkGeofenceStatus();
        }
    }, [currentLocation, assignments]);

    const fetchAssignments = async () => {
        try {
            const response = await fetch(`/api/delivery-assignments/agent/${agentId}`);
            const data = await response.json();
            if (data.success) {
                setAssignments(data.data.assignments || []);
            }
        } catch (error) {
            console.error('Failed to fetch delivery assignments:', error);
        }
    };

    const checkGeofenceStatus = async () => {
        if (!currentLocation || !agentId) return;

        try {
            const response = await fetch(
                `/api/delivery-assignments/agent/${agentId}/geofence-status?lat=${currentLocation.latitude}&lng=${currentLocation.longitude}`
            );
            const data = await response.json();
            if (data.success) {
                // Update assignments with geofence status
                setAssignments(prev => {
                    const updatedAssignments = [...prev];
                    data.data.assignments?.forEach(statusAssignment => {
                        const index = updatedAssignments.findIndex(a => a.id === statusAssignment.id);
                        if (index !== -1) {
                            updatedAssignments[index] = {
                                ...updatedAssignments[index],
                                is_within_geofence: statusAssignment.is_within_geofence,
                                distance_to_target: statusAssignment.distance_to_target
                            };
                        }
                    });
                    return updatedAssignments;
                });
            }
        } catch (error) {
            console.error('Failed to check geofence status:', error);
        }
    };

    const updateMapMarkers = () => {
        clearMapMarkers();

        const newMarkers = [];
        const newCircles = [];

        assignments.forEach(assignment => {
            // Create delivery marker
            const markerIcon = getTaskTypeIcon(assignment.task_type, assignment.is_within_geofence);
            
            const marker = new window.google.maps.Marker({
                position: {
                    lat: assignment.target_location.latitude,
                    lng: assignment.target_location.longitude
                },
                map: map,
                title: `${assignment.task_description}`,
                icon: markerIcon,
                zIndex: 1000
            });

            // Create info window
            const infoWindow = new window.google.maps.InfoWindow({
                content: createInfoWindowContent(assignment)
            });

            marker.addListener('click', () => {
                // Close other info windows
                markers.forEach(m => {
                    if (m.infoWindow) {
                        m.infoWindow.close();
                    }
                });
                infoWindow.open(map, marker);
            });

            marker.infoWindow = infoWindow;
            newMarkers.push(marker);

            // Create geofence circle
            const circle = new window.google.maps.Circle({
                strokeColor: assignment.is_within_geofence ? '#10b981' : '#f59e0b',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: assignment.is_within_geofence ? '#10b981' : '#f59e0b',
                fillOpacity: 0.15,
                map: map,
                center: {
                    lat: assignment.target_location.latitude,
                    lng: assignment.target_location.longitude
                },
                radius: assignment.geofence_radius,
                zIndex: 100
            });

            newCircles.push(circle);
        });

        setMarkers(newMarkers);
        setGeofenceCircles(newCircles);
    };

    const clearMapMarkers = () => {
        markers.forEach(marker => {
            if (marker.infoWindow) {
                marker.infoWindow.close();
            }
            marker.setMap(null);
        });
        geofenceCircles.forEach(circle => {
            circle.setMap(null);
        });
        setMarkers([]);
        setGeofenceCircles([]);
    };

    const getTaskTypeIcon = (taskType, isWithinGeofence) => {
        const baseColor = isWithinGeofence ? '#10b981' : '#f59e0b';
        const icons = {
            'pick_only': '📦',
            'drop_only': '🚚',
            'store_visit': '🏪',
            'full_delivery': '🎯',
            'custom': '⭐'
        };

        return {
            url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="${baseColor}" stroke="white" stroke-width="2"/>
                    <text x="20" y="26" text-anchor="middle" font-size="16" fill="white">
                        ${icons[taskType] || '📍'}
                    </text>
                </svg>
            `)}`,
            scaledSize: new window.google.maps.Size(40, 40),
            anchor: new window.google.maps.Point(20, 20)
        };
    };

    const createInfoWindowContent = (assignment) => {
        const statusColor = assignment.is_within_geofence ? '#10b981' : '#f59e0b';
        const statusText = assignment.is_within_geofence ? 'Within Geofence' : 'Outside Geofence';
        const distanceText = assignment.distance_to_target !== undefined 
            ? `${Math.round(assignment.distance_to_target)}m away` 
            : '';

        return `
            <div style="max-width: 300px; padding: 12px;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <span style="font-size: 20px; margin-right: 8px;">
                        ${getTaskTypeIcon(assignment.task_type, assignment.is_within_geofence).url.includes('📦') ? '📦' : 
                          getTaskTypeIcon(assignment.task_type, assignment.is_within_geofence).url.includes('🚚') ? '🚚' :
                          getTaskTypeIcon(assignment.task_type, assignment.is_within_geofence).url.includes('🏪') ? '🏪' :
                          getTaskTypeIcon(assignment.task_type, assignment.is_within_geofence).url.includes('🎯') ? '🎯' : '⭐'}
                    </span>
                    <strong style="font-size: 16px;">${assignment.task_type.replace('_', ' ').toUpperCase()}</strong>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <strong>Task:</strong> ${assignment.task_description}
                </div>
                
                <div style="margin-bottom: 8px;">
                    <strong>Customer:</strong> ${assignment.customer_name}
                </div>
                
                <div style="margin-bottom: 8px;">
                    <strong>Status:</strong> 
                    <span style="color: ${statusColor}; font-weight: bold;">
                        ${assignment.status.replace('_', ' ').toUpperCase()}
                    </span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <strong>Geofence:</strong> 
                    <span style="color: ${statusColor}; font-weight: bold;">
                        ${statusText} ${distanceText}
                    </span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <strong>Radius:</strong> ${assignment.geofence_radius}m
                </div>
                
                ${assignment.delivery_fee ? `
                    <div style="margin-bottom: 8px;">
                        <strong>Fee:</strong> ₹${assignment.delivery_fee}
                    </div>
                ` : ''}
                
                ${assignment.notes ? `
                    <div style="margin-bottom: 8px;">
                        <strong>Notes:</strong> ${assignment.notes}
                    </div>
                ` : ''}
                
                <div style="margin-top: 12px; padding-top: 8px; border-top: 1px solid #e5e7eb;">
                    <button 
                        onclick="updateAssignmentStatus('${assignment.id}', 'in_transit')"
                        style="background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 8px; font-size: 12px;"
                    >
                        Start Task
                    </button>
                    <button 
                        onclick="updateAssignmentStatus('${assignment.id}', 'delivered')"
                        style="background: #10b981; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;"
                    >
                        Complete
                    </button>
                </div>
            </div>
        `;
    };

    // Expose function to global scope for info window buttons
    useEffect(() => {
        window.updateAssignmentStatus = async (assignmentId, status) => {
            try {
                const response = await fetch(`/api/delivery-assignments/${assignmentId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ status })
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ Assignment status updated:', data.data);
                    fetchAssignments(); // Refresh assignments
                } else {
                    console.error('❌ Failed to update assignment status:', data.error);
                }
            } catch (error) {
                console.error('❌ Network error updating assignment status:', error);
            }
        };

        return () => {
            delete window.updateAssignmentStatus;
        };
    }, []);

    return null; // This component doesn't render anything directly
};

export default DeliveryAssignmentMarkers;

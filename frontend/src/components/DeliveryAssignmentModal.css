.delivery-assignment-modal {
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.delivery-assignment-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.delivery-assignment-modal .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.delivery-assignment-modal .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.delivery-assignment-modal .close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.delivery-assignment-modal .modal-body {
    padding: 24px;
}

.delivery-assignment-modal .agent-info {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
    font-size: 14px;
}

.delivery-assignment-modal .error-message {
    background: #fee;
    color: #c53030;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #fc8181;
    font-size: 14px;
}

.delivery-assignment-modal .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.delivery-assignment-modal .form-group {
    margin-bottom: 16px;
}

.delivery-assignment-modal .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.delivery-assignment-modal .form-group input,
.delivery-assignment-modal .form-group select,
.delivery-assignment-modal .form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.delivery-assignment-modal .form-group input:focus,
.delivery-assignment-modal .form-group select:focus,
.delivery-assignment-modal .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.delivery-assignment-modal .form-group small {
    display: block;
    margin-top: 4px;
    color: #6b7280;
    font-size: 12px;
}

.delivery-assignment-modal .form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.delivery-assignment-modal .form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.delivery-assignment-modal .form-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 100px;
}

.delivery-assignment-modal .form-actions button:not(.primary) {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.delivery-assignment-modal .form-actions button:not(.primary):hover {
    background: #e5e7eb;
}

.delivery-assignment-modal .form-actions button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.delivery-assignment-modal .form-actions button.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.delivery-assignment-modal .form-actions button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Task type specific styling */
.delivery-assignment-modal select[name="task_type"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

/* Geofence radius input styling */
.delivery-assignment-modal input[name="geofence_radius"] {
    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: #0ea5e9;
}

.delivery-assignment-modal input[name="geofence_radius"]:focus {
    border-color: #0284c7;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Location inputs styling */
.delivery-assignment-modal input[name="longitude"],
.delivery-assignment-modal input[name="latitude"] {
    font-family: 'Courier New', monospace;
    background: #fafafa;
}

/* Responsive design */
@media (max-width: 640px) {
    .delivery-assignment-modal {
        width: 95%;
        margin: 10px;
    }
    
    .delivery-assignment-modal .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .delivery-assignment-modal .modal-header {
        padding: 16px;
    }
    
    .delivery-assignment-modal .modal-body {
        padding: 16px;
    }
    
    .delivery-assignment-modal .form-actions {
        flex-direction: column;
    }
    
    .delivery-assignment-modal .form-actions button {
        width: 100%;
    }
}

/* Enhanced Workflow Styling */
.workflow-section {
    margin: 20px 0;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.workflow-header h4 {
    margin: 0;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
}

.add-location-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.add-location-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.location-item {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
}

.location-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.location-header span {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.remove-btn {
    background: #ef4444;
    color: white;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.remove-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

/* Custom Task Builder Styling */
.custom-task-builder {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #8b5cf6;
    border-radius: 12px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.scenario-selector {
    margin-bottom: 20px;
}

.scenario-selector h4 {
    margin: 0 0 12px 0;
    color: #7c3aed;
    font-size: 18px;
    font-weight: 700;
}

.scenario-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #8b5cf6;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    background: white;
    color: #374151;
}

.scenario-description {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 12px 16px;
    margin: 12px 0 20px 0;
}

.scenario-description p {
    margin: 0;
    color: #92400e;
    font-size: 14px;
    font-weight: 500;
}

.custom-tasks-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #d1d5db;
}

.add-task-btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.add-task-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.custom-task-item {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    position: relative;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.task-header span {
    font-weight: 600;
    color: #374151;
    font-size: 16px;
}

.task-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.priority-select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.priority-select option {
    font-weight: 500;
}

/* Task type specific styling */
.custom-task-item select[value="visit"] {
    background-color: #dbeafe;
    border-color: #3b82f6;
}

.custom-task-item select[value="pickup"] {
    background-color: #dcfce7;
    border-color: #10b981;
}

.custom-task-item select[value="delivery"] {
    background-color: #fef3c7;
    border-color: #f59e0b;
}

.custom-task-item select[value="inspection"] {
    background-color: #e0e7ff;
    border-color: #6366f1;
}

.custom-task-item select[value="maintenance"] {
    background-color: #fed7d7;
    border-color: #ef4444;
}

/* Priority indicators */
.priority-select[value="critical"] {
    background-color: #fee2e2;
    border-color: #ef4444;
    color: #dc2626;
}

.priority-select[value="high"] {
    background-color: #fed7aa;
    border-color: #f97316;
    color: #ea580c;
}

.priority-select[value="medium"] {
    background-color: #fef3c7;
    border-color: #f59e0b;
    color: #d97706;
}

.priority-select[value="low"] {
    background-color: #dcfce7;
    border-color: #10b981;
    color: #059669;
}

/* Animation for modal appearance */
.delivery-assignment-modal {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

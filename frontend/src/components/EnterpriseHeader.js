import React, { useState, useEffect } from 'react';
import './EnterpriseHeader.css';

const EnterpriseHeader = ({ onViewModeChange, currentViewMode }) => {
    const [currentTime, setCurrentTime] = useState(new Date());
    const [notifications, setNotifications] = useState([
        { id: 1, type: 'alert', message: 'Agent outside geofence', count: 2 },
        { id: 2, type: 'delivery', message: 'Pending deliveries', count: 5 },
        { id: 3, type: 'system', message: 'System updates', count: 1 }
    ]);
    const [showNotifications, setShowNotifications] = useState(false);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const totalNotifications = notifications.reduce((sum, notif) => sum + notif.count, 0);

    const getNotificationIcon = (type) => {
        switch (type) {
            case 'alert': return '⚠️';
            case 'delivery': return '📦';
            case 'system': return '🔧';
            default: return '📋';
        }
    };

    return (
        <header className="enterprise-header">
            <div className="header-container">
                {/* Left Section - Logo & Navigation */}
                <div className="header-left">
                    <div className="logo-section">
                        <div className="logo">🚚</div>
                        <div className="brand-info">
                            <h1>DeliveryPro</h1>
                            <span>Enterprise Command Center</span>
                        </div>
                    </div>

                    <nav className="main-navigation">
                        <button 
                            className={`nav-btn ${currentViewMode === 'enterprise' ? 'active' : ''}`}
                            onClick={() => onViewModeChange('enterprise')}
                        >
                            📊 Dashboard
                        </button>
                        <button 
                            className={`nav-btn ${currentViewMode === 'map' ? 'active' : ''}`}
                            onClick={() => onViewModeChange('map')}
                        >
                            🗺️ Map View
                        </button>
                        <button className="nav-btn">
                            📈 Analytics
                        </button>
                        <button className="nav-btn">
                            📋 Reports
                        </button>
                    </nav>
                </div>

                {/* Center Section - Search */}
                <div className="header-center">
                    <div className="global-search">
                        <input 
                            type="text" 
                            placeholder="🔍 Search agents, deliveries, locations..."
                            className="search-input"
                        />
                        <button className="search-btn">Search</button>
                    </div>
                </div>

                {/* Right Section - Status & Actions */}
                <div className="header-right">
                    {/* System Status */}
                    <div className="system-status">
                        <div className="status-indicator online">
                            <div className="pulse-dot"></div>
                            <span>System Online</span>
                        </div>
                        <div className="current-time">
                            {currentTime.toLocaleTimeString()}
                        </div>
                    </div>

                    {/* Notifications */}
                    <div className="notifications-container">
                        <button 
                            className="notifications-btn"
                            onClick={() => setShowNotifications(!showNotifications)}
                        >
                            🔔
                            {totalNotifications > 0 && (
                                <span className="notification-badge">{totalNotifications}</span>
                            )}
                        </button>

                        {showNotifications && (
                            <div className="notifications-dropdown">
                                <div className="notifications-header">
                                    <h3>Notifications</h3>
                                    <button className="mark-all-read">Mark all read</button>
                                </div>
                                <div className="notifications-list">
                                    {notifications.map(notif => (
                                        <div key={notif.id} className="notification-item">
                                            <div className="notification-icon">
                                                {getNotificationIcon(notif.type)}
                                            </div>
                                            <div className="notification-content">
                                                <span>{notif.message}</span>
                                                <span className="notification-count">({notif.count})</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Quick Actions */}
                    <div className="quick-actions">
                        <button className="action-btn emergency" title="Emergency Alert">
                            🚨
                        </button>
                        <button className="action-btn broadcast" title="Broadcast Message">
                            📢
                        </button>
                        <button className="action-btn settings" title="Settings">
                            ⚙️
                        </button>
                    </div>

                    {/* User Profile */}
                    <div className="user-profile">
                        <div className="user-avatar">
                            <img src="/admin-avatar.png" alt="Admin" onError={(e) => {
                                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0id2hpdGUiPgo8cGF0aCBkPSJNMTIgMTJjMi4yMSAwIDQtMS43OSA0LTRzLTEuNzktNC00LTQtNCAxLjc5LTQgNCAxLjc5IDQgNCA0em0wIDJjLTIuNjcgMC04IDEuMzQtOCA0djJoMTZ2LTJjMC0yLjY2LTUuMzMtNC04LTR6Ii8+Cjwvc3ZnPgo8L3N2Zz4K';
                            }} />
                        </div>
                        <div className="user-info">
                            <span className="user-name">Admin User</span>
                            <span className="user-role">Fleet Manager</span>
                        </div>
                        <button className="profile-dropdown-btn">▼</button>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default EnterpriseHeader;

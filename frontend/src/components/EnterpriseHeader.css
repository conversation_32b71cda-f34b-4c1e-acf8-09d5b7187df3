/* Enterprise Header Styling */
.enterprise-header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 24px;
    max-width: 1600px;
    margin: 0 auto;
    gap: 24px;
}

/* Left Section */
.header-left {
    display: flex;
    align-items: center;
    gap: 32px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    font-size: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.brand-info h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.brand-info span {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.main-navigation {
    display: flex;
    gap: 8px;
}

.nav-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: #6b7280;
    position: relative;
}

.nav-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-1px);
}

.nav-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Center Section */
.header-center {
    flex: 1;
    max-width: 500px;
}

.global-search {
    display: flex;
    background: rgba(249, 250, 251, 0.8);
    border: 2px solid rgba(229, 231, 235, 0.5);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.global-search:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    font-size: 14px;
    color: #374151;
    outline: none;
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-btn {
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Right Section */
.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.system-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    font-weight: 600;
}

.status-indicator.online {
    color: #10b981;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.current-time {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    font-family: 'Monaco', 'Menlo', monospace;
}

/* Notifications */
.notifications-container {
    position: relative;
}

.notifications-btn {
    position: relative;
    background: rgba(249, 250, 251, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 10px;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.notifications-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    transform: translateY(-1px);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(229, 231, 235, 0.3);
    width: 320px;
    z-index: 1000;
    overflow: hidden;
}

.notifications-header {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.notifications-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 700;
    color: #1f2937;
}

.mark-all-read {
    background: none;
    border: none;
    color: #667eea;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background: rgba(249, 250, 251, 0.5);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    font-size: 16px;
}

.notification-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #374151;
}

.notification-count {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn.emergency {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.action-btn.broadcast {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.action-btn.settings {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: rgba(249, 250, 251, 0.5);
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-profile:hover {
    background: rgba(102, 126, 234, 0.05);
    border-color: #667eea;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.2;
}

.user-role {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.profile-dropdown-btn {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 12px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.user-profile:hover .profile-dropdown-btn {
    transform: rotate(180deg);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-center {
        max-width: 300px;
    }
    
    .main-navigation {
        display: none;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 12px 16px;
        gap: 16px;
    }
    
    .header-center {
        display: none;
    }
    
    .system-status {
        display: none;
    }
    
    .quick-actions {
        gap: 4px;
    }
    
    .user-info {
        display: none;
    }
}

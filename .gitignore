# === Node.js ===
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.pnpm-debug.log

# Optional if you're using a dist directory for builds
dist/
build/

# === Go (Golang) ===
# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Go workspace stuff
vendor/
go.sum
go.work
go.work.sum

# IDE / Editor files
.vscode/
.idea/
*.swp

# === OS-specific ===
.DS_Store
Thumbs.db

# === Logs ===
*.log


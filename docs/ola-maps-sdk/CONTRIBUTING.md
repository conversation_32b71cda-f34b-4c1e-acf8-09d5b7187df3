# Contributing to OlaMaps Web SDK

Thank you for your interest in contributing to the OlaMaps Web SDK! We truly appreciate your help in making this project better. While this repository doesn't contain the source code, there are still several valuable ways you can contribute to the project.

## Ways to Contribute

### 1. Reporting Bugs

- Use the [bug report template](https://github.com/ola-maps/olamaps-web-sdk/blob/main/.github/ISSUE_TEMPLATE/bug_report.md) to submit any issues you find.
- Be as detailed as possible when describing the bug.
- Provide steps to reproduce the issue, along with any relevant logs or screenshots.

### 2. Feature Requests

- Use the [feature request template](https://github.com/ola-maps/olamaps-web-sdk/blob/main/.github/ISSUE_TEMPLATE/feature_request.md) to submit new ideas or features.
- Clearly explain the use case for the feature and how it will improve the SDK.
- Describe the expected behavior and how it should interact with existing functionality.

### 3. Documentation Improvements

- Point out any unclear or incomplete documentation.
- Suggest or submit improvements to help make the documentation more helpful and user-friendly.
- Report any documentation errors or discrepancies you come across.

### 4. Share Example Implementations

- Share real-world implementation examples that showcase the SDK's capabilities.
- Suggest new example scenarios to help users understand how to integrate the SDK into different environments.

## Issue Guidelines

### 1. Search for Existing Issues

- Before creating a new issue, please check if a similar one has already been reported.
- Look through both open and closed issues to avoid duplicates.

### 2. Be Clear and Concise

- Provide a clear and descriptive title for your issue.
- Follow the relevant templates to ensure you include all necessary details.
- Be specific about what is happening and any error messages or behaviors observed.

### 3. Be Respectful

- Follow our Code of Conduct to maintain a positive and respectful environment.
- Be patient and constructive in your feedback or suggestions.

## Pull Request Guidelines

As this repository is distribution-only and does not accept direct code contributions, we welcome the following types of contributions:

- **Documentation updates** to improve clarity and comprehensiveness.
- **Examples** to illustrate various ways to use the SDK.
- **Readme updates** to enhance the onboarding experience for new users.

## Questions and Discussions

For questions, discussions, or to engage with the community, please use the [Discussions](https://github.com/ola-maps/olamaps-web-sdk/discussions) tab. You can ask about:

- General inquiries or clarifications.
- Implementation advice and best practices.
- Ideas for new features or improvements to the SDK.

## Code of Conduct

By participating in this project, you agree to be respectful and abide by all laws and guidelines.

_**We look forward to your contributions!**_

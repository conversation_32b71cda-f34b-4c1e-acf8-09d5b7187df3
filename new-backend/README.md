# Delivery Tracking Backend

A comprehensive Go backend for delivery tracking system with GPS tracking, trip management, payments, and real-time location updates.

## Features

- **GPS Tracking**: Real-time location updates with PostGIS spatial queries
- **Trip Management**: Complete trip lifecycle management (create, accept, status updates)
- **User Management**: Customer and agent registration and profile management
- **Payment Processing**: Payment creation and status tracking
- **Rating System**: User rating and review system
- **Background Jobs**: Automated data archiving and cleanup
- **RESTful APIs**: Complete REST API with proper error handling
- **Real-time Updates**: Live location tracking and updates

## Tech Stack

- **Language**: Go 1.21+
- **Framework**: Gin (HTTP router)
- **Database**: PostgreSQL with PostGIS extension
- **Time Series**: TimescaleDB for GPS tracking data
- **Logging**: Logrus
- **Configuration**: Environment variables with .env support
- **Background Jobs**: Cron-based job scheduler

## Prerequisites

- Go 1.21 or higher
- PostgreSQL 17.5 with PostGIS extension
- TimescaleDB extension (optional but recommended)

## Database Setup

1. Create the database:
```sql
CREATE DATABASE delivery_tracking;
```

2. Run the database schema from the project root:
```bash
psql -U hgcgh -d delivery_tracking -f database_guide/create_database.sql
```

## Installation

1. Clone and navigate to the new backend:
```bash
cd new-backend
```

2. Install dependencies:
```bash
go mod tidy
```

3. Copy environment configuration:
```bash
cp .env.example .env
```

4. Update `.env` with your database credentials:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=hgcgh
DB_PASSWORD=
DB_NAME=delivery_tracking
DB_SSLMODE=disable
```

5. Run the application:
```bash
go run main.go
```

The server will start on `http://localhost:8080`

## API Documentation

### Health Check
```
GET /health
```

### User Management

#### Register User
```
POST /api/users/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "user_type": "customer"
}
```

#### Get User
```
GET /api/users/{user_id}
```

#### Update User
```
PUT /api/users/{user_id}
Content-Type: application/json

{
  "name": "John Smith",
  "email": "<EMAIL>"
}
```

### Agent Management

#### Get Available Agents
```
GET /api/agents/available?vehicle_type=car
```

#### Update Agent Availability
```
PUT /api/agents/{agent_id}/availability
Content-Type: application/json

{
  "is_available": true
}
```

#### Update Agent Profile
```
PUT /api/agents/{agent_id}/profile
Content-Type: application/json

{
  "vehicle_type": "car",
  "vehicle_no": "ABC123",
  "license_no": "DL123456"
}
```

### Trip Management

#### Create Trip
```
POST /api/trip/create
Content-Type: application/json

{
  "trip_type": "delivery",
  "customer_id": "uuid",
  "agent_id": "uuid",
  "pickup_location": {
    "longitude": -122.4194,
    "latitude": 37.7749
  },
  "drop_location": {
    "longitude": -122.4094,
    "latitude": 37.7849
  },
  "fare_estimate": 25.50
}
```

#### Update Trip Status
```
POST /api/trip/status-update
Content-Type: application/json

{
  "trip_id": "uuid",
  "status": "picked_up",
  "location": {
    "longitude": -122.4194,
    "latitude": 37.7749
  },
  "updated_by": "uuid",
  "note": "Package picked up"
}
```

#### Accept Trip
```
POST /api/trip/{trip_id}/accept
Content-Type: application/json

{
  "agent_id": "uuid"
}
```

### GPS Tracking

#### Location Ping
```
POST /api/location/ping
Content-Type: application/json

{
  "agent_id": "uuid",
  "role": "delivery",
  "trip_id": "uuid",
  "location": {
    "longitude": -122.4194,
    "latitude": 37.7749
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "speed_m_s": 15.5,
  "heading_deg": 45.0,
  "battery_pct": 85.5
}
```

#### Batch Location Ping
```
POST /api/location/batch-ping
Content-Type: application/json

[
  {
    "agent_id": "uuid",
    "role": "delivery",
    "location": {
      "longitude": -122.4194,
      "latitude": 37.7749
    },
    "timestamp": "2024-01-01T12:00:00Z"
  }
]
```

#### Get Live Location
```
GET /api/tracking/live-location/{agent_id}
```

#### Get Tracking History
```
GET /api/tracking/history/{agent_id}?date=2024-01-01
GET /api/tracking/history/{agent_id}?start_date=2024-01-01&end_date=2024-01-02
```

#### Get Active Agents
```
GET /api/tracking/active-agents?within_minutes=15
```

### Payment Management

#### Create Payment
```
POST /api/payments/create
Content-Type: application/json

{
  "trip_id": "uuid",
  "amount": 25.50,
  "payment_mode": "credit_card",
  "transaction_ref": "txn_123456"
}
```

#### Update Payment Status
```
PUT /api/payments/{payment_id}/status
Content-Type: application/json

{
  "status": "paid",
  "transaction_ref": "txn_123456_confirmed"
}
```

#### Get Payment Stats
```
GET /api/payments/stats
```

### Rating System

#### Create Rating
```
POST /api/ratings/create
Content-Type: application/json

{
  "trip_id": "uuid",
  "from_user": "uuid",
  "to_user": "uuid",
  "rating": 5,
  "review": "Excellent service!"
}
```

#### Get User Ratings
```
GET /api/ratings/user/{user_id}?type=received
```

#### Get Top Rated Agents
```
GET /api/ratings/top-agents?limit=10
```

## Configuration

Key environment variables:

- `DB_HOST`: Database host (default: localhost)
- `DB_PORT`: Database port (default: 5432)
- `DB_USER`: Database user (default: hgcgh)
- `DB_NAME`: Database name (default: delivery_tracking)
- `SERVER_PORT`: Server port (default: 8080)
- `LOG_LEVEL`: Logging level (default: info)
- `RATE_LIMIT_REQUESTS_PER_MINUTE`: Rate limiting (default: 100)

## Background Jobs

The system includes automated background jobs:

- **Data Archival**: Archives GPS tracking data older than 3 days (runs daily at 2 AM)
- **Data Cleanup**: Removes old tracking records (runs every 6 hours)
- **Rating Updates**: Recalculates agent ratings (runs hourly)
- **Availability Updates**: Updates agent availability based on last GPS ping (runs every 5 minutes)

## Error Handling

All API responses follow a consistent format:

**Success Response:**
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {...},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Operation failed",
  "error": "Detailed error message",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Development

### Running Tests
```bash
go test ./...
```

### Building for Production
```bash
go build -o delivery-tracking-backend main.go
```

### Docker Support
```bash
# Build image
docker build -t delivery-tracking-backend .

# Run container
docker run -p 8080:8080 --env-file .env delivery-tracking-backend
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

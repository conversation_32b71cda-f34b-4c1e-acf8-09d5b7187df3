package main

import (
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

func main() {
	fmt.Println("🚀 Starting GPS data import from gpx_data_3.csv...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Use the specific agent ID from the frontend
	agentID := "3b35ff6d-d482-4cb5-bbc1-ed78774c59b2"
	agentUUID, err := uuid.Parse(agentID)
	if err != nil {
		log.Fatalf("Failed to parse agent UUID: %v", err)
	}
	fmt.Printf("✅ Using agent ID: %s\n", agentUUID)

	// Open CSV file
	csvPath := "/Users/<USER>/Downloads/delivery-tracking/gpx_data_3.csv"
	file, err := os.Open(csvPath)
	if err != nil {
		log.Fatalf("Failed to open CSV file: %v", err)
	}
	defer file.Close()

	// Parse CSV
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Failed to read CSV: %v", err)
	}

	fmt.Printf("📊 Found %d records in CSV\n", len(records)-1) // -1 for header

	// Clear existing data for this agent for today
	today := time.Now().Format("2006-01-02")
	deleteQuery := `DELETE FROM trackingupdates WHERE agent_id = $1 AND DATE(timestamp) = $2`
	result, err := db.Exec(deleteQuery, agentUUID, today)
	if err != nil {
		log.Printf("⚠️ Warning: Failed to clear existing data: %v", err)
	} else {
		rowsAffected, _ := result.RowsAffected()
		fmt.Printf("🗑️ Cleared %d existing records for today\n", rowsAffected)
	}

	// Prepare insert statement
	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10
		)`

	// Start transaction
	tx, err := db.Begin()
	if err != nil {
		log.Fatalf("Failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Prepare statement
	stmt, err := tx.Prepare(insertQuery)
	if err != nil {
		log.Fatalf("Failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	// Import data - spread over today with 30-second intervals
	baseTime := time.Now().Truncate(24 * time.Hour).Add(9 * time.Hour) // Start at 9 AM today
	successCount := 0
	errorCount := 0

	fmt.Println("📥 Importing GPS data...")

	for i, record := range records {
		if i == 0 {
			continue // Skip header
		}

		// Parse latitude and longitude
		lat, err := strconv.ParseFloat(record[0], 64)
		if err != nil {
			fmt.Printf("❌ Error parsing latitude at row %d: %v\n", i+1, err)
			errorCount++
			continue
		}

		lon, err := strconv.ParseFloat(record[1], 64)
		if err != nil {
			fmt.Printf("❌ Error parsing longitude at row %d: %v\n", i+1, err)
			errorCount++
			continue
		}

		// Parse elevation (optional)
		var elevation *float64
		if len(record) > 2 && record[2] != "" {
			elev, err := strconv.ParseFloat(record[2], 64)
			if err == nil {
				elevation = &elev
			}
		}

		// Create timestamp - spread the points over today with 30-second intervals
		timestamp := baseTime.Add(time.Duration(i*30) * time.Second)

		// Create POINT geometry string
		pointWKT := fmt.Sprintf("POINT(%f %f)", lon, lat)

		// Calculate some realistic values
		var speed *float64
		var heading *float64
		var accuracy *float64

		// Add some variation to make it realistic
		if i > 1 {
			// Calculate speed based on distance (simplified)
			speedValue := float64(5 + (i%15)) // 5-20 m/s variation
			speed = &speedValue

			// Calculate heading (simplified)
			headingValue := float64((i * 3) % 360) // Rotating heading
			heading = &headingValue

			// GPS accuracy
			accuracyValue := float64(3 + (i%5)) // 3-8 meters accuracy
			accuracy = &accuracyValue
		}

		// Insert into database
		_, err = stmt.Exec(
			agentUUID,        // agent_id
			"driver",         // role
			pointWKT,         // geom (POINT)
			timestamp,        // timestamp
			speed,            // speed_m_s
			heading,          // heading_deg
			accuracy,         // accuracy_m
			elevation,        // altitude_m
			true,             // is_active
			"gps_device",     // source
		)

		if err != nil {
			fmt.Printf("❌ Error inserting row %d: %v\n", i+1, err)
			errorCount++
			continue
		}

		successCount++

		// Progress indicator
		if i%500 == 0 {
			fmt.Printf("📍 Processed %d/%d records...\n", i, len(records)-1)
		}
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	fmt.Printf("\n✅ Import completed!\n")
	fmt.Printf("📊 Successfully imported: %d records\n", successCount)
	fmt.Printf("❌ Errors: %d records\n", errorCount)
	fmt.Printf("📅 Data imported for date: %s\n", today)
	fmt.Printf("🕘 Time range: %s to %s\n", 
		baseTime.Format("15:04:05"), 
		baseTime.Add(time.Duration(successCount*30)*time.Second).Format("15:04:05"))
	
	fmt.Println("\n🔍 You can now test the GPS line drawing in the frontend!")
	fmt.Printf("📍 Navigate to date: %s\n", today)
	fmt.Printf("👤 Agent ID: %s\n", agentID)
}

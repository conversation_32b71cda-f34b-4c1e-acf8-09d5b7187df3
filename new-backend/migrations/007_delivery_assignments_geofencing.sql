-- Migration: Add delivery assignments and geofencing system
-- Created: 2025-01-21

-- Create task_type enum (only if it doesn't exist)
DO $$ BEGIN
    CREATE TYPE task_type AS ENUM (
        'pick_only',
        'drop_only',
        'store_visit',
        'full_delivery',
        'custom'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create geofence_status enum (only if it doesn't exist)
DO $$ BEGIN
    CREATE TYPE geofence_status AS ENUM (
        'active',
        'inactive',
        'expired'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create delivery_assignments table
CREATE TABLE IF NOT EXISTS delivery_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trip_id UUID NOT NULL REFERENCES trips(trip_id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_type task_type NOT NULL,
    task_description TEXT NOT NULL,
    target_location GEOMETRY(POINT, 4326) NOT NULL,
    geofence_radius INTEGER NOT NULL CHECK (geofence_radius >= 50 AND geofence_radius <= 2000),
    status delivery_status NOT NULL DEFAULT 'requested',
    scheduled_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    item_details JSONB,
    delivery_fee DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create delivery_geofences table (different from existing geofences table)
CREATE TABLE IF NOT EXISTS delivery_geofences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES delivery_assignments(id) ON DELETE CASCADE,
    center_location GEOMETRY(POINT, 4326) NOT NULL,
    radius_meters INTEGER NOT NULL CHECK (radius_meters >= 50 AND radius_meters <= 2000),
    status geofence_status NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create delivery_geofence_events table
CREATE TABLE IF NOT EXISTS delivery_geofence_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    geofence_id UUID NOT NULL REFERENCES delivery_geofences(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    event_type VARCHAR(10) NOT NULL CHECK (event_type IN ('enter', 'exit')),
    location GEOMETRY(POINT, 4326) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    distance_m DECIMAL(10,2) NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_agent_id ON delivery_assignments(agent_id);
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_customer_id ON delivery_assignments(customer_id);
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_trip_id ON delivery_assignments(trip_id);
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_status ON delivery_assignments(status);
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_task_type ON delivery_assignments(task_type);
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_scheduled_date ON delivery_assignments(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_delivery_assignments_target_location ON delivery_assignments USING GIST(target_location);

CREATE INDEX IF NOT EXISTS idx_delivery_geofences_assignment_id ON delivery_geofences(assignment_id);
CREATE INDEX IF NOT EXISTS idx_delivery_geofences_status ON delivery_geofences(status);
CREATE INDEX IF NOT EXISTS idx_delivery_geofences_center_location ON delivery_geofences USING GIST(center_location);

CREATE INDEX IF NOT EXISTS idx_delivery_geofence_events_geofence_id ON delivery_geofence_events(geofence_id);
CREATE INDEX IF NOT EXISTS idx_delivery_geofence_events_agent_id ON delivery_geofence_events(agent_id);
CREATE INDEX IF NOT EXISTS idx_delivery_geofence_events_event_type ON delivery_geofence_events(event_type);
CREATE INDEX IF NOT EXISTS idx_delivery_geofence_events_timestamp ON delivery_geofence_events(timestamp);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_delivery_assignments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_delivery_assignments_updated_at
    BEFORE UPDATE ON delivery_assignments
    FOR EACH ROW
    EXECUTE FUNCTION update_delivery_assignments_updated_at();

-- Create function to automatically create geofence when assignment is created
CREATE OR REPLACE FUNCTION create_geofence_for_assignment()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO delivery_geofences (assignment_id, center_location, radius_meters, status)
    VALUES (NEW.id, NEW.target_location, NEW.geofence_radius, 'active');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_create_geofence_for_assignment ON delivery_assignments;
CREATE TRIGGER trigger_create_geofence_for_assignment
    AFTER INSERT ON delivery_assignments
    FOR EACH ROW
    EXECUTE FUNCTION create_geofence_for_assignment();

-- Create function to check if agent is within geofence
CREATE OR REPLACE FUNCTION is_within_geofence(
    agent_location GEOMETRY(POINT, 4326),
    geofence_center GEOMETRY(POINT, 4326),
    radius_meters INTEGER
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN ST_DWithin(
        ST_Transform(agent_location, 3857),
        ST_Transform(geofence_center, 3857),
        radius_meters
    );
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate distance between two points in meters
CREATE OR REPLACE FUNCTION calculate_distance_meters(
    point1 GEOMETRY(POINT, 4326),
    point2 GEOMETRY(POINT, 4326)
) RETURNS DECIMAL AS $$
BEGIN
    RETURN ST_Distance(
        ST_Transform(point1, 3857),
        ST_Transform(point2, 3857)
    );
END;
$$ LANGUAGE plpgsql;

-- Insert sample delivery assignments for testing
INSERT INTO delivery_assignments (
    trip_id, agent_id, customer_id, task_type, task_description, 
    target_location, geofence_radius, status, scheduled_date, notes, delivery_fee
) VALUES 
-- Pick-only tasks
((SELECT trip_id FROM trips LIMIT 1), 
 (SELECT user_id FROM users WHERE user_type = 'agent' LIMIT 1),
 (SELECT user_id FROM users WHERE user_type = 'customer' LIMIT 1),
 'pick_only', 'Pick up electronics package from warehouse',
 ST_GeomFromText('POINT(77.2090 28.6139)', 4326), 400, 'requested',
 NOW() + INTERVAL '2 hours', 'Handle with care - fragile items', 150.00),

-- Drop-only tasks  
((SELECT trip_id FROM trips LIMIT 1 OFFSET 1),
 (SELECT user_id FROM users WHERE user_type = 'agent' LIMIT 1 OFFSET 1),
 (SELECT user_id FROM users WHERE user_type = 'customer' LIMIT 1 OFFSET 1),
 'drop_only', 'Deliver groceries to apartment complex',
 ST_GeomFromText('POINT(77.2167 28.6333)', 4326), 300, 'accepted',
 NOW() + INTERVAL '4 hours', 'Ring doorbell, leave at door if no answer', 75.00),

-- Store visit tasks
((SELECT trip_id FROM trips LIMIT 1 OFFSET 2),
 (SELECT user_id FROM users WHERE user_type = 'agent' LIMIT 1 OFFSET 2),
 (SELECT user_id FROM users WHERE user_type = 'customer' LIMIT 1 OFFSET 2),
 'store_visit', 'Inventory check at retail store',
 ST_GeomFromText('POINT(77.2310 28.6129)', 4326), 200, 'in_transit',
 NOW() + INTERVAL '1 hour', 'Check stock levels and update system', 200.00);

-- Add comments for documentation
COMMENT ON TABLE delivery_assignments IS 'Stores delivery task assignments with specific task types and geofencing';
COMMENT ON TABLE delivery_geofences IS 'Geographic boundaries for delivery assignments with configurable radius';
COMMENT ON TABLE delivery_geofence_events IS 'Tracks agent entry/exit events for delivery geofences';

COMMENT ON COLUMN delivery_assignments.task_type IS 'Type of delivery task: pick_only, drop_only, store_visit, full_delivery, custom';
COMMENT ON COLUMN delivery_assignments.geofence_radius IS 'Radius in meters for automatic geofencing (50-2000m)';
COMMENT ON COLUMN delivery_geofences.radius_meters IS 'Geofence radius in meters (50-2000m)';
COMMENT ON COLUMN delivery_geofence_events.event_type IS 'Type of geofence event: enter or exit';

<!DOCTYPE html>
<html>
<head>
    <title>Test Custom Marker Image</title>
</head>
<body>
    <h1>Testing Custom Marker Image</h1>
    
    <h2>Direct Image Test:</h2>
    <img src="http://localhost:8080/uploads/markers/3b35ff6d-d482-4cb5-bbc1-ed78774c59b2_1ca30043-fdf5-4dac-b02c-e730fcc94253.png" 
         style="width: 32px; height: 32px; border: 3px solid white; border-radius: 50%; background: #00C851;"
         onload="console.log('✅ Image loaded successfully')"
         onerror="console.error('❌ Failed to load image')" />
    
    <h2>Marker Simulation:</h2>
    <div style="width: 32px; height: 32px; border: 3px solid white; border-radius: 50%; background: #00C851; display: flex; align-items: center; justify-content: center; overflow: hidden;">
        <img src="http://localhost:8080/uploads/markers/3b35ff6d-d482-4cb5-bbc1-ed78774c59b2_1ca30043-fdf5-4dac-b02c-e730fcc94253.png" 
             style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;"
             onload="console.log('✅ Marker simulation image loaded')"
             onerror="console.error('❌ Marker simulation image failed')" />
    </div>

    <h2>API Test:</h2>
    <button onclick="testAPI()">Test Marker Settings API</button>
    <div id="api-result"></div>

    <script>
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8080/api/markers/settings/3b35ff6d-d482-4cb5-bbc1-ed78774c59b2');
                const data = await response.json();
                document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                // Check for delivered marker specifically
                const deliveredSetting = data.data.settings.find(s => s.marker_type === 'delivered');
                if (deliveredSetting) {
                    console.log('✅ Found delivered marker setting:', deliveredSetting);
                    if (deliveredSetting.icon_image_url) {
                        console.log('✅ Delivered marker has image URL:', deliveredSetting.icon_image_url);
                    }
                }
            } catch (error) {
                console.error('❌ API test failed:', error);
                document.getElementById('api-result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>

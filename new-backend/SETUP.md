# Delivery Tracking Backend - Setup Guide

## Quick Start

### 1. Database Setup
Make sure your PostgreSQL database is running with the schema already created:
```bash
# Database should already be created with:
# Database Name: delivery_tracking
# Host: localhost
# Port: 5432
# User: hgcgh
```

### 2. Install Dependencies
```bash
cd new-backend
go mod tidy
```

### 3. Configure Environment
```bash
# Copy the example environment file
cp .env.example .env

# The default configuration should work with your database:
# DB_HOST=localhost
# DB_PORT=5432
# DB_USER=hgcgh
# DB_NAME=delivery_tracking
```

### 4. Test Database Connection
```bash
go run scripts/test_connection.go
```

### 5. Start the Server
```bash
# Option 1: Use the run script
./scripts/run.sh

# Option 2: Run directly
go run main.go
```

The server will start on `http://localhost:8080`

### 6. Test the API
```bash
# In a new terminal, test the API endpoints
./scripts/test_api.sh
```

## API Endpoints Overview

### Core Endpoints
- `GET /health` - Health check
- `GET /jobs/status` - Background jobs status

### User Management
- `POST /api/users/register` - Register new user
- `GET /api/users/{id}` - Get user details
- `PUT /api/users/{id}` - Update user

### Agent Management
- `GET /api/agents/available` - Get available agents
- `PUT /api/agents/{id}/availability` - Update agent availability
- `PUT /api/agents/{id}/profile` - Update agent profile

### GPS Tracking
- `POST /api/location/ping` - Send GPS location
- `POST /api/location/batch-ping` - Send multiple GPS locations
- `GET /api/tracking/live-location/{agent_id}` - Get live location
- `GET /api/tracking/history/{agent_id}` - Get tracking history
- `GET /api/tracking/active-agents` - Get active agents

### Trip Management
- `POST /api/trip/create` - Create new trip
- `GET /api/trip/{trip_id}` - Get trip details
- `POST /api/trip/status-update` - Update trip status
- `POST /api/trip/{trip_id}/accept` - Accept trip
- `GET /api/trip/active` - Get active trips

### Payment System
- `POST /api/payments/create` - Create payment
- `PUT /api/payments/{id}/status` - Update payment status
- `GET /api/payments/stats` - Get payment statistics

### Rating System
- `POST /api/ratings/create` - Create rating
- `GET /api/ratings/user/{user_id}` - Get user ratings
- `GET /api/ratings/top-agents` - Get top rated agents

## Background Jobs

The system automatically runs these background jobs:

1. **Data Archival** (Daily at 2 AM)
   - Archives GPS tracking data older than 3 days

2. **Data Cleanup** (Every 6 hours)
   - Removes old tracking records

3. **Rating Updates** (Hourly)
   - Recalculates agent ratings

4. **Availability Updates** (Every 15 minutes)
   - Updates agent availability based on last GPS ping

## Testing the System

### 1. Register Users
```bash
# Register a customer
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Customer",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "user_type": "customer"
  }'

# Register an agent
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Agent",
    "email": "<EMAIL>",
    "phone": "+1234567891",
    "user_type": "agent"
  }'
```

### 2. Send GPS Location
```bash
curl -X POST http://localhost:8080/api/location/ping \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "YOUR_AGENT_ID",
    "role": "delivery",
    "location": {
      "longitude": -122.4194,
      "latitude": 37.7749
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "speed_m_s": 15.5,
    "battery_pct": 85.5
  }'
```

### 3. Create a Trip
```bash
curl -X POST http://localhost:8080/api/trip/create \
  -H "Content-Type: application/json" \
  -d '{
    "trip_type": "delivery",
    "customer_id": "YOUR_CUSTOMER_ID",
    "agent_id": "YOUR_AGENT_ID",
    "pickup_location": {
      "longitude": -122.4194,
      "latitude": 37.7749
    },
    "drop_location": {
      "longitude": -122.4094,
      "latitude": 37.7849
    },
    "fare_estimate": 25.50
  }'
```

## Key Features Implemented

✅ **Real-time GPS Tracking**
- Location pings with PostGIS spatial queries
- Live location updates and history
- Batch location processing

✅ **Complete Trip Lifecycle**
- Trip creation and management
- Status updates with location tracking
- Trip acceptance workflow

✅ **User & Agent Management**
- User registration and profiles
- Agent availability management
- Agent statistics and ratings

✅ **Payment Processing**
- Payment creation and status tracking
- Payment statistics and history

✅ **Rating System**
- User ratings and reviews
- Top-rated agents
- Automatic rating calculations

✅ **Background Jobs**
- Automated data archiving
- Agent availability updates
- Rating recalculations

✅ **Enterprise Features**
- Rate limiting and CORS
- Comprehensive error handling
- Structured logging
- Health checks and monitoring

## Troubleshooting

### Database Connection Issues
1. Ensure PostgreSQL is running
2. Check database credentials in `.env`
3. Verify PostGIS extension is installed
4. Run `go run scripts/test_connection.go`

### Server Won't Start
1. Check if port 8080 is available
2. Verify Go version (1.21+)
3. Run `go mod tidy` to install dependencies

### API Tests Failing
1. Ensure server is running
2. Check database has sample data
3. Verify API endpoints are accessible

## Next Steps

1. **Frontend Integration**: Connect your frontend application to these APIs
2. **Authentication**: Add JWT authentication middleware
3. **WebSocket Support**: Implement real-time notifications
4. **Monitoring**: Add metrics and monitoring tools
5. **Deployment**: Deploy to production environment

The backend is now fully functional and ready for integration with your frontend application!

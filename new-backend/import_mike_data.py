#!/usr/bin/env python3
"""
Import GPS data for <PERSON> from CSV files
- gpx_data_2.csv for YESTERDAY
- gpx_data_4.csv for TODAY
"""

import csv
import psycopg2
from datetime import datetime, timedelta
import sys
import os

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'hgcgh',
    'password': '',
    'database': 'delivery_tracking'
}

# <PERSON>'s UUID
MIKE_DRIVER_ID = '3b35ff6d-d482-4cb5-bbc1-ed78774c59b2'

def connect_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        sys.exit(1)

def import_csv_data(csv_file_path, target_date, conn):
    """Import CSV data for a specific date"""
    print(f"📁 Processing {csv_file_path} for date {target_date}")
    
    if not os.path.exists(csv_file_path):
        print(f"❌ File not found: {csv_file_path}")
        return 0
    
    cursor = conn.cursor()
    
    # Read CSV file
    with open(csv_file_path, 'r') as file:
        csv_reader = csv.DictReader(file)
        
        records_inserted = 0
        batch_size = 1000
        batch_data = []
        
        # Calculate time intervals (assuming data points are evenly spaced throughout the day)
        total_lines = sum(1 for line in open(csv_file_path)) - 1  # Subtract header
        time_interval_seconds = (24 * 60 * 60) / total_lines  # Spread across 24 hours
        
        print(f"📊 Total GPS points: {total_lines}")
        print(f"⏱️ Time interval: {time_interval_seconds:.2f} seconds between points")
        
        # Reset file pointer
        file.seek(0)
        csv_reader = csv.DictReader(file)
        
        for i, row in enumerate(csv_reader):
            try:
                latitude = float(row['latitude'])
                longitude = float(row['longitude'])
                
                # Calculate timestamp for this point
                timestamp = target_date + timedelta(seconds=i * time_interval_seconds)
                
                # Create POINT geometry string
                geom_point = f"POINT({longitude} {latitude})"
                
                # Prepare data for batch insert
                batch_data.append((
                    MIKE_DRIVER_ID,  # agent_id
                    'delivery',      # role
                    None,           # trip_id
                    geom_point,     # geom as WKT
                    timestamp,      # timestamp
                    10 + (i % 20),  # speed_m_s (varying between 10-30)
                    (i * 5) % 360,  # heading_deg (rotating)
                    3 + (i % 5),    # accuracy_m (3-7 meters)
                    100 + (i % 50), # altitude_m (varying)
                    max(20, 100 - (i % 80)),  # battery_pct (decreasing over time)
                    'mobile_sdk'    # source
                ))
                
                # Insert batch when it reaches batch_size
                if len(batch_data) >= batch_size:
                    insert_batch(cursor, batch_data)
                    records_inserted += len(batch_data)
                    batch_data = []
                    
                    if records_inserted % 5000 == 0:
                        print(f"✅ Inserted {records_inserted} records...")
                        
            except (ValueError, KeyError) as e:
                print(f"⚠️ Skipping invalid row {i}: {e}")
                continue
        
        # Insert remaining batch
        if batch_data:
            insert_batch(cursor, batch_data)
            records_inserted += len(batch_data)
        
        conn.commit()
        cursor.close()
        
        print(f"✅ Successfully imported {records_inserted} records for {target_date.date()}")
        return records_inserted

def insert_batch(cursor, batch_data):
    """Insert a batch of records"""
    insert_query = """
        INSERT INTO trackingupdates (
            agent_id, role, trip_id, geom, timestamp, 
            speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source
        ) VALUES %s
    """
    
    # Convert geom to PostGIS geometry
    formatted_data = []
    for record in batch_data:
        formatted_record = list(record)
        formatted_record[3] = f"ST_GeomFromText('{record[3]}', 4326)"  # Convert WKT to PostGIS
        formatted_data.append(tuple(formatted_record))
    
    # Use execute_values for efficient batch insert
    from psycopg2.extras import execute_values
    
    # Modify query to handle ST_GeomFromText
    insert_query = """
        INSERT INTO trackingupdates (
            agent_id, role, trip_id, geom, timestamp, 
            speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source
        ) VALUES %s
    """
    
    # Format the data properly for PostGIS
    values_list = []
    for record in batch_data:
        values_list.append((
            record[0],  # agent_id
            record[1],  # role
            record[2],  # trip_id
            record[3],  # geom (WKT string)
            record[4],  # timestamp
            record[5],  # speed_m_s
            record[6],  # heading_deg
            record[7],  # accuracy_m
            record[8],  # altitude_m
            record[9],  # battery_pct
            record[10]  # source
        ))
    
    # Execute with proper PostGIS conversion
    for record in values_list:
        cursor.execute("""
            INSERT INTO trackingupdates (
                agent_id, role, trip_id, geom, timestamp, 
                speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source
            ) VALUES (%s, %s, %s, ST_GeomFromText(%s, 4326), %s, %s, %s, %s, %s, %s, %s)
        """, record)

def main():
    """Main function"""
    print("🚀 Starting GPS data import for Mike Driver")
    print("=" * 50)
    
    # Connect to database
    conn = connect_db()
    
    try:
        # Calculate dates
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday = today - timedelta(days=1)
        
        print(f"📅 Today: {today.date()}")
        print(f"📅 Yesterday: {yesterday.date()}")
        print()
        
        total_records = 0
        
        # Import yesterday's data (gpx_data_2.csv)
        print("1️⃣ Importing YESTERDAY data (gpx_data_2.csv)")
        records_yesterday = import_csv_data('/Users/<USER>/Downloads/delivery-tracking/gpx_data_2.csv', yesterday, conn)
        total_records += records_yesterday
        print()
        
        # Import today's data (gpx_data_4.csv)
        print("2️⃣ Importing TODAY data (gpx_data_4.csv)")
        records_today = import_csv_data('/Users/<USER>/Downloads/delivery-tracking/gpx_data_4.csv', today, conn)
        total_records += records_today
        print()
        
        print("=" * 50)
        print(f"🎉 Import completed successfully!")
        print(f"📊 Total records imported: {total_records}")
        print(f"📊 Yesterday records: {records_yesterday}")
        print(f"📊 Today records: {records_today}")
        
        # Verify the import
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM trackingupdates WHERE agent_id = %s", (MIKE_DRIVER_ID,))
        db_count = cursor.fetchone()[0]
        cursor.close()
        
        print(f"✅ Database verification: {db_count} records found for Mike Driver")
        
    except Exception as e:
        print(f"❌ Error during import: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()

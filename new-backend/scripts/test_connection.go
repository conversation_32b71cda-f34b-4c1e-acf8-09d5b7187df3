package main

import (
	"fmt"
	"log"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"
)

func main() {
	fmt.Println("Testing database connection...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	fmt.Printf("Connecting to database: %s\n", cfg.GetDatabaseURL())

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Test basic query
	var version string
	err = db.QueryRow("SELECT version()").Scan(&version)
	if err != nil {
		log.Fatalf("Failed to query database: %v", err)
	}

	fmt.Printf("✅ Database connection successful!\n")
	fmt.Printf("PostgreSQL Version: %s\n", version)

	// Test PostGIS
	var postgisVersion string
	err = db.QueryRow("SELECT PostGIS_Version()").Scan(&postgisVersion)
	if err != nil {
		log.Printf("⚠️  PostGIS not available: %v", err)
	} else {
		fmt.Printf("✅ PostGIS Version: %s\n", postgisVersion)
	}

	// Test table existence
	tables := []string{"users", "agentprofiles", "trips", "tripstatusupdates", "livelocations", "trackingupdates", "payments", "ratings"}
	
	fmt.Println("\nChecking tables:")
	for _, table := range tables {
		var exists bool
		query := `SELECT EXISTS (
			SELECT FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = $1
		)`
		
		err = db.QueryRow(query, table).Scan(&exists)
		if err != nil {
			fmt.Printf("❌ Error checking table %s: %v\n", table, err)
		} else if exists {
			fmt.Printf("✅ Table %s exists\n", table)
		} else {
			fmt.Printf("❌ Table %s does not exist\n", table)
		}
	}

	// Test sample data
	fmt.Println("\nChecking sample data:")
	var userCount int
	err = db.QueryRow("SELECT COUNT(*) FROM users").Scan(&userCount)
	if err != nil {
		fmt.Printf("❌ Error counting users: %v\n", err)
	} else {
		fmt.Printf("✅ Users table has %d records\n", userCount)
	}

	fmt.Println("\n🎉 Database test completed successfully!")
}

package main

import (
	"fmt"
	"log"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

func main() {
	fmt.Println("🚀 Creating more sample agents and tracking data...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Sample agents to create
	agents := []struct {
		name     string
		email    string
		phone    string
		vehicle  string
		location struct{ lat, lon float64 }
	}{
		{"<PERSON><PERSON> Sharma", "<EMAIL>", "+91-9876543211", "scooter", struct{ lat, lon float64 }{28.7041, 77.1025}},
		{"<PERSON><PERSON>", "<EMAIL>", "+91-9876543212", "bike", struct{ lat, lon float64 }{28.5535, 77.2588}},
		{"<PERSON>", "<EMAIL>", "+91-9876543213", "scooter", struct{ lat, lon float64 }{28.6692, 77.2300}},
		{"Vikram Yadav", "<EMAIL>", "+91-9876543214", "bike", struct{ lat, lon float64 }{28.5244, 77.1855}},
	}

	// Start transaction
	tx, err := db.Begin()
	if err != nil {
		log.Fatalf("Failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	for _, agent := range agents {
		// Check if user already exists
		var existingID uuid.UUID
		err := tx.QueryRow("SELECT id FROM users WHERE email = $1", agent.email).Scan(&existingID)
		if err == nil {
			fmt.Printf("⚠️  User %s already exists, skipping...\n", agent.name)
			continue
		}

		// Create user
		userID := uuid.New()
		_, err = tx.Exec(`
			INSERT INTO users (id, name, email, phone, user_type, created_at, is_active)
			VALUES ($1, $2, $3, $4, 'agent', NOW(), true)
		`, userID, agent.name, agent.email, agent.phone)
		if err != nil {
			fmt.Printf("❌ Error creating user %s: %v\n", agent.name, err)
			continue
		}

		// Create agent profile
		_, err = tx.Exec(`
			INSERT INTO agentprofiles (user_id, vehicle_type, rating, is_available)
			VALUES ($1, $2, 4.5, true)
		`, userID, agent.vehicle)
		if err != nil {
			fmt.Printf("❌ Error creating agent profile for %s: %v\n", agent.name, err)
			continue
		}

		// Create recent tracking data
		baseTime := time.Now().Add(-30 * time.Minute) // Start 30 minutes ago
		pointWKT := fmt.Sprintf("POINT(%f %f)", agent.location.lon, agent.location.lat)

		_, err = tx.Exec(`
			INSERT INTO trackingupdates (
				agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
				accuracy_m, altitude_m, battery_pct, is_active, source
			) VALUES (
				$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
			)
		`, userID, "delivery", pointWKT, baseTime, 
			float64(12+len(agent.name)%10), // Speed variation
			float64((len(agent.name)*45)%360), // Heading variation
			float64(3+len(agent.name)%3), // Accuracy
			float64(200+len(agent.name)*5), // Altitude
			float64(80+len(agent.name)%20), // Battery
			true, "mobile_sdk")

		if err != nil {
			fmt.Printf("❌ Error creating tracking data for %s: %v\n", agent.name, err)
			continue
		}

		fmt.Printf("✅ Created agent: %s (%s) at (%.4f, %.4f)\n", 
			agent.name, agent.vehicle, agent.location.lat, agent.location.lon)
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	fmt.Printf("\n🎉 Sample agents created successfully!\n")
	fmt.Println("🚀 Your delivery tracking system now has multiple active agents!")
}

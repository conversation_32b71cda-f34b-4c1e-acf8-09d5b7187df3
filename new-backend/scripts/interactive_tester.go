package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

type LocationData struct {
	Name     string
	Lat      float64
	Lon      float64
	Speed    float64
	Heading  float64
	Battery  float64
	City     string
	State    string
}

func main() {
	fmt.Println("🚀 Interactive Live Location Update Tester")
	fmt.Println("==========================================")
	fmt.Println("")
	fmt.Println("This will add tracking data to TrackingUpdates table")
	fmt.Println("and show you how LiveLocations gets updated automatically!")
	fmt.Println("")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Define 5 rounds of location data across different Indian states
	rounds := map[string][]LocationData{
		"1": {
			{"Mike Driver", 28.6139, 77.2090, 15.5, 90, 85, "New Delhi", "Delhi"},
			{"<PERSON>riya <PERSON>", 19.0760, 72.8777, 12.3, 45, 88, "Mumbai", "Maharashtra"},
			{"Raj<PERSON> <PERSON>", 12.9716, 77.5946, 18.7, 180, 82, "Bangalore", "Karnataka"},
			{"Anita Patel", 13.0827, 80.2707, 14.2, 270, 90, "Chennai", "Tamil Nadu"},
			{"Vikram Yadav", 22.5726, 88.3639, 16.8, 135, 75, "Kolkata", "West Bengal"},
		},
		"2": {
			{"Mike Driver", 28.7041, 77.1025, 22.1, 120, 80, "Gurgaon", "Haryana"},
			{"Priya Sharma", 18.5204, 73.8567, 19.5, 90, 83, "Pune", "Maharashtra"},
			{"Rajesh Kumar", 13.0358, 77.5970, 25.3, 200, 77, "Bangalore East", "Karnataka"},
			{"Anita Patel", 12.9141, 80.2316, 17.8, 300, 85, "Chennai South", "Tamil Nadu"},
			{"Vikram Yadav", 22.6708, 88.0977, 20.4, 160, 70, "Howrah", "West Bengal"},
		},
		"3": {
			{"Mike Driver", 26.9124, 75.7873, 28.9, 200, 75, "Jaipur", "Rajasthan"},
			{"Priya Sharma", 21.1458, 79.0882, 24.6, 135, 78, "Nagpur", "Maharashtra"},
			{"Rajesh Kumar", 15.3173, 75.7139, 31.2, 250, 72, "Hubli", "Karnataka"},
			{"Anita Patel", 11.0168, 76.9558, 26.7, 45, 80, "Coimbatore", "Tamil Nadu"},
			{"Vikram Yadav", 23.2599, 87.8526, 23.1, 180, 65, "Asansol", "West Bengal"},
		},
		"4": {
			{"Mike Driver", 23.0225, 72.5714, 19.3, 270, 70, "Ahmedabad", "Gujarat"},
			{"Priya Sharma", 18.9388, 72.8354, 21.8, 180, 73, "Navi Mumbai", "Maharashtra"},
			{"Rajesh Kumar", 12.2958, 76.6394, 27.5, 300, 67, "Mysore", "Karnataka"},
			{"Anita Patel", 9.9312, 76.2673, 23.4, 90, 75, "Kochi", "Kerala"},
			{"Vikram Yadav", 22.9868, 87.7499, 25.7, 225, 60, "Durgapur", "West Bengal"},
		},
		"5": {
			{"Mike Driver", 30.7333, 76.7794, 16.7, 315, 65, "Chandigarh", "Punjab"},
			{"Priya Sharma", 19.8762, 75.3433, 18.9, 225, 68, "Aurangabad", "Maharashtra"},
			{"Rajesh Kumar", 14.5203, 75.7224, 22.3, 45, 62, "Shimoga", "Karnataka"},
			{"Anita Patel", 8.5241, 76.9366, 20.1, 135, 70, "Trivandrum", "Kerala"},
			{"Vikram Yadav", 23.3441, 85.2955, 19.8, 270, 55, "Ranchi", "Jharkhand"},
		},
	}

	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Println("📋 Choose an option:")
		fmt.Println("1 - Add Round 1 Data (Delhi, Mumbai, Bangalore, Chennai, Kolkata)")
		fmt.Println("2 - Add Round 2 Data (Haryana, Maharashtra, Karnataka, Tamil Nadu, West Bengal)")
		fmt.Println("3 - Add Round 3 Data (Rajasthan, Maharashtra, Karnataka, Tamil Nadu, West Bengal)")
		fmt.Println("4 - Add Round 4 Data (Gujarat, Maharashtra, Karnataka, Kerala, West Bengal)")
		fmt.Println("5 - Add Round 5 Data (Punjab, Maharashtra, Karnataka, Kerala, Jharkhand)")
		fmt.Println("s - Show current LiveLocations")
		fmt.Println("c - Check API response")
		fmt.Println("q - Quit")
		fmt.Println("")
		fmt.Print("Enter your choice: ")

		if !scanner.Scan() {
			break
		}
		choice := strings.TrimSpace(scanner.Text())
		fmt.Println("")

		switch choice {
		case "1", "2", "3", "4", "5":
			fmt.Println("🔄 BEFORE - Current state:")
			showLiveLocations(db)

			addTrackingData(db, rounds[choice], choice)

			fmt.Println("🔄 AFTER - Updated state:")
			showLiveLocations(db)

			fmt.Println("⏱️  Database trigger executed automatically!")
			fmt.Println("Notice how LiveLocations got updated (not new rows added)")
			fmt.Println("")

		case "s", "S":
			showLiveLocations(db)

		case "c", "C":
			fmt.Println("🌐 Testing API endpoint...")
			// You can add API test here if needed
			fmt.Println("API test would go here (curl command)")
			fmt.Println("")

		case "q", "Q":
			fmt.Println("👋 Thanks for testing! Your trigger is working perfectly!")
			return

		default:
			fmt.Println("❌ Invalid choice! Please enter 1-5, s, c, or q")
			fmt.Println("")
		}

		fmt.Println("=================================")
		fmt.Println("")
	}
}

func showLiveLocations(db *database.DB) {
	fmt.Println("📍 Current LiveLocations:")
	
	rows, err := db.Query(`
		SELECT 
			u.name,
			ROUND(ST_Y(l.point)::numeric, 4) as latitude,
			ROUND(ST_X(l.point)::numeric, 4) as longitude,
			ROUND(l.speed_m_s::numeric, 1) as speed_ms,
			ROUND(l.battery_pct::numeric, 0) as battery,
			l.updated_at::time as updated_time
		FROM livelocations l
		JOIN users u ON l.agent_id = u.id
		ORDER BY u.name
	`)
	
	if err != nil {
		fmt.Printf("❌ Error querying LiveLocations: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Printf("%-15s | %-8s | %-9s | %-7s | %-7s | %-12s\n", 
		"Name", "Latitude", "Longitude", "Speed", "Battery", "Updated")
	fmt.Println(strings.Repeat("-", 80))

	for rows.Next() {
		var name string
		var lat, lon, speed float64
		var battery int
		var updatedTime string

		err := rows.Scan(&name, &lat, &lon, &speed, &battery, &updatedTime)
		if err != nil {
			fmt.Printf("❌ Error scanning row: %v\n", err)
			continue
		}

		fmt.Printf("%-15s | %8.4f | %9.4f | %5.1fm/s | %5d%% | %s\n", 
			name, lat, lon, speed, battery, updatedTime)
	}
	fmt.Println("")
}

func addTrackingData(db *database.DB, locations []LocationData, roundNum string) {
	fmt.Printf("📥 Adding Round %s Data - Locations across different Indian states:\n", roundNum)

	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, battery_pct, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
		)`

	for _, loc := range locations {
		// Get agent ID
		var agentID uuid.UUID
		err := db.QueryRow("SELECT id FROM users WHERE name = $1 AND user_type = 'agent'", loc.Name).Scan(&agentID)
		if err != nil {
			fmt.Printf("❌ Agent '%s' not found: %v\n", loc.Name, err)
			continue
		}

		// Create POINT geometry string
		pointWKT := fmt.Sprintf("POINT(%f %f)", loc.Lon, loc.Lat)

		// Insert tracking data
		_, err = db.Exec(
			insertQuery,
			agentID,           // agent_id
			"delivery",        // role
			pointWKT,          // geom (POINT)
			time.Now(),        // timestamp
			loc.Speed,         // speed_m_s
			loc.Heading,       // heading_deg
			3.5,               // accuracy_m
			220.0,             // altitude_m
			loc.Battery,       // battery_pct
			true,              // is_active
			"mobile_sdk",      // source
		)

		if err != nil {
			fmt.Printf("❌ Error inserting data for %s: %v\n", loc.Name, err)
			continue
		}

		fmt.Printf("   ✅ %s: %s, %s (%.4f, %.4f) Speed: %.1fm/s Battery: %.0f%%\n", 
			loc.Name, loc.City, loc.State, loc.Lat, loc.Lon, loc.Speed, loc.Battery)
	}

	fmt.Printf("🎉 Round %s data added successfully!\n\n", roundNum)
}

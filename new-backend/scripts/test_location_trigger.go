package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/lib/pq"
	"github.com/google/uuid"
)

func main() {
	// Connect to the database
	db, err := sql.Open("postgres", "postgres://postgres:postgres@localhost:5432/delivery_tracking?sslmode=disable")
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}
	
	fmt.Println("🔌 Connected to database successfully")

	// Get an agent ID for testing
	var agentID string
	err = db.QueryRow(`
		SELECT id FROM users 
		WHERE user_type = 'agent' AND is_active = true
		LIMIT 1
	`).Scan(&agentID)
	
	if err != nil {
		log.Fatalf("Failed to get test agent ID: %v", err)
	}

	fmt.Printf("🧪 Using agent ID: %s\n", agentID)

	// Insert a new tracking update
	now := time.Now()
	pointWKT := fmt.Sprintf("POINT(%f %f)", 77.590082, 12.978624) // Bangalore coordinates
	
	_, err = db.Exec(`
		INSERT INTO TrackingUpdates (
			agent_id, role, geom, timestamp, 
			speed_m_s, heading_deg, accuracy_m, battery_pct, source
		) VALUES (
			$1, 'delivery', ST_GeomFromText($2, 4326), $3, 
			15.5, 45.0, 3.0, 80.5, 'mobile_sdk'
		)
	`, agentID, pointWKT, now)
	
	if err != nil {
		log.Fatalf("Failed to insert test tracking update: %v", err)
	}

	fmt.Println("✅ Inserted tracking update")

	// Wait a moment for the trigger to run
	time.Sleep(1 * time.Second)

	// Check if LiveLocations was updated
	var count int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM LiveLocations 
		WHERE agent_id = $1 AND timestamp = $2
	`, agentID, now).Scan(&count)
	
	if err != nil {
		log.Fatalf("Failed to check LiveLocations: %v", err)
	}

	if count > 0 {
		fmt.Println("✅ Database trigger is working properly! LiveLocations was updated.")
		
		// Print the live location details
		var role, source sql.NullString
		var lat, lng, speed, heading float64
		var battery sql.NullFloat64
		var updatedAt time.Time
		
		err = db.QueryRow(`
			SELECT 
				role, ST_X(point) as longitude, ST_Y(point) as latitude,
				speed_m_s, heading_deg, battery_pct, source, updated_at
			FROM LiveLocations 
			WHERE agent_id = $1
		`, agentID).Scan(
			&role, &lng, &lat, &speed, &heading, 
			&battery, &source, &updatedAt,
		)
		
		if err != nil {
			log.Fatalf("Failed to get live location details: %v", err)
		}
		
		fmt.Printf("\n📍 Live Location Details:\n")
		fmt.Printf("   Role: %s\n", role.String)
		fmt.Printf("   Coordinates: %.6f, %.6f\n", lat, lng)
		fmt.Printf("   Speed: %.1f m/s\n", speed)
		fmt.Printf("   Heading: %.1f°\n", heading)
		if battery.Valid {
			fmt.Printf("   Battery: %.1f%%\n", battery.Float64)
		}
		if source.Valid {
			fmt.Printf("   Source: %s\n", source.String)
		}
		fmt.Printf("   Updated: %s\n", updatedAt.Format(time.RFC3339))
	} else {
		fmt.Println("❌ Database trigger is NOT working! LiveLocations was not updated.")
	}
} 
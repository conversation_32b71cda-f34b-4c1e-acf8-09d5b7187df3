package main

import (
	"database/sql"
	"fmt"
	"log"
	"math"
	"math/rand"
	"os"
	"time"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Database connection
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" {
		dbHost = "localhost"
	}
	if dbPort == "" {
		dbPort = "5432"
	}

	connStr := fmt.Sprintf("host=%s port=%s user=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbName)

	if dbPassword != "" {
		connStr = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
			dbHost, dbPort, dbUser, dbPassword, dbName)
	}

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("✅ Connected to database successfully")

	// Clear existing tracking data
	fmt.Println("🧹 Clearing existing tracking data...")
	if _, err := db.Exec("DELETE FROM trackingupdates"); err != nil {
		log.Fatal("Failed to clear tracking data:", err)
	}

	// Generate test GPS data for multiple agents and dates
	agents := []string{
		"3b35ff6d-d482-4cb5-bbc1-ed78774c59b2", // Main test agent
		"a1b2c3d4-e5f6-7890-abcd-ef1234567890", // Test agent 2
		"f1e2d3c4-b5a6-9870-fedc-ba0987654321", // Test agent 3
	}

	// Generate data for the last 7 days
	for i := 0; i < 7; i++ {
		date := time.Now().AddDate(0, 0, -i)
		fmt.Printf("📅 Generating GPS data for %s...\n", date.Format("2006-01-02"))

		for _, agentID := range agents {
			generateGPSDataForAgent(db, agentID, date)
		}
	}

	fmt.Println("🎉 Test GPS data generation completed!")
}

func generateGPSDataForAgent(db *sql.DB, agentID string, date time.Time) {
	// Define a realistic route (e.g., around a city)
	// Starting point (example: Delhi area)
	startLat := 28.6139
	startLng := 77.2090

	// Generate a realistic delivery route with multiple stops
	route := generateDeliveryRoute(startLat, startLng, 50) // 50 GPS points

	fmt.Printf("  📍 Generating %d GPS points for agent %s\n", len(route), agentID[:8])

	// Insert GPS points with realistic timestamps throughout the day
	startTime := time.Date(date.Year(), date.Month(), date.Day(), 9, 0, 0, 0, date.Location()) // 9 AM
	endTime := time.Date(date.Year(), date.Month(), date.Day(), 18, 0, 0, 0, date.Location())   // 6 PM
	
	totalDuration := endTime.Sub(startTime)
	intervalDuration := totalDuration / time.Duration(len(route))

	for i, point := range route {
		timestamp := startTime.Add(time.Duration(i) * intervalDuration)
		
		// Add some random variation to make it more realistic
		timestamp = timestamp.Add(time.Duration(rand.Intn(120)-60) * time.Second) // ±1 minute variation

		// Calculate realistic speed and heading
		speed := calculateSpeed(i, len(route))
		heading := calculateHeading(i, route)
		accuracy := 3.0 + rand.Float64()*5.0 // 3-8 meters accuracy
		
		// Insert into database
		query := `
			INSERT INTO trackingupdates (
				agent_id, role, geom, timestamp, speed_m_s, heading_deg,
				accuracy_m, altitude_m, battery_pct, is_active, source
			) VALUES ($1, $2, ST_SetSRID(ST_MakePoint($3, $4), 4326), $5, $6, $7, $8, $9, $10, $11, $12)
		`

		_, err := db.Exec(query,
			agentID,
			"driver", // role (valid: driver, delivery, hybrid)
			point.Lng, point.Lat,
			timestamp,
			speed,
			heading,
			accuracy,
			100.0 + rand.Float64()*50.0, // 100-150m altitude
			80 + rand.Intn(20), // 80-100% battery
			true, // is_active
			"gps_device", // source (valid: mobile_sdk, gps_device, manual)
		)
		
		if err != nil {
			log.Printf("❌ Failed to insert GPS point %d for agent %s: %v", i, agentID, err)
		}
	}

	fmt.Printf("  ✅ Inserted %d GPS points for agent %s\n", len(route), agentID[:8])
}

type GPSPoint struct {
	Lat float64
	Lng float64
}

func generateDeliveryRoute(startLat, startLng float64, numPoints int) []GPSPoint {
	route := make([]GPSPoint, numPoints)
	
	// Start point
	route[0] = GPSPoint{Lat: startLat, Lng: startLng}
	
	// Generate a realistic route with some randomness
	currentLat := startLat
	currentLng := startLng
	
	for i := 1; i < numPoints; i++ {
		// Move in a generally consistent direction with some variation
		// Simulate city driving with turns and stops
		
		// Base movement (roughly 100-500 meters per point)
		baseMoveKm := 0.1 + rand.Float64()*0.4 // 0.1-0.5 km
		
		// Random direction with some consistency
		angle := rand.Float64() * 2 * math.Pi
		if i > 1 {
			// Bias towards continuing in a similar direction
			prevAngle := math.Atan2(currentLat-route[i-2].Lat, currentLng-route[i-2].Lng)
			angle = prevAngle + (rand.Float64()-0.5)*math.Pi/2 // ±45 degrees variation
		}
		
		// Convert to lat/lng movement
		latMove := baseMoveKm * math.Cos(angle) / 111.0 // Rough conversion
		lngMove := baseMoveKm * math.Sin(angle) / (111.0 * math.Cos(currentLat*math.Pi/180))
		
		currentLat += latMove
		currentLng += lngMove
		
		route[i] = GPSPoint{Lat: currentLat, Lng: currentLng}
	}
	
	return route
}

func calculateSpeed(index, total int) float64 {
	// Simulate realistic delivery vehicle speeds
	// Slower at start/end (loading/unloading), faster in middle
	
	progress := float64(index) / float64(total)
	
	// Base speed: 5-15 m/s (18-54 km/h)
	baseSpeed := 5.0 + rand.Float64()*10.0
	
	// Slower at stops (beginning and end of segments)
	if progress < 0.1 || progress > 0.9 {
		baseSpeed *= 0.3 // Slow down for loading/unloading
	} else if index%10 == 0 {
		baseSpeed *= 0.5 // Occasional stops for deliveries
	}
	
	return baseSpeed
}

func calculateHeading(index int, route []GPSPoint) float64 {
	if index == 0 || index >= len(route)-1 {
		return 0.0
	}
	
	// Calculate heading based on movement direction
	current := route[index]
	next := route[index+1]
	
	deltaLat := next.Lat - current.Lat
	deltaLng := next.Lng - current.Lng
	
	heading := math.Atan2(deltaLng, deltaLat) * 180 / math.Pi
	if heading < 0 {
		heading += 360
	}
	
	return heading
}

#!/bin/bash

# API Testing Script for Delivery Tracking Backend

BASE_URL="http://localhost:8080"
API_URL="$BASE_URL/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5

    print_info "Testing: $description"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X $method \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -X $method "$API_URL$endpoint")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status 0 "$description - Status: $status_code"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "$body" | jq . 2>/dev/null || echo "$body"
        fi
    else
        print_status 1 "$description - Expected: $expected_status, Got: $status_code"
        echo "Response: $body"
    fi
    
    echo ""
}

echo "🧪 Starting API Tests for Delivery Tracking Backend"
echo "=================================================="

# Test if server is running
print_info "Checking if server is running..."
if curl -s "$BASE_URL/health" > /dev/null; then
    print_status 0 "Server is running"
else
    print_status 1 "Server is not running. Please start the server first."
    exit 1
fi

echo ""

# Test Health Check
test_endpoint "GET" "/health" "" "200" "Health Check"

# Test Jobs Status
test_endpoint "GET" "/jobs/status" "" "200" "Jobs Status"

# Test User Registration
print_info "=== User Management Tests ==="

# Register a customer
customer_data='{
    "name": "John Customer",
    "email": "<EMAIL>",
    "phone": "+**********",
    "user_type": "customer"
}'
test_endpoint "POST" "/users/register" "$customer_data" "201" "Register Customer"

# Register an agent
agent_data='{
    "name": "Jane Agent",
    "email": "<EMAIL>",
    "phone": "+**********",
    "user_type": "agent"
}'
test_endpoint "POST" "/users/register" "$agent_data" "201" "Register Agent"

# Test duplicate email
test_endpoint "POST" "/users/register" "$customer_data" "409" "Register Duplicate Email"

# Test invalid data
invalid_data='{
    "name": "",
    "email": "invalid-email",
    "phone": "+**********",
    "user_type": "customer"
}'
test_endpoint "POST" "/users/register" "$invalid_data" "400" "Register Invalid Data"

print_info "=== Agent Management Tests ==="

# Test get available agents
test_endpoint "GET" "/agents/available" "" "200" "Get Available Agents"

# Test get available agents by vehicle type
test_endpoint "GET" "/agents/available?vehicle_type=car" "" "200" "Get Available Agents by Vehicle Type"

print_info "=== GPS Tracking Tests ==="

# Test location ping
location_data='{
    "agent_id": "550e8400-e29b-41d4-a716-446655440000",
    "role": "delivery",
    "location": {
        "longitude": -122.4194,
        "latitude": 37.7749
    },
    "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
    "speed_m_s": 15.5,
    "heading_deg": 45.0,
    "battery_pct": 85.5
}'
test_endpoint "POST" "/location/ping" "$location_data" "200" "Location Ping"

# Test batch location ping
batch_data='[
    {
        "agent_id": "550e8400-e29b-41d4-a716-446655440000",
        "role": "delivery",
        "location": {
            "longitude": -122.4194,
            "latitude": 37.7749
        },
        "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
    }
]'
test_endpoint "POST" "/location/batch-ping" "$batch_data" "200" "Batch Location Ping"

# Test get active agents
test_endpoint "GET" "/tracking/active-agents" "" "200" "Get Active Agents"

print_info "=== Trip Management Tests ==="

# Test create trip
trip_data='{
    "trip_type": "delivery",
    "customer_id": "550e8400-e29b-41d4-a716-446655440001",
    "agent_id": "550e8400-e29b-41d4-a716-446655440002",
    "pickup_location": {
        "longitude": -122.4194,
        "latitude": 37.7749
    },
    "drop_location": {
        "longitude": -122.4094,
        "latitude": 37.7849
    },
    "fare_estimate": 25.50
}'
test_endpoint "POST" "/trip/create" "$trip_data" "201" "Create Trip"

# Test get active trips
test_endpoint "GET" "/trip/active" "" "200" "Get Active Trips"

print_info "=== Payment Tests ==="

# Test create payment
payment_data='{
    "trip_id": "550e8400-e29b-41d4-a716-446655440003",
    "amount": 25.50,
    "payment_mode": "credit_card"
}'
test_endpoint "POST" "/payments/create" "$payment_data" "201" "Create Payment"

# Test get payment stats
test_endpoint "GET" "/payments/stats" "" "200" "Get Payment Stats"

print_info "=== Rating Tests ==="

# Test create rating
rating_data='{
    "trip_id": "550e8400-e29b-41d4-a716-446655440003",
    "from_user": "550e8400-e29b-41d4-a716-446655440001",
    "to_user": "550e8400-e29b-41d4-a716-446655440002",
    "rating": 5,
    "review": "Excellent service!"
}'
test_endpoint "POST" "/ratings/create" "$rating_data" "201" "Create Rating"

# Test get top rated agents
test_endpoint "GET" "/ratings/top-agents" "" "200" "Get Top Rated Agents"

# Test get recent ratings
test_endpoint "GET" "/ratings/recent" "" "200" "Get Recent Ratings"

print_info "=== Error Handling Tests ==="

# Test 404 endpoint
test_endpoint "GET" "/nonexistent" "" "404" "Non-existent Endpoint"

# Test invalid method
test_endpoint "PATCH" "/users/register" "" "405" "Invalid Method"

echo "🎉 API Testing Complete!"
echo ""
print_info "Note: Some tests may fail if the database doesn't have the required sample data."
print_info "This is normal for a fresh installation. The API structure and error handling are being tested."

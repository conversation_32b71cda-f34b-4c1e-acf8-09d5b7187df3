package main

import (
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

func main() {
	fmt.Println("🚀 Starting GPS data import for <PERSON>...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Get <PERSON> Driver's user ID
	var mikeID uuid.UUID
	err = db.QueryRow("SELECT id FROM users WHERE LOWER(name) = 'mike driver' AND user_type = 'agent'").Scan(&mikeID)
	if err != nil {
		log.Fatalf("Failed to find <PERSON>: %v", err)
	}
	fmt.Printf("✅ Found Mike Driver with ID: %s\n", mikeID)

	// Open CSV file
	csvPath := "/Users/<USER>/Downloads/google-cloud-sdk/delivery-tracking/gpx_data_4.csv"
	file, err := os.Open(csvPath)
	if err != nil {
		log.Fatalf("Failed to open CSV file: %v", err)
	}
	defer file.Close()

	// Parse CSV
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Failed to read CSV: %v", err)
	}

	fmt.Printf("📊 Found %d GPS points in CSV file\n", len(records)-1) // -1 for header

	// Prepare insert statement
	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10
		)`

	// Start transaction
	tx, err := db.Begin()
	if err != nil {
		log.Fatalf("Failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Prepare statement
	stmt, err := tx.Prepare(insertQuery)
	if err != nil {
		log.Fatalf("Failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	// Import data
	baseTime := time.Now().Truncate(24 * time.Hour) // Start of today
	successCount := 0
	errorCount := 0

	fmt.Println("📥 Importing GPS data...")

	for i, record := range records {
		if i == 0 {
			continue // Skip header
		}

		// Parse latitude and longitude
		lat, err := strconv.ParseFloat(record[0], 64)
		if err != nil {
			fmt.Printf("❌ Error parsing latitude at row %d: %v\n", i+1, err)
			errorCount++
			continue
		}

		lon, err := strconv.ParseFloat(record[1], 64)
		if err != nil {
			fmt.Printf("❌ Error parsing longitude at row %d: %v\n", i+1, err)
			errorCount++
			continue
		}

		// Parse elevation (optional)
		var elevation *float64
		if record[2] != "" {
			elev, err := strconv.ParseFloat(record[2], 64)
			if err == nil {
				elevation = &elev
			}
		}

		// Create timestamp - spread the points over today with 30-second intervals
		timestamp := baseTime.Add(time.Duration(i*30) * time.Second)

		// Create POINT geometry string
		pointWKT := fmt.Sprintf("POINT(%f %f)", lon, lat)

		// Calculate some realistic values
		var speed *float64
		var heading *float64
		var accuracy *float64

		// Add some variation to make it realistic
		if i > 1 {
			// Calculate speed based on distance (simplified)
			speedValue := float64(10 + (i%20)) // 10-30 m/s variation
			speed = &speedValue

			// Calculate heading (simplified)
			headingValue := float64((i * 5) % 360) // Rotating heading
			heading = &headingValue

			// GPS accuracy
			accuracyValue := float64(3 + (i%5)) // 3-8 meters accuracy
			accuracy = &accuracyValue
		}

		// Insert into database
		_, err = stmt.Exec(
			mikeID,           // agent_id
			"delivery",       // role
			pointWKT,         // geom (POINT)
			timestamp,        // timestamp
			speed,            // speed_m_s
			heading,          // heading_deg
			accuracy,         // accuracy_m
			elevation,        // altitude_m
			true,             // is_active
			"mobile_sdk",     // source
		)

		if err != nil {
			fmt.Printf("❌ Error inserting row %d: %v\n", i+1, err)
			errorCount++
			continue
		}

		successCount++

		// Progress indicator
		if i%1000 == 0 {
			fmt.Printf("📍 Processed %d points...\n", i)
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	fmt.Printf("\n🎉 Import completed!\n")
	fmt.Printf("✅ Successfully imported: %d GPS points\n", successCount)
	fmt.Printf("❌ Failed to import: %d GPS points\n", errorCount)
	fmt.Printf("📅 Data spans from: %s to %s\n", 
		baseTime.Format("15:04:05"), 
		baseTime.Add(time.Duration(successCount*30)*time.Second).Format("15:04:05"))

	// Note: We don't need to explicitly update live location anymore
	// Our database trigger will automatically update LiveLocations from TrackingUpdates
	// The trigger will only update if the timestamp is newer than what's in LiveLocations

	// Set Mike as available
	_, err = db.Exec("UPDATE agentprofiles SET is_available = true WHERE user_id = $1", mikeID)
	if err != nil {
		fmt.Printf("⚠️  Warning: Failed to set Mike as available: %v\n", err)
	} else {
		fmt.Printf("✅ Set Mike Driver as available\n")
	}

	fmt.Println("\n🚀 Mike Driver's delivery data is now ready for tracking!")
}

#!/bin/bash

# <PERSON>ript to apply database migrations

echo "📦 Applying database migrations..."

# Load environment variables
if [ -f "../.env" ]; then
  source ../.env
fi

# Default PostgreSQL connection parameters
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"delivery_tracking"}
DB_USER=${DB_USER:-"postgres"}
DB_PASSWORD=${DB_PASSWORD:-"postgres"}

# Apply migration
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f ../database/migration.sql

if [ $? -eq 0 ]; then
  echo "✅ Migration applied successfully!"
else
  echo "❌ Migration failed!"
  exit 1
fi

echo "🔄 Restarting backend services..."
cd ..
go build -o delivery-tracking-api
./delivery-tracking-api 
package main

import (
	"fmt"
	"log"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"
)

func main() {
	fmt.Println("🔍 Checking Table Structures")
	fmt.Println("============================")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Check trips table structure
	fmt.Println("📋 Trips table structure:")
	rows, err := db.Query(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_name = 'trips' 
		ORDER BY ordinal_position
	`)
	if err != nil {
		log.Fatalf("Failed to get trips table structure: %v", err)
	}
	defer rows.Close()

	fmt.Printf("%-20s | %-20s | %-10s\n", "Column", "Type", "Nullable")
	fmt.Println("--------------------------------------------------------")
	
	for rows.Next() {
		var columnName, dataType, isNullable string
		err := rows.Scan(&columnName, &dataType, &isNullable)
		if err != nil {
			continue
		}
		fmt.Printf("%-20s | %-20s | %-10s\n", columnName, dataType, isNullable)
	}

	// Check tripstatusupdates table structure
	fmt.Println("\n📋 TripStatusUpdates table structure:")
	rows2, err := db.Query(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_name = 'tripstatusupdates' 
		ORDER BY ordinal_position
	`)
	if err != nil {
		log.Fatalf("Failed to get tripstatusupdates table structure: %v", err)
	}
	defer rows2.Close()

	fmt.Printf("%-20s | %-20s | %-10s\n", "Column", "Type", "Nullable")
	fmt.Println("--------------------------------------------------------")
	
	for rows2.Next() {
		var columnName, dataType, isNullable string
		err := rows2.Scan(&columnName, &dataType, &isNullable)
		if err != nil {
			continue
		}
		fmt.Printf("%-20s | %-20s | %-10s\n", columnName, dataType, isNullable)
	}
}

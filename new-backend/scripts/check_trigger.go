package main

import (
	"fmt"
	"log"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"
)

func main() {
	fmt.Println("🔍 Checking database trigger functionality...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Check if trigger exists
	var triggerExists bool
	err = db.QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM information_schema.triggers 
			WHERE trigger_name = 'trigger_update_live_location'
			AND event_object_table = 'trackingupdates'
		)
	`).Scan(&triggerExists)
	
	if err != nil {
		log.Fatalf("Failed to check trigger existence: %v", err)
	}

	if triggerExists {
		fmt.Println("✅ Trigger 'trigger_update_live_location' exists on TrackingUpdates table")
	} else {
		fmt.Println("❌ Trigger 'trigger_update_live_location' does NOT exist")
		fmt.Println("🔧 Let's create the trigger...")
		
		// Create the trigger function
		_, err = db.Exec(`
			CREATE OR REPLACE FUNCTION update_live_location_from_tracking()
			RETURNS TRIGGER AS $$
			BEGIN
				-- Insert or update the LiveLocations table with the newest tracking data
				INSERT INTO LiveLocations (
					agent_id, role, trip_id, geom, point, timestamp, 
					speed_m_s, heading_deg, accuracy_m, altitude_m, 
					battery_pct, source, updated_at
				)
				VALUES (
					NEW.agent_id, NEW.role, NEW.trip_id, 
					NEW.geom, 
					ST_SetSRID(ST_MakePoint(
						ST_X(NEW.geom::geometry),
						ST_Y(NEW.geom::geometry)
					), 4326),
					NEW.timestamp, 
					COALESCE(NEW.speed_m_s, 0), 
					COALESCE(NEW.heading_deg, 0), 
					NEW.accuracy_m, NEW.altitude_m, 
					NEW.battery_pct, NEW.source, 
					CURRENT_TIMESTAMP
				)
				ON CONFLICT (agent_id)
				DO UPDATE SET
					role = EXCLUDED.role,
					trip_id = EXCLUDED.trip_id,
					geom = EXCLUDED.geom,
					point = EXCLUDED.point,
					timestamp = EXCLUDED.timestamp,
					speed_m_s = EXCLUDED.speed_m_s,
					heading_deg = EXCLUDED.heading_deg,
					accuracy_m = EXCLUDED.accuracy_m,
					altitude_m = EXCLUDED.altitude_m,
					battery_pct = EXCLUDED.battery_pct,
					source = EXCLUDED.source,
					updated_at = CURRENT_TIMESTAMP
				WHERE 
					-- Only update if the new record is more recent
					EXCLUDED.timestamp > LiveLocations.timestamp;

				RETURN NEW;
			END;
			$$ LANGUAGE plpgsql;
		`)
		
		if err != nil {
			log.Fatalf("Failed to create trigger function: %v", err)
		}
		
		// Create the trigger
		_, err = db.Exec(`
			DROP TRIGGER IF EXISTS trigger_update_live_location ON TrackingUpdates;
			CREATE TRIGGER trigger_update_live_location
			AFTER INSERT ON TrackingUpdates
			FOR EACH ROW
			EXECUTE FUNCTION update_live_location_from_tracking();
		`)
		
		if err != nil {
			log.Fatalf("Failed to create trigger: %v", err)
		}
		
		fmt.Println("✅ Trigger created successfully!")
	}

	// Check current data in both tables
	var trackingCount, liveCount int
	
	err = db.QueryRow("SELECT COUNT(*) FROM trackingupdates").Scan(&trackingCount)
	if err != nil {
		log.Printf("Error counting tracking updates: %v", err)
	}
	
	err = db.QueryRow("SELECT COUNT(*) FROM livelocations").Scan(&liveCount)
	if err != nil {
		log.Printf("Error counting live locations: %v", err)
	}
	
	fmt.Printf("📊 Current data:\n")
	fmt.Printf("   - TrackingUpdates: %d records\n", trackingCount)
	fmt.Printf("   - LiveLocations: %d records\n", liveCount)
	
	// Show sample data from LiveLocations
	fmt.Println("\n📍 Current LiveLocations data:")
	rows, err := db.Query(`
		SELECT l.agent_id, u.name, l.timestamp, l.updated_at,
		       ST_X(l.point) as longitude, ST_Y(l.point) as latitude
		FROM livelocations l
		JOIN users u ON l.agent_id = u.id
		ORDER BY l.updated_at DESC
		LIMIT 5
	`)
	
	if err != nil {
		log.Printf("Error querying live locations: %v", err)
		return
	}
	defer rows.Close()
	
	for rows.Next() {
		var agentID, name string
		var timestamp, updatedAt string
		var longitude, latitude float64
		
		err := rows.Scan(&agentID, &name, &timestamp, &updatedAt, &longitude, &latitude)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}
		
		fmt.Printf("   - %s: (%.4f, %.4f) at %s\n", name, latitude, longitude, updatedAt)
	}
	
	fmt.Println("\n🚀 Trigger check completed!")
}

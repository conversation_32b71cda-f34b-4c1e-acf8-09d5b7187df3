package main

import (
	"fmt"
	"log"
	"strings"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"
)

func main() {
	fmt.Println("🗄️  Testing Archive Functionality")
	fmt.Println("=================================")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Check if archive tables exist
	fmt.Println("📋 Checking archive tables...")
	
	tables := []string{"trips_archive", "trip_status_updates_archive"}
	for _, table := range tables {
		var exists bool
		err := db.QueryRow(`
			SELECT EXISTS (
				SELECT FROM information_schema.tables 
				WHERE table_name = $1
			)
		`, table).Scan(&exists)
		
		if err != nil {
			fmt.Printf("❌ Error checking table %s: %v\n", table, err)
			continue
		}
		
		if exists {
			fmt.Printf("✅ Table %s exists\n", table)
		} else {
			fmt.Printf("❌ Table %s not found\n", table)
		}
	}

	// Check current data counts
	fmt.Println("\n📊 Current data counts:")
	
	var tripsCount, statusUpdatesCount int
	
	err = db.QueryRow("SELECT COUNT(*) FROM trips").Scan(&tripsCount)
	if err != nil {
		fmt.Printf("❌ Error counting trips: %v\n", err)
	} else {
		fmt.Printf("📦 Trips: %d\n", tripsCount)
	}
	
	err = db.QueryRow("SELECT COUNT(*) FROM tripstatusupdates").Scan(&statusUpdatesCount)
	if err != nil {
		fmt.Printf("❌ Error counting trip status updates: %v\n", err)
	} else {
		fmt.Printf("📝 Trip Status Updates: %d\n", statusUpdatesCount)
	}

	// Check archive counts
	var archiveTripsCount, archiveStatusCount int
	
	err = db.QueryRow("SELECT COUNT(*) FROM trips_archive").Scan(&archiveTripsCount)
	if err != nil {
		fmt.Printf("❌ Error counting archived trips: %v\n", err)
	} else {
		fmt.Printf("🗄️  Archived Trips: %d\n", archiveTripsCount)
	}
	
	err = db.QueryRow("SELECT COUNT(*) FROM trip_status_updates_archive").Scan(&archiveStatusCount)
	if err != nil {
		fmt.Printf("❌ Error counting archived status updates: %v\n", err)
	} else {
		fmt.Printf("🗄️  Archived Status Updates: %d\n", archiveStatusCount)
	}

	// Test the archive function
	fmt.Println("\n🧪 Testing archive function...")
	
	rows, err := db.Query("SELECT * FROM archive_old_data(30)")
	if err != nil {
		fmt.Printf("❌ Error calling archive function: %v\n", err)
		return
	}
	defer rows.Close()

	if rows.Next() {
		var tripsArchived, statusArchived int
		err := rows.Scan(&tripsArchived, &statusArchived)
		if err != nil {
			fmt.Printf("❌ Error reading archive results: %v\n", err)
			return
		}
		
		fmt.Printf("📦 Trips archived: %d\n", tripsArchived)
		fmt.Printf("📝 Status updates archived: %d\n", statusArchived)
		
		if tripsArchived > 0 || statusArchived > 0 {
			fmt.Println("✅ Archive function executed successfully!")
		} else {
			fmt.Println("ℹ️  No data to archive (no trips/updates older than 30 days)")
		}
	}

	// Show sample archived data if any exists
	fmt.Println("\n📋 Sample archived data:")
	
	archiveRows, err := db.Query(`
		SELECT trip_id, created_at, archived_at
		FROM trips_archive
		ORDER BY archived_at DESC
		LIMIT 5
	`)
	if err != nil {
		fmt.Printf("❌ Error querying archived trips: %v\n", err)
	} else {
		defer archiveRows.Close()
		
		fmt.Println("🗄️  Recent archived trips:")
		fmt.Printf("%-36s | %-19s | %-19s\n", "Trip ID", "Created At", "Archived At")
		fmt.Println(strings.Repeat("-", 80))

		count := 0
		for archiveRows.Next() {
			var tripID string
			var createdAt, archivedAt string

			err := archiveRows.Scan(&tripID, &createdAt, &archivedAt)
			if err != nil {
				continue
			}

			fmt.Printf("%-36s | %-19s | %-19s\n",
				tripID[:8]+"...", createdAt[:19], archivedAt[:19])
			count++
		}
		
		if count == 0 {
			fmt.Println("(No archived trips found)")
		}
	}

	fmt.Println("\n🎉 Archive functionality test completed!")
	fmt.Println("\n📝 Usage:")
	fmt.Println("To archive data older than 90 days: SELECT * FROM archive_old_data(90);")
	fmt.Println("To archive data older than 30 days: SELECT * FROM archive_old_data(30);")
}

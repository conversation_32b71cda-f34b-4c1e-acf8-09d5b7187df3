package main

import (
	"fmt"
	"log"
	"math"
	"math/rand"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

type Agent struct {
	ID       uuid.UUID
	Name     string
	Lat      float64
	Lon      float64
	Speed    float64
	Heading  float64
	Battery  float64
}

func main() {
	fmt.Println("🚀 Starting live data simulation...")
	fmt.Println("This will continuously add tracking data to TrackingUpdates")
	fmt.Println("and verify that LiveLocations gets updated automatically")
	fmt.Println("Press Ctrl+C to stop\n")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Get all active agents
	agents := []Agent{}
	rows, err := db.Query(`
		SELECT u.id, u.name, 
		       ST_X(l.point) as longitude, ST_Y(l.point) as latitude,
		       l.speed_m_s, l.heading_deg, l.battery_pct
		FROM users u
		JOIN livelocations l ON u.id = l.agent_id
		WHERE u.user_type = 'agent'
	`)
	
	if err != nil {
		log.Fatalf("Failed to get agents: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var agent Agent
		var battery *float64
		err := rows.Scan(&agent.ID, &agent.Name, &agent.Lon, &agent.Lat, 
			&agent.Speed, &agent.Heading, &battery)
		if err != nil {
			log.Printf("Error scanning agent: %v", err)
			continue
		}
		
		if battery != nil {
			agent.Battery = *battery
		} else {
			agent.Battery = 80.0
		}
		
		agents = append(agents, agent)
	}

	if len(agents) == 0 {
		log.Fatal("No agents found in database")
	}

	fmt.Printf("📱 Found %d agents to simulate:\n", len(agents))
	for _, agent := range agents {
		fmt.Printf("   - %s at (%.4f, %.4f)\n", agent.Name, agent.Lat, agent.Lon)
	}
	fmt.Println()

	// Prepare insert statement
	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, battery_pct, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
		)`

	// Simulation loop
	iteration := 0
	for {
		iteration++
		fmt.Printf("🔄 Iteration %d - %s\n", iteration, time.Now().Format("15:04:05"))

		for i := range agents {
			// Simulate movement (small random changes)
			agents[i].Lat += (rand.Float64() - 0.5) * 0.001  // ~100m movement
			agents[i].Lon += (rand.Float64() - 0.5) * 0.001
			
			// Simulate speed changes
			agents[i].Speed = 10 + rand.Float64()*20 // 10-30 m/s
			
			// Simulate heading changes
			agents[i].Heading = math.Mod(agents[i].Heading + (rand.Float64()-0.5)*30, 360)
			
			// Simulate battery drain
			agents[i].Battery = math.Max(10, agents[i].Battery - rand.Float64()*2)

			// Create POINT geometry string
			pointWKT := fmt.Sprintf("POINT(%f %f)", agents[i].Lon, agents[i].Lat)

			// Insert new tracking data
			_, err = db.Exec(
				insertQuery,
				agents[i].ID,           // agent_id
				"delivery",             // role
				pointWKT,               // geom (POINT)
				time.Now(),             // timestamp
				agents[i].Speed,        // speed_m_s
				agents[i].Heading,      // heading_deg
				3.0 + rand.Float64()*2, // accuracy_m (3-5m)
				200.0 + rand.Float64()*50, // altitude_m
				agents[i].Battery,      // battery_pct
				true,                   // is_active
				"mobile_sdk",           // source
			)

			if err != nil {
				fmt.Printf("❌ Error inserting data for %s: %v\n", agents[i].Name, err)
				continue
			}

			fmt.Printf("📍 %s: (%.4f, %.4f) Speed: %.1fm/s Battery: %.0f%%\n", 
				agents[i].Name, agents[i].Lat, agents[i].Lon, agents[i].Speed, agents[i].Battery)
		}

		// Check LiveLocations table to verify updates
		fmt.Println("\n🔍 Checking LiveLocations table:")
		checkRows, err := db.Query(`
			SELECT u.name, l.updated_at,
			       ST_X(l.point) as longitude, ST_Y(l.point) as latitude,
			       l.speed_m_s, l.battery_pct
			FROM livelocations l
			JOIN users u ON l.agent_id = u.id
			ORDER BY l.updated_at DESC
		`)
		
		if err != nil {
			fmt.Printf("❌ Error checking LiveLocations: %v\n", err)
		} else {
			for checkRows.Next() {
				var name string
				var updatedAt time.Time
				var lon, lat, speed float64
				var battery *float64
				
				err := checkRows.Scan(&name, &updatedAt, &lon, &lat, &speed, &battery)
				if err != nil {
					continue
				}
				
				batteryStr := "N/A"
				if battery != nil {
					batteryStr = fmt.Sprintf("%.0f%%", *battery)
				}
				
				fmt.Printf("   ✅ %s: (%.4f, %.4f) Speed: %.1fm/s Battery: %s Updated: %s\n", 
					name, lat, lon, speed, batteryStr, updatedAt.Format("15:04:05"))
			}
			checkRows.Close()
		}

		fmt.Printf("\n⏱️  Waiting 10 seconds before next update...\n\n")
		time.Sleep(10 * time.Second)
	}
}

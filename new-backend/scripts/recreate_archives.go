package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"
)

func main() {
	fmt.Println("🔄 Recreating Archive Tables")
	fmt.Println("============================")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Read the SQL file
	sqlFile := filepath.Join("scripts", "cleanup_and_create_simple_archives.sql")
	sqlContent, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		log.Fatalf("Failed to read SQL file: %v", err)
	}

	fmt.Println("📄 Executing archive table recreation SQL...")

	// Execute the SQL
	_, err = db.Exec(string(sqlContent))
	if err != nil {
		log.Fatalf("Failed to execute SQL: %v", err)
	}

	fmt.Println("✅ Archive tables recreated successfully!")

	// Verify the tables exist
	fmt.Println("\n🔍 Verifying archive tables...")

	tables := []string{"trips_archive", "trip_status_updates_archive"}
	for _, table := range tables {
		var exists bool
		err := db.QueryRow(`
			SELECT EXISTS (
				SELECT FROM information_schema.tables 
				WHERE table_name = $1
			)
		`, table).Scan(&exists)
		
		if err != nil {
			fmt.Printf("❌ Error checking table %s: %v\n", table, err)
			continue
		}
		
		if exists {
			fmt.Printf("✅ Table %s exists\n", table)
		} else {
			fmt.Printf("❌ Table %s not found\n", table)
		}
	}

	fmt.Println("\n🎉 Archive tables are ready!")
}

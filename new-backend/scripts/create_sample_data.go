package main

import (
	"fmt"
	"log"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

func main() {
	fmt.Println("🚀 Creating sample tracking data...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Sample locations around Delhi
	locations := []struct {
		lat, lon float64
		name     string
	}{
		{28.6139, 77.2090, "Connaught Place"},
		{28.6562, 77.2410, "Red Fort"},
		{28.6129, 77.2295, "India Gate"},
		{28.5535, 77.2588, "Lotus Temple"},
		{28.6692, 77.2300, "<PERSON><PERSON><PERSON>"},
		{28.5244, 77.1855, "Qutub Minar"},
		{28.6507, 77.2334, "Jama Masjid"},
		{28.6280, 77.2184, "Rajpath"},
	}

	// Get Mike <PERSON>'s ID
	var mikeID uuid.UUID
	err = db.QueryRow("SELECT id FROM users WHERE LOWER(name) = 'mike driver' AND user_type = 'agent'").Scan(&mikeID)
	if err != nil {
		log.Fatalf("Failed to find Mike Driver: %v", err)
	}
	fmt.Printf("✅ Found Mike Driver with ID: %s\n", mikeID)

	// Prepare insert statement
	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, battery_pct, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
		)`

	// Start transaction
	tx, err := db.Begin()
	if err != nil {
		log.Fatalf("Failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Create tracking data for the last few hours
	baseTime := time.Now().Add(-2 * time.Hour) // Start 2 hours ago
	successCount := 0

	fmt.Println("📥 Creating sample GPS tracking data...")

	for i, loc := range locations {
		// Create timestamp with 15-minute intervals
		timestamp := baseTime.Add(time.Duration(i*15) * time.Minute)

		// Create POINT geometry string
		pointWKT := fmt.Sprintf("POINT(%f %f)", loc.lon, loc.lat)

		// Calculate realistic values
		speed := float64(15 + (i%10))        // 15-25 m/s
		heading := float64((i * 45) % 360)   // Varying heading
		accuracy := float64(3 + (i % 3))     // 3-6 meters accuracy
		altitude := float64(200 + (i * 10))  // Varying altitude
		battery := float64(90 - (i * 5))     // Decreasing battery

		// Insert into database
		_, err = tx.Exec(
			insertQuery,
			mikeID,           // agent_id
			"delivery",       // role
			pointWKT,         // geom (POINT)
			timestamp,        // timestamp
			speed,            // speed_m_s
			heading,          // heading_deg
			accuracy,         // accuracy_m
			altitude,         // altitude_m
			battery,          // battery_pct
			true,             // is_active
			"mobile_sdk",     // source
		)

		if err != nil {
			fmt.Printf("❌ Error inserting location %s: %v\n", loc.name, err)
			continue
		}

		successCount++
		fmt.Printf("📍 Added location: %s (%.4f, %.4f) at %s\n", 
			loc.name, loc.lat, loc.lon, timestamp.Format("15:04:05"))
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	// Set Mike as available
	_, err = db.Exec("UPDATE agentprofiles SET is_available = true WHERE user_id = $1", mikeID)
	if err != nil {
		fmt.Printf("⚠️  Warning: Failed to set Mike as available: %v\n", err)
	} else {
		fmt.Printf("✅ Set Mike Driver as available\n")
	}

	fmt.Printf("\n🎉 Sample data created!\n")
	fmt.Printf("✅ Successfully created: %d GPS tracking points\n", successCount)
	fmt.Printf("📅 Data spans from: %s to %s\n", 
		baseTime.Format("15:04:05"), 
		baseTime.Add(time.Duration((len(locations)-1)*15)*time.Minute).Format("15:04:05"))

	fmt.Println("\n🚀 Mike Driver's tracking data is now ready!")
}

-- =====================================================
-- CLEANUP AND CREATE SIMPLE ARCHIVE TABLES
-- =====================================================
-- This script removes the complex optimization tables and creates only 2 simple archive tables

-- Drop all the complex optimization tables
DROP TABLE IF EXISTS trips_partitioned CASCADE;
DROP TABLE IF EXISTS trips_2025_07 CASCADE;
DROP TABLE IF EXISTS trips_2025_08 CASCADE;
DROP TABLE IF EXISTS trips_2025_09 CASCADE;
DROP TABLE IF EXISTS trips_2025_10 CASCADE;
DROP TABLE IF EXISTS trips_2025_11 CASCADE;
DROP TABLE IF EXISTS trips_2025_12 CASCADE;
DROP TABLE IF EXISTS daily_trip_summary CASCADE;
DROP TABLE IF EXISTS agent_performance_summary CASCADE;
DROP TABLE IF EXISTS hourly_stats CASCADE;
DROP TABLE IF EXISTS trackingupdates_archive CASCADE;
DROP TABLE IF EXISTS trip_status_updates CASCADE;

-- Drop materialized view if exists
DROP MATERIALIZED VIEW IF EXISTS mv_agent_stats CASCADE;

-- Drop custom types if they exist
DROP TYPE IF EXISTS trip_status_enum CASCADE;
DROP TYPE IF EXISTS trip_type_enum CASCADE;

-- =====================================================
-- CREATE SIMPLE ARCHIVE TABLES
-- =====================================================

-- 1. trips_archive - Archive table for old trips (matching actual trips table structure)
CREATE TABLE IF NOT EXISTS trips_archive (
    trip_id UUID PRIMARY KEY,
    customer_id UUID NOT NULL,
    agent_id UUID NOT NULL,
    pickup_location GEOGRAPHY(POINT, 4326) NOT NULL,
    drop_location GEOGRAPHY(POINT, 4326) NOT NULL,
    item_details JSONB,
    fare_estimate DOUBLE PRECISION,
    delivery_fee DOUBLE PRECISION,
    created_at TIMESTAMP,
    completed_at TIMESTAMP,
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for trips_archive
CREATE INDEX IF NOT EXISTS idx_trips_archive_created_at ON trips_archive(created_at);
CREATE INDEX IF NOT EXISTS idx_trips_archive_agent_id ON trips_archive(agent_id);
-- Removed status index since status column doesn't exist in actual trips table
CREATE INDEX IF NOT EXISTS idx_trips_archive_archived_at ON trips_archive(archived_at);

-- 2. trip_status_updates_archive - Archive table for old trip status updates (matching actual table structure)
CREATE TABLE IF NOT EXISTS trip_status_updates_archive (
    id UUID PRIMARY KEY,
    trip_id UUID NOT NULL,
    timestamp TIMESTAMP,
    location GEOGRAPHY(POINT, 4326),
    updated_by UUID,
    note TEXT,
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for trip_status_updates_archive
CREATE INDEX IF NOT EXISTS idx_trip_status_archive_trip_id ON trip_status_updates_archive(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_status_archive_timestamp ON trip_status_updates_archive(timestamp);
-- Removed status index since status column doesn't exist in actual table
CREATE INDEX IF NOT EXISTS idx_trip_status_archive_archived_at ON trip_status_updates_archive(archived_at);

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================
COMMENT ON TABLE trips_archive IS 'Archive table for trips older than specified retention period';
COMMENT ON TABLE trip_status_updates_archive IS 'Archive table for trip status updates older than specified retention period';

-- =====================================================
-- SIMPLE ARCHIVING FUNCTION
-- =====================================================
CREATE OR REPLACE FUNCTION archive_old_data(older_than_days INTEGER DEFAULT 90)
RETURNS TABLE(trips_archived INTEGER, status_updates_archived INTEGER) AS $$
DECLARE
    trips_count INTEGER := 0;
    status_count INTEGER := 0;
BEGIN
    -- Archive old trips
    INSERT INTO trips_archive (
        trip_id, customer_id, agent_id, pickup_location,
        drop_location, item_details, fare_estimate, delivery_fee,
        created_at, completed_at, archived_at
    )
    SELECT
        trip_id, customer_id, agent_id, pickup_location,
        drop_location, item_details, fare_estimate, delivery_fee,
        created_at, completed_at, CURRENT_TIMESTAMP
    FROM trips
    WHERE created_at < CURRENT_DATE - (older_than_days || ' days')::INTERVAL
    AND trip_id NOT IN (SELECT trip_id FROM trips_archive);

    GET DIAGNOSTICS trips_count = ROW_COUNT;

    -- Archive old trip status updates
    INSERT INTO trip_status_updates_archive (
        id, trip_id, timestamp, location, updated_by, note, archived_at
    )
    SELECT
        id, trip_id, timestamp, location, updated_by, note, CURRENT_TIMESTAMP
    FROM tripstatusupdates
    WHERE timestamp < CURRENT_DATE - (older_than_days || ' days')::INTERVAL
    AND id NOT IN (SELECT id FROM trip_status_updates_archive);

    GET DIAGNOSTICS status_count = ROW_COUNT;

    -- Delete archived data from main tables
    DELETE FROM trips
    WHERE created_at < CURRENT_DATE - (older_than_days || ' days')::INTERVAL
    AND trip_id IN (SELECT trip_id FROM trips_archive);

    DELETE FROM tripstatusupdates
    WHERE timestamp < CURRENT_DATE - (older_than_days || ' days')::INTERVAL
    AND id IN (SELECT id FROM trip_status_updates_archive);
    
    RETURN QUERY SELECT trips_count, status_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- EXAMPLE USAGE
-- =====================================================
-- To archive data older than 90 days:
-- SELECT * FROM archive_old_data(90);

-- To archive data older than 30 days:
-- SELECT * FROM archive_old_data(30);

package main

import (
	"fmt"
	"log"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

func main() {
	fmt.Println("🧪 Testing database trigger functionality...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Get Mike Driver's ID
	var mikeID uuid.UUID
	err = db.QueryRow("SELECT id FROM users WHERE LOWER(name) = 'mike driver' AND user_type = 'agent'").Scan(&mikeID)
	if err != nil {
		log.Fatalf("Failed to find Mike Driver: %v", err)
	}
	fmt.Printf("✅ Found Mike Driver with ID: %s\n", mikeID)

	// Check current LiveLocation for <PERSON>
	fmt.Println("\n📍 BEFORE - Current LiveLocation for <PERSON>:")
	var beforeLat, beforeLon float64
	var beforeTimestamp, beforeUpdated time.Time
	var beforeBattery *float64
	
	err = db.QueryRow(`
		SELECT ST_Y(point) as latitude, ST_X(point) as longitude, 
		       timestamp, updated_at, battery_pct
		FROM livelocations 
		WHERE agent_id = $1
	`, mikeID).Scan(&beforeLat, &beforeLon, &beforeTimestamp, &beforeUpdated, &beforeBattery)
	
	if err != nil {
		fmt.Printf("❌ Error getting current location: %v\n", err)
	} else {
		batteryStr := "N/A"
		if beforeBattery != nil {
			batteryStr = fmt.Sprintf("%.0f%%", *beforeBattery)
		}
		fmt.Printf("   Location: (%.4f, %.4f)\n", beforeLat, beforeLon)
		fmt.Printf("   Timestamp: %s\n", beforeTimestamp.Format("15:04:05"))
		fmt.Printf("   Updated: %s\n", beforeUpdated.Format("15:04:05"))
		fmt.Printf("   Battery: %s\n", batteryStr)
	}

	// Insert new tracking data
	fmt.Println("\n📥 Inserting new tracking data...")
	newLat := 28.6500 // New Delhi location
	newLon := 77.2300
	newBattery := 45.0
	newSpeed := 25.0
	newHeading := 180.0
	
	pointWKT := fmt.Sprintf("POINT(%f %f)", newLon, newLat)
	
	_, err = db.Exec(`
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, battery_pct, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
		)
	`, mikeID, "delivery", pointWKT, time.Now(), newSpeed, newHeading, 
		4.0, 250.0, newBattery, true, "mobile_sdk")
	
	if err != nil {
		log.Fatalf("Failed to insert tracking data: %v", err)
	}
	
	fmt.Printf("   ✅ Inserted: (%.4f, %.4f) Battery: %.0f%% Speed: %.0fm/s\n", 
		newLat, newLon, newBattery, newSpeed)

	// Wait a moment for trigger to execute
	time.Sleep(1 * time.Second)

	// Check LiveLocation again
	fmt.Println("\n📍 AFTER - Updated LiveLocation for Mike:")
	var afterLat, afterLon float64
	var afterTimestamp, afterUpdated time.Time
	var afterBattery *float64
	var afterSpeed, afterHeading float64
	
	err = db.QueryRow(`
		SELECT ST_Y(point) as latitude, ST_X(point) as longitude, 
		       timestamp, updated_at, battery_pct, speed_m_s, heading_deg
		FROM livelocations 
		WHERE agent_id = $1
	`, mikeID).Scan(&afterLat, &afterLon, &afterTimestamp, &afterUpdated, 
		&afterBattery, &afterSpeed, &afterHeading)
	
	if err != nil {
		fmt.Printf("❌ Error getting updated location: %v\n", err)
	} else {
		batteryStr := "N/A"
		if afterBattery != nil {
			batteryStr = fmt.Sprintf("%.0f%%", *afterBattery)
		}
		fmt.Printf("   Location: (%.4f, %.4f)\n", afterLat, afterLon)
		fmt.Printf("   Timestamp: %s\n", afterTimestamp.Format("15:04:05"))
		fmt.Printf("   Updated: %s\n", afterUpdated.Format("15:04:05"))
		fmt.Printf("   Battery: %s\n", batteryStr)
		fmt.Printf("   Speed: %.0fm/s\n", afterSpeed)
		fmt.Printf("   Heading: %.0f°\n", afterHeading)
	}

	// Verify the trigger worked
	fmt.Println("\n🔍 VERIFICATION:")
	if afterLat == newLat && afterLon == newLon {
		fmt.Println("   ✅ Location updated correctly!")
	} else {
		fmt.Println("   ❌ Location NOT updated!")
	}
	
	if afterBattery != nil && *afterBattery == newBattery {
		fmt.Println("   ✅ Battery updated correctly!")
	} else {
		fmt.Println("   ❌ Battery NOT updated!")
	}
	
	if afterSpeed == newSpeed {
		fmt.Println("   ✅ Speed updated correctly!")
	} else {
		fmt.Println("   ❌ Speed NOT updated!")
	}
	
	if afterUpdated.After(beforeUpdated) {
		fmt.Println("   ✅ Updated timestamp is newer!")
	} else {
		fmt.Println("   ❌ Updated timestamp NOT newer!")
	}

	// Check total count in LiveLocations (should still be 5)
	var liveCount int
	err = db.QueryRow("SELECT COUNT(*) FROM livelocations").Scan(&liveCount)
	if err == nil {
		fmt.Printf("   ✅ LiveLocations count: %d (should be 5 - no new rows added)\n", liveCount)
	}

	fmt.Println("\n🎉 Trigger test completed!")
}

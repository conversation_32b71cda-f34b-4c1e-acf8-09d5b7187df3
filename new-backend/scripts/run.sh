#!/bin/bash

# Delivery Tracking Backend Runner Script

set -e

echo "🚀 Starting Delivery Tracking Backend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update .env file with your database credentials"
fi

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21 or higher."
    exit 1
fi

echo "✅ Go version: $(go version)"

# Install dependencies
echo "📦 Installing dependencies..."
go mod tidy

# Test database connection
echo "🔍 Testing database connection..."
go run scripts/test_connection.go

if [ $? -eq 0 ]; then
    echo "✅ Database connection successful!"
else
    echo "❌ Database connection failed. Please check your database configuration."
    exit 1
fi

# Run the application
echo "🎯 Starting the server..."
echo "📍 Server will be available at: http://localhost:8080"
echo "🏥 Health check: http://localhost:8080/health"
echo "📊 Jobs status: http://localhost:8080/jobs/status"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

go run main.go

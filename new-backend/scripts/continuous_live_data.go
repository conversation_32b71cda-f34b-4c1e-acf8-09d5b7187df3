package main

import (
	"fmt"
	"log"
	"math"
	"math/rand"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

type Agent struct {
	ID       uuid.UUID
	Name     string
	Lat      float64
	Lon      float64
	Speed    float64
	Heading  float64
	Battery  float64
}

func main() {
	fmt.Println("🚀 Starting continuous live data simulation...")
	fmt.Println("This simulates drivers sending live location updates every 5 seconds")
	fmt.Println("Press Ctrl+C to stop\n")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Get all active agents from LiveLocations
	agents := []Agent{}
	rows, err := db.Query(`
		SELECT u.id, u.name, 
		       ST_Y(l.point) as latitude, ST_X(l.point) as longitude,
		       COALESCE(l.speed_m_s, 15) as speed_m_s, 
		       COALESCE(l.heading_deg, 0) as heading_deg, 
		       COALESCE(l.battery_pct, 80) as battery_pct
		FROM users u
		JOIN livelocations l ON u.id = l.agent_id
		WHERE u.user_type = 'agent'
		ORDER BY u.name
	`)
	
	if err != nil {
		log.Fatalf("Failed to get agents: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var agent Agent
		err := rows.Scan(&agent.ID, &agent.Name, &agent.Lat, &agent.Lon, 
			&agent.Speed, &agent.Heading, &agent.Battery)
		if err != nil {
			log.Printf("Error scanning agent: %v", err)
			continue
		}
		agents = append(agents, agent)
	}

	if len(agents) == 0 {
		log.Fatal("No agents found in database")
	}

	fmt.Printf("📱 Simulating %d delivery agents:\n", len(agents))
	for _, agent := range agents {
		fmt.Printf("   - %s: (%.4f, %.4f) Battery: %.0f%%\n", 
			agent.Name, agent.Lat, agent.Lon, agent.Battery)
	}
	fmt.Println()

	// Prepare insert statement
	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, battery_pct, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
		)`

	// Simulation loop - runs every 5 seconds
	iteration := 0
	for {
		iteration++
		currentTime := time.Now()
		fmt.Printf("📡 Update #%d - %s\n", iteration, currentTime.Format("15:04:05"))

		// Update each agent
		for i := range agents {
			// Simulate realistic movement patterns
			// Small random movement (like driving through city)
			deltaLat := (rand.Float64() - 0.5) * 0.0008  // ~90m max movement
			deltaLon := (rand.Float64() - 0.5) * 0.0008
			
			agents[i].Lat += deltaLat
			agents[i].Lon += deltaLon
			
			// Simulate realistic speed (5-30 m/s for delivery vehicles)
			agents[i].Speed = 5 + rand.Float64()*25
			
			// Simulate heading changes (gradual turns)
			headingChange := (rand.Float64() - 0.5) * 20 // Max 20° change
			agents[i].Heading = math.Mod(agents[i].Heading + headingChange + 360, 360)
			
			// Simulate battery drain (very slow)
			agents[i].Battery = math.Max(5, agents[i].Battery - rand.Float64()*0.5)

			// Create POINT geometry
			pointWKT := fmt.Sprintf("POINT(%f %f)", agents[i].Lon, agents[i].Lat)

			// Insert tracking data (simulating mobile app sending location)
			_, err = db.Exec(
				insertQuery,
				agents[i].ID,                    // agent_id
				"delivery",                      // role
				pointWKT,                        // geom
				currentTime,                     // timestamp
				agents[i].Speed,                 // speed_m_s
				agents[i].Heading,               // heading_deg
				2.0 + rand.Float64()*3,         // accuracy_m (2-5m GPS accuracy)
				200.0 + rand.Float64()*100,     // altitude_m
				agents[i].Battery,               // battery_pct
				true,                            // is_active
				"mobile_sdk",                    // source
			)

			if err != nil {
				fmt.Printf("❌ Error for %s: %v\n", agents[i].Name, err)
				continue
			}

			fmt.Printf("   📍 %s: (%.4f, %.4f) %.1fm/s %.0f° %.0f%%\n", 
				agents[i].Name, agents[i].Lat, agents[i].Lon, 
				agents[i].Speed, agents[i].Heading, agents[i].Battery)
		}

		fmt.Printf("✅ Sent location updates for %d agents\n", len(agents))
		fmt.Printf("⏱️  Next update in 5 seconds...\n\n")
		
		// Wait 5 seconds before next update (realistic mobile app frequency)
		time.Sleep(5 * time.Second)
	}
}

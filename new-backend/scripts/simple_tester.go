package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

func main() {
	fmt.Println("🚀 Simple Live Location Tester")
	fmt.Println("==============================")
	fmt.Println("")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Println("Choose an option:")
		fmt.Println("1 - Show current locations")
		fmt.Println("2 - Move Mike Driver to Mumbai")
		fmt.Println("3 - Move <PERSON><PERSON> Sharma to Bangalore")
		fmt.Println("4 - Move all agents to different cities")
		fmt.Println("q - Quit")
		fmt.Print("\nEnter choice: ")

		if !scanner.Scan() {
			break
		}
		choice := strings.TrimSpace(scanner.Text())
		fmt.Println("")

		switch choice {
		case "1":
			showCurrentLocations(db)

		case "2":
			fmt.Println("🔄 BEFORE:")
			showCurrentLocations(db)
			
			moveAgent(db, "Mike Driver", 19.0760, 72.8777, "Mumbai", 85)
			
			fmt.Println("🔄 AFTER:")
			showCurrentLocations(db)

		case "3":
			fmt.Println("🔄 BEFORE:")
			showCurrentLocations(db)
			
			moveAgent(db, "Priya Sharma", 12.9716, 77.5946, "Bangalore", 78)
			
			fmt.Println("🔄 AFTER:")
			showCurrentLocations(db)

		case "4":
			fmt.Println("🔄 BEFORE:")
			showCurrentLocations(db)
			
			moveAgent(db, "Mike Driver", 28.6139, 77.2090, "Delhi", 90)
			moveAgent(db, "Priya Sharma", 19.0760, 72.8777, "Mumbai", 85)
			moveAgent(db, "Rajesh Kumar", 12.9716, 77.5946, "Bangalore", 80)
			moveAgent(db, "Anita Patel", 13.0827, 80.2707, "Chennai", 88)
			moveAgent(db, "Vikram Yadav", 22.5726, 88.3639, "Kolkata", 75)
			
			fmt.Println("🔄 AFTER:")
			showCurrentLocations(db)

		case "q", "Q":
			fmt.Println("👋 Goodbye!")
			return

		default:
			fmt.Println("❌ Invalid choice!")
		}

		fmt.Println("\n" + strings.Repeat("=", 50) + "\n")
	}
}

func showCurrentLocations(db *database.DB) {
	fmt.Println("📍 Current LiveLocations:")
	
	rows, err := db.Query(`
		SELECT 
			u.name,
			ROUND(ST_Y(l.point)::numeric, 4) as latitude,
			ROUND(ST_X(l.point)::numeric, 4) as longitude,
			ROUND(l.battery_pct::numeric, 0) as battery,
			l.updated_at
		FROM livelocations l
		JOIN users u ON l.agent_id = u.id
		ORDER BY u.name
	`)
	
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Printf("%-15s | %-8s | %-9s | %-7s | %-20s\n", 
		"Name", "Latitude", "Longitude", "Battery", "Updated")
	fmt.Println(strings.Repeat("-", 75))

	for rows.Next() {
		var name string
		var lat, lon float64
		var battery int
		var updatedAt time.Time

		err := rows.Scan(&name, &lat, &lon, &battery, &updatedAt)
		if err != nil {
			fmt.Printf("❌ Error scanning: %v\n", err)
			continue
		}

		fmt.Printf("%-15s | %8.4f | %9.4f | %5d%% | %s\n", 
			name, lat, lon, battery, updatedAt.Format("15:04:05"))
	}
	fmt.Println("")
}

func moveAgent(db *database.DB, agentName string, lat, lon float64, city string, battery float64) {
	// Get agent ID
	var agentID uuid.UUID
	err := db.QueryRow("SELECT id FROM users WHERE name = $1 AND user_type = 'agent'", agentName).Scan(&agentID)
	if err != nil {
		fmt.Printf("❌ Agent '%s' not found: %v\n", agentName, err)
		return
	}

	// Create POINT geometry string
	pointWKT := fmt.Sprintf("POINT(%f %f)", lon, lat)

	// Insert tracking data
	_, err = db.Exec(`
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, battery_pct, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10, $11
		)
	`, agentID, "delivery", pointWKT, time.Now(), 20.0, 90.0, 3.0, 200.0, battery, true, "mobile_sdk")

	if err != nil {
		fmt.Printf("❌ Error moving %s: %v\n", agentName, err)
		return
	}

	fmt.Printf("✅ Moved %s to %s (%.4f, %.4f) with %.0f%% battery\n", 
		agentName, city, lat, lon, battery)
	
	// Wait a moment for trigger to execute
	time.Sleep(500 * time.Millisecond)
}

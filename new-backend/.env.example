# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=hgcgh
DB_PASSWORD=
DB_NAME=delivery_tracking
DB_SSLMODE=disable

# Server Configuration
SERVER_PORT=8081
SERVER_HOST=localhost
GIN_MODE=debug

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY_HOURS=24

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8100
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Background Jobs
ARCHIVE_INTERVAL_HOURS=24
CLEANUP_INTERVAL_HOURS=6
RATING_UPDATE_INTERVAL_HOURS=1

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# WebSocket Configuration
WS_READ_BUFFER_SIZE=1024
WS_WRITE_BUFFER_SIZE=1024
WS_PING_PERIOD_SECONDS=54

#!/usr/bin/env python3
"""
Generate SQL files for importing GPS data
"""

import csv
import os
from datetime import datetime, timedelta

# <PERSON>'s UUID
MIKE_DRIVER_ID = '3b35ff6d-d482-4cb5-bbc1-ed78774c59b2'

def generate_sql_for_csv(csv_file_path, date_offset, output_file, description):
    """Generate SQL file for CSV data"""
    print(f"📁 Processing {csv_file_path} for {description}")
    
    if not os.path.exists(csv_file_path):
        print(f"❌ File not found: {csv_file_path}")
        return 0
    
    # Count total lines
    with open(csv_file_path, 'r') as f:
        total_lines = sum(1 for line in f) - 1  # Subtract header
    
    print(f"📊 Total GPS points: {total_lines}")
    
    # Calculate time interval
    time_interval_seconds = 86400 / total_lines  # 24 hours in seconds
    print(f"⏱️ Time interval: {time_interval_seconds:.2f} seconds between points")
    
    with open(output_file, 'w') as sql_file:
        sql_file.write(f"-- Import GPS data for <PERSON> ({description})\n")
        sql_file.write("BEGIN;\n\n")
        
        with open(csv_file_path, 'r') as csv_file:
            csv_reader = csv.DictReader(csv_file)
            
            records_generated = 0
            
            for i, row in enumerate(csv_reader):
                try:
                    latitude = float(row['latitude'])
                    longitude = float(row['longitude'])
                    
                    # Calculate timestamp offset in seconds
                    seconds_offset = i * time_interval_seconds
                    
                    # Generate varying data
                    speed = 10 + (i % 20)
                    heading = (i * 5) % 360
                    accuracy = 3 + (i % 5)
                    altitude = 100 + (i % 50)
                    battery = max(20, 100 - (i % 80))
                    
                    # Create SQL insert
                    sql = f"""INSERT INTO trackingupdates (agent_id, role, trip_id, geom, timestamp, speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source) 
VALUES ('{MIKE_DRIVER_ID}', 'delivery', NULL, ST_GeomFromText('POINT({longitude} {latitude})', 4326), 
(CURRENT_DATE + INTERVAL '{date_offset} days' + INTERVAL '{seconds_offset} seconds'), 
{speed}, {heading}, {accuracy}, {altitude}, {battery}, 'mobile_sdk');\n"""
                    
                    sql_file.write(sql)
                    records_generated += 1
                    
                    if records_generated % 1000 == 0:
                        print(f"✅ Generated {records_generated} SQL statements...")
                        
                except (ValueError, KeyError) as e:
                    print(f"⚠️ Skipping invalid row {i}: {e}")
                    continue
        
        sql_file.write("\nCOMMIT;\n")
    
    print(f"✅ Generated SQL file: {output_file} with {records_generated} records")
    return records_generated

def main():
    """Main function"""
    print("🚀 Generating SQL files for Mike Driver GPS data import")
    print("=" * 60)
    
    # Calculate dates
    today = datetime.now()
    yesterday = today - timedelta(days=1)
    
    print(f"📅 Today: {today.date()}")
    print(f"📅 Yesterday: {yesterday.date()}")
    print()
    
    total_records = 0
    
    # Generate SQL for yesterday's data (gpx_data_2.csv)
    print("1️⃣ Generating SQL for YESTERDAY data (gpx_data_2.csv)")
    yesterday_sql = "/tmp/import_yesterday.sql"
    records_yesterday = generate_sql_for_csv(
        '/Users/<USER>/Downloads/delivery-tracking/gpx_data_2.csv', 
        -1, 
        yesterday_sql, 
        'YESTERDAY'
    )
    total_records += records_yesterday
    print()
    
    # Generate SQL for today's data (gpx_data_4.csv)
    print("2️⃣ Generating SQL for TODAY data (gpx_data_4.csv)")
    today_sql = "/tmp/import_today.sql"
    records_today = generate_sql_for_csv(
        '/Users/<USER>/Downloads/delivery-tracking/gpx_data_4.csv', 
        0, 
        today_sql, 
        'TODAY'
    )
    total_records += records_today
    print()
    
    print("=" * 60)
    print(f"🎉 SQL generation completed successfully!")
    print(f"📊 Total records to be imported: {total_records}")
    print(f"📊 Yesterday records: {records_yesterday}")
    print(f"📊 Today records: {records_today}")
    print()
    print("📝 Generated SQL files:")
    print(f"   Yesterday: {yesterday_sql}")
    print(f"   Today: {today_sql}")
    print()
    print("🔧 To execute the SQL files, run:")
    print(f'   PGPASSWORD="" psql -h localhost -p 5432 -U hgcgh -d delivery_tracking -f {yesterday_sql}')
    print(f'   PGPASSWORD="" psql -h localhost -p 5432 -U hgcgh -d delivery_tracking -f {today_sql}')

if __name__ == "__main__":
    main()

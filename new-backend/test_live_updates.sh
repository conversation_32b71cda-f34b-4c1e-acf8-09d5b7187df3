#!/bin/bash

# Colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Database connection details
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="hgcgh"
DB_NAME="delivery_tracking"
export PGPASSWORD=""

echo -e "${BLUE}🚀 Live Location Update Tester${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""
echo -e "${YELLOW}This script will add tracking data to TrackingUpdates table${NC}"
echo -e "${YELLOW}and show you how LiveLocations gets updated automatically!${NC}"
echo ""

# Function to execute SQL and show results
execute_sql() {
    local query="$1"
    local description="$2"
    
    echo -e "${CYAN}$description${NC}"
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$query"
    echo ""
}

# Function to show current LiveLocations
show_live_locations() {
    echo -e "${PURPLE}📍 Current LiveLocations:${NC}"
    execute_sql "
        SELECT 
            u.name,
            ROUND(ST_Y(l.point)::numeric, 4) as latitude,
            ROUND(ST_X(l.point)::numeric, 4) as longitude,
            ROUND(l.speed_m_s::numeric, 1) as speed_ms,
            ROUND(l.battery_pct::numeric, 0) as battery,
            l.updated_at::time as updated_time
        FROM livelocations l
        JOIN users u ON l.agent_id = u.id
        ORDER BY u.name;
    " ""
}

# Function to add tracking data
add_tracking_data() {
    local round_num="$1"
    
    case $round_num in
        1)
            echo -e "${GREEN}📥 Adding Round 1 Data - Morning Shift Start${NC}"
            locations=(
                "Mike Driver,28.6139,77.2090,15.5,90,85"      # Connaught Place
                "Priya Sharma,28.7041,77.1025,12.3,45,88"     # Gurgaon
                "Rajesh Kumar,28.5535,77.2588,18.7,180,82"    # Lotus Temple
                "Anita Patel,28.6692,77.2300,14.2,270,90"     # Chandni Chowk
                "Vikram Yadav,28.5244,77.1855,16.8,135,75"    # Qutub Minar
            )
            ;;
        2)
            echo -e "${GREEN}📥 Adding Round 2 Data - Mid Morning Deliveries${NC}"
            locations=(
                "Mike Driver,28.6280,77.2184,22.1,120,83"      # Rajpath
                "Priya Sharma,28.6950,77.1100,19.5,90,85"      # Moving in Gurgaon
                "Rajesh Kumar,28.5600,77.2500,25.3,200,79"     # Near Lotus Temple
                "Anita Patel,28.6600,77.2400,17.8,300,87"      # Moving from Chandni Chowk
                "Vikram Yadav,28.5300,77.1900,20.4,160,72"     # Near Qutub Minar
            )
            ;;
        3)
            echo -e "${GREEN}📥 Adding Round 3 Data - Lunch Time Rush${NC}"
            locations=(
                "Mike Driver,28.6507,77.2334,28.9,200,80"      # Jama Masjid
                "Priya Sharma,28.6800,77.1200,24.6,135,82"     # Gurgaon center
                "Rajesh Kumar,28.5700,77.2400,31.2,250,76"     # Moving north
                "Anita Patel,28.6500,77.2500,26.7,45,84"       # Central Delhi
                "Vikram Yadav,28.5400,77.2000,23.1,180,69"     # Moving east
            )
            ;;
        4)
            echo -e "${GREEN}📥 Adding Round 4 Data - Afternoon Deliveries${NC}"
            locations=(
                "Mike Driver,28.6562,77.2410,19.3,270,77"      # Red Fort
                "Priya Sharma,28.6700,77.1300,21.8,180,79"     # Gurgaon outskirts
                "Rajesh Kumar,28.5800,77.2300,27.5,300,73"     # Central area
                "Anita Patel,28.6400,77.2600,23.4,90,81"       # East Delhi
                "Vikram Yadav,28.5500,77.2100,25.7,225,66"     # South Delhi
            )
            ;;
        5)
            echo -e "${GREEN}📥 Adding Round 5 Data - Evening Rush${NC}"
            locations=(
                "Mike Driver,28.6129,77.2295,16.7,315,74"      # India Gate
                "Priya Sharma,28.6600,77.1400,18.9,225,76"     # Returning to base
                "Rajesh Kumar,28.5900,77.2200,22.3,45,70"      # North Delhi
                "Anita Patel,28.6300,77.2700,20.1,135,78"      # Far East Delhi
                "Vikram Yadav,28.5600,77.2200,19.8,270,63"     # Central South
            )
            ;;
        *)
            echo -e "${RED}❌ Invalid round number!${NC}"
            return 1
            ;;
    esac
    
    # Insert data for each agent
    for location_data in "${locations[@]}"; do
        IFS=',' read -r name lat lon speed heading battery <<< "$location_data"
        
        # Get agent ID
        agent_id=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
            SELECT id FROM users WHERE name = '$name' AND user_type = 'agent';
        " | tr -d ' ')
        
        if [ -z "$agent_id" ]; then
            echo -e "${RED}❌ Agent '$name' not found!${NC}"
            continue
        fi
        
        # Insert tracking data
        execute_sql "
            INSERT INTO trackingupdates (
                agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
                accuracy_m, altitude_m, battery_pct, is_active, source
            ) VALUES (
                '$agent_id', 'delivery', ST_GeomFromText('POINT($lon $lat)', 4326),
                NOW(), $speed, $heading, 3.5, 220, $battery, true, 'mobile_sdk'
            );
        " "   ✅ Added data for $name: ($lat, $lon) Speed: ${speed}m/s Battery: ${battery}%"
    done
    
    echo -e "${GREEN}🎉 Round $round_num data added successfully!${NC}"
    echo ""
}

# Main menu loop
while true; do
    echo -e "${BLUE}📋 Choose an option:${NC}"
    echo -e "${CYAN}1${NC} - Add Round 1 Data (Morning Shift Start)"
    echo -e "${CYAN}2${NC} - Add Round 2 Data (Mid Morning Deliveries)"
    echo -e "${CYAN}3${NC} - Add Round 3 Data (Lunch Time Rush)"
    echo -e "${CYAN}4${NC} - Add Round 4 Data (Afternoon Deliveries)"
    echo -e "${CYAN}5${NC} - Add Round 5 Data (Evening Rush)"
    echo -e "${CYAN}s${NC} - Show current LiveLocations"
    echo -e "${CYAN}c${NC} - Check API response"
    echo -e "${CYAN}q${NC} - Quit"
    echo ""
    echo -n -e "${YELLOW}Enter your choice: ${NC}"
    read choice
    echo ""
    
    case $choice in
        1|2|3|4|5)
            echo -e "${BLUE}🔄 BEFORE - Current state:${NC}"
            show_live_locations
            
            add_tracking_data $choice
            
            echo -e "${BLUE}🔄 AFTER - Updated state:${NC}"
            show_live_locations
            
            echo -e "${PURPLE}⏱️  Database trigger executed automatically!${NC}"
            echo -e "${PURPLE}Notice how LiveLocations got updated (not new rows added)${NC}"
            echo ""
            ;;
        s|S)
            show_live_locations
            ;;
        c|C)
            echo -e "${CYAN}🌐 Testing API endpoint:${NC}"
            curl -s "http://localhost:8080/api/tracking/active-agents?within_minutes=60" | jq '.data.agents[] | {name: .name, location: .location, battery: .battery_pct, updated: .updated_at}' 2>/dev/null || echo "API call failed or jq not installed"
            echo ""
            ;;
        q|Q)
            echo -e "${GREEN}👋 Thanks for testing! Your trigger is working perfectly!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Invalid choice! Please enter 1-5, s, c, or q${NC}"
            echo ""
            ;;
    esac
    
    echo -e "${BLUE}=================================${NC}"
    echo ""
done

#!/usr/bin/env python3
"""
Simple GPS data import for <PERSON> using subprocess
"""

import csv
import subprocess
import sys
from datetime import datetime, timedelta
import os

# <PERSON>'s UUID
MIKE_DRIVER_ID = '3b35ff6d-d482-4cb5-bbc1-ed78774c59b2'

def execute_sql(sql_command):
    """Execute SQL command using psql"""
    cmd = [
        'psql',
        '-h', 'localhost',
        '-p', '5432',
        '-U', 'hgcgh',
        '-d', 'delivery_tracking',
        '-c', sql_command
    ]
    
    env = os.environ.copy()
    env['PGPASSWORD'] = ''
    
    try:
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ SQL Error: {e.stderr}")
        return None

def import_csv_data(csv_file_path, date_offset, description):
    """Import CSV data for a specific date"""
    print(f"📁 Processing {csv_file_path} for {description}")
    
    if not os.path.exists(csv_file_path):
        print(f"❌ File not found: {csv_file_path}")
        return 0
    
    # Count total lines
    with open(csv_file_path, 'r') as f:
        total_lines = sum(1 for line in f) - 1  # Subtract header
    
    print(f"📊 Total GPS points: {total_lines}")
    
    # Calculate time interval
    time_interval_seconds = 86400 / total_lines  # 24 hours in seconds
    print(f"⏱️ Time interval: {time_interval_seconds:.2f} seconds between points")
    
    # Start transaction
    execute_sql("BEGIN;")
    
    records_inserted = 0
    batch_size = 100
    batch_sql = []
    
    with open(csv_file_path, 'r') as file:
        csv_reader = csv.DictReader(file)
        
        for i, row in enumerate(csv_reader):
            try:
                latitude = float(row['latitude'])
                longitude = float(row['longitude'])
                
                # Calculate timestamp offset in seconds
                seconds_offset = i * time_interval_seconds
                
                # Generate varying data
                speed = 10 + (i % 20)
                heading = (i * 5) % 360
                accuracy = 3 + (i % 5)
                altitude = 100 + (i % 50)
                battery = max(20, 100 - (i % 80))
                
                # Create SQL insert
                sql = f"""INSERT INTO trackingupdates (agent_id, role, trip_id, geom, timestamp, speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source) 
                         VALUES ('{MIKE_DRIVER_ID}', 'delivery', NULL, ST_GeomFromText('POINT({longitude} {latitude})', 4326), 
                         (CURRENT_DATE + INTERVAL '{date_offset} days' + INTERVAL '{seconds_offset} seconds'), 
                         {speed}, {heading}, {accuracy}, {altitude}, {battery}, 'mobile_sdk');"""
                
                batch_sql.append(sql)
                
                # Execute batch when it reaches batch_size
                if len(batch_sql) >= batch_size:
                    combined_sql = '\n'.join(batch_sql)
                    if execute_sql(combined_sql):
                        records_inserted += len(batch_sql)
                        batch_sql = []
                        
                        if records_inserted % 1000 == 0:
                            print(f"✅ Inserted {records_inserted} records...")
                    else:
                        print(f"❌ Failed to insert batch at record {i}")
                        execute_sql("ROLLBACK;")
                        return 0
                        
            except (ValueError, KeyError) as e:
                print(f"⚠️ Skipping invalid row {i}: {e}")
                continue
        
        # Insert remaining batch
        if batch_sql:
            combined_sql = '\n'.join(batch_sql)
            if execute_sql(combined_sql):
                records_inserted += len(batch_sql)
            else:
                print(f"❌ Failed to insert final batch")
                execute_sql("ROLLBACK;")
                return 0
    
    # Commit transaction
    execute_sql("COMMIT;")
    
    print(f"✅ Successfully imported {records_inserted} records for {description}")
    return records_inserted

def main():
    """Main function"""
    print("🚀 Starting GPS data import for Mike Driver")
    print("=" * 50)
    
    # Calculate dates
    today = datetime.now()
    yesterday = today - timedelta(days=1)
    
    print(f"📅 Today: {today.date()}")
    print(f"📅 Yesterday: {yesterday.date()}")
    print()
    
    total_records = 0
    
    # Import yesterday's data (gpx_data_2.csv)
    print("1️⃣ Importing YESTERDAY data (gpx_data_2.csv)")
    records_yesterday = import_csv_data('/Users/<USER>/Downloads/delivery-tracking/gpx_data_2.csv', -1, 'YESTERDAY')
    total_records += records_yesterday
    print()
    
    # Import today's data (gpx_data_4.csv)
    print("2️⃣ Importing TODAY data (gpx_data_4.csv)")
    records_today = import_csv_data('/Users/<USER>/Downloads/delivery-tracking/gpx_data_4.csv', 0, 'TODAY')
    total_records += records_today
    print()
    
    print("=" * 50)
    print(f"🎉 Import completed successfully!")
    print(f"📊 Total records imported: {total_records}")
    print(f"📊 Yesterday records: {records_yesterday}")
    print(f"📊 Today records: {records_today}")
    
    # Verify the import
    result = execute_sql(f"SELECT COUNT(*) FROM trackingupdates WHERE agent_id = '{MIKE_DRIVER_ID}';")
    if result:
        db_count = result.strip().split('\n')[2].strip()  # Extract count from psql output
        print(f"✅ Database verification: {db_count} records found for Mike Driver")
    
    # Show date distribution
    print("\n📊 Date distribution:")
    result = execute_sql(f"SELECT DATE(timestamp) as date, COUNT(*) as records FROM trackingupdates WHERE agent_id = '{MIKE_DRIVER_ID}' GROUP BY DATE(timestamp) ORDER BY date;")
    if result:
        print(result)

if __name__ == "__main__":
    main()

package utils

import (
	"math"
)

const (
	// EarthRadiusKm is the Earth's radius in kilometers
	EarthRadiusKm = 6371.0
)

// CalculateDistance calculates the distance between two geographic points using the Haversine formula
// Returns distance in kilometers
func CalculateDistance(lat1, lon1, lat2, lon2 float64) float64 {
	// Convert degrees to radians
	lat1Rad := lat1 * math.Pi / 180
	lon1Rad := lon1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lon2Rad := lon2 * math.Pi / 180

	// Haversine formula
	dLat := lat2Rad - lat1Rad
	dLon := lon2Rad - lon1Rad

	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(dLon/2)*math.Sin(dLon/2)

	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return EarthRadiusKm * c
}

// CalculateBearing calculates the bearing (direction) from point 1 to point 2
// Returns bearing in degrees (0-360)
func CalculateBearing(lat1, lon1, lat2, lon2 float64) float64 {
	// Convert degrees to radians
	lat1Rad := lat1 * math.Pi / 180
	lon1Rad := lon1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lon2Rad := lon2 * math.Pi / 180

	dLon := lon2Rad - lon1Rad

	y := math.Sin(dLon) * math.Cos(lat2Rad)
	x := math.Cos(lat1Rad)*math.Sin(lat2Rad) - math.Sin(lat1Rad)*math.Cos(lat2Rad)*math.Cos(dLon)

	bearing := math.Atan2(y, x)

	// Convert to degrees and normalize to 0-360
	bearingDeg := bearing * 180 / math.Pi
	if bearingDeg < 0 {
		bearingDeg += 360
	}

	return bearingDeg
}

// IsPointInRadius checks if a point is within a certain radius of another point
func IsPointInRadius(centerLat, centerLon, pointLat, pointLon, radiusKm float64) bool {
	distance := CalculateDistance(centerLat, centerLon, pointLat, pointLon)
	return distance <= radiusKm
}

// CalculateSpeed calculates speed between two points given the time difference
// Returns speed in m/s
func CalculateSpeed(lat1, lon1, lat2, lon2 float64, timeDiffSeconds float64) float64 {
	if timeDiffSeconds <= 0 {
		return 0
	}

	distanceKm := CalculateDistance(lat1, lon1, lat2, lon2)
	distanceM := distanceKm * 1000 // Convert to meters
	
	return distanceM / timeDiffSeconds
}

// BoundingBox represents a geographic bounding box
type BoundingBox struct {
	MinLat float64 `json:"min_lat"`
	MaxLat float64 `json:"max_lat"`
	MinLon float64 `json:"min_lon"`
	MaxLon float64 `json:"max_lon"`
}

// CalculateBoundingBox calculates a bounding box around a center point with given radius
func CalculateBoundingBox(centerLat, centerLon, radiusKm float64) BoundingBox {
	// Approximate degrees per kilometer
	latDegPerKm := 1.0 / 111.0
	lonDegPerKm := 1.0 / (111.0 * math.Cos(centerLat*math.Pi/180))

	latOffset := radiusKm * latDegPerKm
	lonOffset := radiusKm * lonDegPerKm

	return BoundingBox{
		MinLat: centerLat - latOffset,
		MaxLat: centerLat + latOffset,
		MinLon: centerLon - lonOffset,
		MaxLon: centerLon + lonOffset,
	}
}

// IsPointInBoundingBox checks if a point is within a bounding box
func IsPointInBoundingBox(lat, lon float64, bbox BoundingBox) bool {
	return lat >= bbox.MinLat && lat <= bbox.MaxLat &&
		lon >= bbox.MinLon && lon <= bbox.MaxLon
}

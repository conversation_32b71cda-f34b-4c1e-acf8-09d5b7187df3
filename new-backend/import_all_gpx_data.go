package main

import (
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"

	"github.com/google/uuid"
)

type GPXFile struct {
	Path string
	Date string
	Name string
}

func main() {
	fmt.Println("🚀 Starting GPS data import from all GPX CSV files...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Use the specific agent ID from the frontend
	agentID := "3b35ff6d-d482-4cb5-bbc1-ed78774c59b2"
	agentUUID, err := uuid.Parse(agentID)
	if err != nil {
		log.Fatalf("Failed to parse agent UUID: %v", err)
	}
	fmt.Printf("✅ Using agent ID: %s\n", agentUUID)

	// Define the GPX files to import with different dates
	gpxFiles := []GPXFile{
		{
			Path: "/Users/<USER>/Downloads/delivery-tracking/gpx_data.csv",
			Date: "2025-07-13",
			Name: "gpx_data.csv (438 points)",
		},
		{
			Path: "/Users/<USER>/Downloads/delivery-tracking/gpx_data_2.csv", 
			Date: "2025-07-14",
			Name: "gpx_data_2.csv (1584 points)",
		},
		{
			Path: "/Users/<USER>/Downloads/delivery-tracking/gpx_data_3.csv",
			Date: "2025-07-15", 
			Name: "gpx_data_3.csv (4961 points)",
		},
	}

	totalImported := 0
	totalErrors := 0

	// Process each GPX file
	for _, gpxFile := range gpxFiles {
		fmt.Printf("\n📁 Processing %s for date %s...\n", gpxFile.Name, gpxFile.Date)
		
		imported, errors := importGPXFile(db, agentUUID, gpxFile)
		totalImported += imported
		totalErrors += errors
	}

	fmt.Printf("\n🎉 ALL IMPORTS COMPLETED!\n")
	fmt.Printf("📊 Total successfully imported: %d records\n", totalImported)
	fmt.Printf("❌ Total errors: %d records\n", totalErrors)
	fmt.Println("\n🔍 You can now test GPS line drawing on these dates:")
	fmt.Println("📅 2025-07-13 (438 GPS points)")
	fmt.Println("📅 2025-07-14 (1584 GPS points)")  
	fmt.Println("📅 2025-07-15 (4961 GPS points)")
	fmt.Printf("👤 Agent ID: %s\n", agentID)
}

func importGPXFile(db *database.DB, agentUUID uuid.UUID, gpxFile GPXFile) (int, int) {
	// Open CSV file
	file, err := os.Open(gpxFile.Path)
	if err != nil {
		log.Printf("❌ Failed to open %s: %v", gpxFile.Path, err)
		return 0, 1
	}
	defer file.Close()

	// Parse CSV
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		log.Printf("❌ Failed to read CSV %s: %v", gpxFile.Path, err)
		return 0, 1
	}

	fmt.Printf("📊 Found %d records in %s\n", len(records)-1, gpxFile.Name)

	// Clear existing data for this agent for this date
	deleteQuery := `DELETE FROM trackingupdates WHERE agent_id = $1 AND DATE(timestamp) = $2`
	result, err := db.Exec(deleteQuery, agentUUID, gpxFile.Date)
	if err != nil {
		log.Printf("⚠️ Warning: Failed to clear existing data for %s: %v", gpxFile.Date, err)
	} else {
		rowsAffected, _ := result.RowsAffected()
		fmt.Printf("🗑️ Cleared %d existing records for %s\n", rowsAffected, gpxFile.Date)
	}

	// Prepare insert statement
	insertQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, geom, timestamp, speed_m_s, heading_deg, 
			accuracy_m, altitude_m, is_active, source
		) VALUES (
			$1, $2, ST_GeomFromText($3, 4326), $4, $5, $6, $7, $8, $9, $10
		)`

	// Start transaction
	tx, err := db.Begin()
	if err != nil {
		log.Printf("❌ Failed to begin transaction for %s: %v", gpxFile.Date, err)
		return 0, 1
	}
	defer tx.Rollback()

	// Prepare statement
	stmt, err := tx.Prepare(insertQuery)
	if err != nil {
		log.Printf("❌ Failed to prepare statement for %s: %v", gpxFile.Date, err)
		return 0, 1
	}
	defer stmt.Close()

	// Parse the date and create base time
	baseDate, err := time.Parse("2006-01-02", gpxFile.Date)
	if err != nil {
		log.Printf("❌ Failed to parse date %s: %v", gpxFile.Date, err)
		return 0, 1
	}
	baseTime := baseDate.Add(9 * time.Hour) // Start at 9 AM

	successCount := 0
	errorCount := 0

	fmt.Printf("📥 Importing GPS data for %s...\n", gpxFile.Date)

	for i, record := range records {
		if i == 0 {
			continue // Skip header
		}

		// Parse latitude and longitude
		lat, err := strconv.ParseFloat(record[0], 64)
		if err != nil {
			errorCount++
			continue
		}

		lon, err := strconv.ParseFloat(record[1], 64)
		if err != nil {
			errorCount++
			continue
		}

		// Parse elevation (optional)
		var elevation *float64
		if len(record) > 2 && record[2] != "" {
			elev, err := strconv.ParseFloat(record[2], 64)
			if err == nil {
				elevation = &elev
			}
		}

		// Create timestamp - spread the points over the day with 30-second intervals
		timestamp := baseTime.Add(time.Duration(i*30) * time.Second)

		// Create POINT geometry string
		pointWKT := fmt.Sprintf("POINT(%f %f)", lon, lat)

		// Calculate some realistic values
		var speed *float64
		var heading *float64
		var accuracy *float64

		if i > 1 {
			speedValue := float64(5 + (i%15)) // 5-20 m/s variation
			speed = &speedValue

			headingValue := float64((i * 3) % 360) // Rotating heading
			heading = &headingValue

			accuracyValue := float64(3 + (i%5)) // 3-8 meters accuracy
			accuracy = &accuracyValue
		}

		// Insert into database
		_, err = stmt.Exec(
			agentUUID,        // agent_id
			"driver",         // role
			pointWKT,         // geom (POINT)
			timestamp,        // timestamp
			speed,            // speed_m_s
			heading,          // heading_deg
			accuracy,         // accuracy_m
			elevation,        // altitude_m
			true,             // is_active
			"gps_device",     // source
		)

		if err != nil {
			errorCount++
			continue
		}

		successCount++

		// Progress indicator
		if i%500 == 0 {
			fmt.Printf("📍 Processed %d/%d records for %s...\n", i, len(records)-1, gpxFile.Date)
		}
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		log.Printf("❌ Failed to commit transaction for %s: %v", gpxFile.Date, err)
		return 0, errorCount + 1
	}

	fmt.Printf("✅ %s: Successfully imported %d records, %d errors\n", gpxFile.Date, successCount, errorCount)
	return successCount, errorCount
}

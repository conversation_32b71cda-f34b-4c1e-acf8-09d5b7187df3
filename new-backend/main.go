package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/database"
	"delivery-tracking-backend/handlers"
	"delivery-tracking-backend/jobs"
	"delivery-tracking-backend/middleware"
	"delivery-tracking-backend/repository"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logrus.WithError(err).Fatal("Failed to load configuration")
	}

	// Setup logging
	setupLogging(cfg)

	// Set Gin mode
	gin.SetMode(cfg.Server.GinMode)

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		logrus.WithError(err).Fatal("Failed to connect to database")
	}
	defer db.Close()

	// Initialize repositories
	userRepo := repository.NewUserRepository(db)
	agentRepo := repository.NewAgentRepository(db)
	tripRepo := repository.NewTripRepository(db)
	trackingRepo := repository.NewTrackingRepository(db)
	paymentRepo := repository.NewPaymentRepository(db)
	ratingRepo := repository.NewRatingRepository(db)
	markerSettingsRepo := repository.NewMarkerSettingsRepository(db)
	temperatureRepo := repository.NewTemperatureRepository(db)
	routeComplianceRepo := repository.NewRouteComplianceRepository(db)
	inventoryRepo := repository.NewInventoryRepository(db)
	deliveryAssignmentRepo := repository.NewDeliveryAssignmentRepository(db)

	// Initialize handlers
	userHandler := handlers.NewUserHandler(userRepo, agentRepo)
	tripHandler := handlers.NewTripHandler(tripRepo, userRepo)
	trackingHandler := handlers.NewTrackingHandler(trackingRepo)
	paymentHandler := handlers.NewPaymentHandler(paymentRepo, tripRepo)
	ratingHandler := handlers.NewRatingHandler(ratingRepo, tripRepo, userRepo)
	markerSettingsHandler := handlers.NewMarkerSettingsHandler(markerSettingsRepo)
	temperatureHandler := handlers.NewTemperatureHandler(temperatureRepo, tripRepo)
	deliveryPredictionsHandler := handlers.NewDeliveryPredictionsHandler(tripRepo, trackingRepo)
	routeComplianceHandler := handlers.NewRouteComplianceHandler(routeComplianceRepo, tripRepo)
	inventoryHandler := handlers.NewInventoryHandler(inventoryRepo, tripRepo)
	routingHandler := handlers.NewRoutingHandler()
	deliveryAssignmentHandler := handlers.NewDeliveryAssignmentHandler(deliveryAssignmentRepo)

	// Initialize and start background jobs
	jobScheduler := jobs.NewJobScheduler(cfg, trackingRepo, ratingRepo, userRepo, agentRepo)
	jobScheduler.Start()
	defer jobScheduler.Stop()

	// Setup router
	router := setupRouter(cfg, userHandler, tripHandler, trackingHandler, paymentHandler, ratingHandler, markerSettingsHandler, temperatureHandler, deliveryPredictionsHandler, routeComplianceHandler, inventoryHandler, routingHandler, deliveryAssignmentHandler, jobScheduler)

	// Create HTTP server
	server := &http.Server{
		Addr:    cfg.GetServerAddress(),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logrus.WithField("address", cfg.GetServerAddress()).Info("Starting HTTP server")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logrus.WithError(err).Error("Server forced to shutdown")
	} else {
		logrus.Info("Server shutdown complete")
	}
}

func setupLogging(cfg *config.Config) {
	// Set log level
	level, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		logrus.WithError(err).Warn("Invalid log level, using info")
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)

	// Set log format
	if cfg.Logging.Format == "json" {
		logrus.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	} else {
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: time.RFC3339,
		})
	}

	logrus.Info("Logging configured successfully")
}

func setupRouter(cfg *config.Config, userHandler *handlers.UserHandler, tripHandler *handlers.TripHandler,
	trackingHandler *handlers.TrackingHandler, paymentHandler *handlers.PaymentHandler,
	ratingHandler *handlers.RatingHandler, markerSettingsHandler *handlers.MarkerSettingsHandler,
	temperatureHandler *handlers.TemperatureHandler, deliveryPredictionsHandler *handlers.DeliveryPredictionsHandler,
	routeComplianceHandler *handlers.RouteComplianceHandler, inventoryHandler *handlers.InventoryHandler,
	routingHandler *handlers.RoutingHandler, deliveryAssignmentHandler *handlers.DeliveryAssignmentHandler, jobScheduler *jobs.JobScheduler) *gin.Engine {
	
	router := gin.New()

	// Global middleware
	router.Use(middleware.ErrorHandlerMiddleware())
	router.Use(middleware.LoggingMiddleware())
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.RateLimitMiddleware(cfg))

	// CORS middleware - Allow all origins for development
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-User-Timezone, X-User-Timezone-Offset, X-Request-Id, Accept, Origin")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Type")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})

	// Serve static files for uploaded images
	router.Static("/uploads", "./uploads")

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"version":   "1.0.0",
		})
	})

	// Jobs status endpoint
	router.GET("/jobs/status", func(c *gin.Context) {
		status := jobScheduler.GetJobStatus()
		c.JSON(http.StatusOK, gin.H{
			"status":    "success",
			"timestamp": time.Now().Format(time.RFC3339),
			"jobs":      status,
		})
	})

	// API routes
	api := router.Group("/api")
	{
		// User management routes
		users := api.Group("/users")
		{
			users.POST("/register", userHandler.RegisterUser)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.GET("/customers", userHandler.GetCustomers)
		}

		// Agent management routes
		agents := api.Group("/agents")
		{
			agents.GET("/available", userHandler.GetAvailableAgents)
			agents.PUT("/:id/availability", userHandler.UpdateAgentAvailability)
			agents.PUT("/:id/profile", userHandler.UpdateAgentProfile)
			agents.GET("/:id/stats", userHandler.GetAgentStats)
		}

		// Trip management routes
		trips := api.Group("/trip")
		{
			trips.POST("/create", tripHandler.CreateTrip)
			trips.GET("/:trip_id", tripHandler.GetTrip)
			trips.POST("/status-update", tripHandler.UpdateTripStatus)
			trips.GET("/:trip_id/status-updates", tripHandler.GetTripStatusUpdates)
			trips.GET("/history/:user_id", tripHandler.GetTripHistory)
			trips.GET("/active", tripHandler.GetActiveTrips)
			trips.POST("/:trip_id/accept", tripHandler.AcceptTrip)
			trips.POST("/assign-delivery", tripHandler.AssignDelivery)
			trips.GET("/agent/:agent_id/deliveries", tripHandler.GetAgentDeliveries)
		}

		// GPS tracking routes
		location := api.Group("/location")
		{
			location.POST("/ping", trackingHandler.LocationPing)
			location.POST("/batch-ping", trackingHandler.BatchLocationPing)
		}

		tracking := api.Group("/tracking")
		{
			tracking.GET("/live-location/:agent_id", trackingHandler.GetLiveLocation)
			tracking.GET("/history/:agent_id", trackingHandler.GetTrackingHistory)
			tracking.GET("/active-agents", trackingHandler.GetActiveAgents)
			tracking.GET("/stats/:agent_id", trackingHandler.GetTrackingStats)
			tracking.GET("/delivery-stats/:agent_id", trackingHandler.GetAgentDeliveryStats)
			tracking.GET("/agents-delivery-counts", trackingHandler.GetAgentsDeliveryCounts)
		}

		// Geocoding routes
		geocoding := api.Group("/geocoding")
		{
			geocoding.POST("/save", trackingHandler.SaveGeocodedName)
		}

		// Payment routes
		payments := api.Group("/payments")
		{
			payments.POST("/create", paymentHandler.CreatePayment)
			payments.GET("/:payment_id", paymentHandler.GetPayment)
			payments.PUT("/:payment_id/status", paymentHandler.UpdatePaymentStatus)
			payments.GET("/status/:status", paymentHandler.GetPaymentsByStatus)
			payments.GET("/stats", paymentHandler.GetPaymentStats)
			payments.GET("/user/:user_id", paymentHandler.GetUserPaymentHistory)
			payments.GET("/trip/:trip_id", paymentHandler.GetTripPayment)
		}

		// Rating routes
		ratings := api.Group("/ratings")
		{
			ratings.POST("/create", ratingHandler.CreateRating)
			ratings.GET("/:rating_id", ratingHandler.GetRating)
			ratings.GET("/user/:user_id", ratingHandler.GetUserRatings)
			ratings.GET("/trip/:trip_id", ratingHandler.GetTripRatings)
			ratings.GET("/top-agents", ratingHandler.GetTopRatedAgents)
			ratings.GET("/recent", ratingHandler.GetRecentRatings)
			ratings.DELETE("/:rating_id", ratingHandler.DeleteRating)
		}

		// Marker settings routes
		markers := api.Group("/markers")
		{
			markers.GET("/settings/:user_id", markerSettingsHandler.GetMarkerSettings)
			markers.GET("/settings/:user_id/:marker_type", markerSettingsHandler.GetMarkerSettingByType)
			markers.POST("/settings/:user_id", markerSettingsHandler.SaveMarkerSetting)
			markers.DELETE("/settings/:user_id/:marker_type", markerSettingsHandler.DeleteMarkerSetting)
			markers.POST("/upload/:user_id", markerSettingsHandler.UploadMarkerImage)
		}

		// Temperature monitoring routes (Food & Pharma industries)
		temperature := api.Group("/temperature")
		{
			temperature.POST("/reading", temperatureHandler.RecordTemperatureReading)
			temperature.GET("/history/:trip_id", temperatureHandler.GetTemperatureHistory)
			temperature.GET("/alerts", temperatureHandler.GetTemperatureAlerts)
			temperature.POST("/requirements", temperatureHandler.SetTemperatureRequirements)
			temperature.PUT("/alerts/:alert_id/resolve", temperatureHandler.ResolveTemperatureAlert)
			temperature.GET("/dashboard", temperatureHandler.GetTemperatureDashboard)
		}

		// Delivery predictions routes (E-commerce industry)
		deliveryPredictions := api.Group("/delivery-predictions")
		{
			deliveryPredictions.POST("/eta", deliveryPredictionsHandler.CalculateETA)
			deliveryPredictions.GET("/stats", deliveryPredictionsHandler.GetDeliveryStats)
			deliveryPredictions.GET("/agent-performance/:agent_id", deliveryPredictionsHandler.GetAgentPerformance)
		}

		// Route compliance routes (Logistics industry)
		routeCompliance := api.Group("/route-compliance")
		{
			routeCompliance.POST("/geofences", routeComplianceHandler.CreateGeofence)
			routeCompliance.GET("/geofences", routeComplianceHandler.GetGeofences)
			routeCompliance.PUT("/geofences/:geofence_id", routeComplianceHandler.UpdateGeofence)
			routeCompliance.DELETE("/geofences/:geofence_id", routeComplianceHandler.DeleteGeofence)
			routeCompliance.POST("/planned-routes", routeComplianceHandler.CreatePlannedRoute)
			routeCompliance.GET("/violations", routeComplianceHandler.GetComplianceViolations)
			routeCompliance.PUT("/violations/:violation_id/resolve", routeComplianceHandler.ResolveViolation)
			routeCompliance.GET("/dashboard", routeComplianceHandler.GetComplianceDashboard)
			routeCompliance.GET("/report", routeComplianceHandler.GetComplianceReport)
		}

		// Inventory visibility routes (Manufacturing industry)
		inventory := api.Group("/inventory")
		{
			inventory.GET("/dashboard", inventoryHandler.GetInventoryDashboard)
			inventory.GET("/alerts", inventoryHandler.GetInventoryAlerts)
			inventory.POST("/movements", inventoryHandler.RecordInventoryMovement)
			inventory.PUT("/:inventory_id", inventoryHandler.UpdateInventory)
			inventory.GET("/:inventory_id/movements", inventoryHandler.GetInventoryMovements)
			inventory.POST("/products", inventoryHandler.CreateProduct)
			inventory.GET("/products", inventoryHandler.GetProducts)
			inventory.PUT("/alerts/:alert_id/resolve", inventoryHandler.ResolveInventoryAlert)
		}

		// Routing proxy routes (to avoid CORS issues with OLA Maps)
		routing := api.Group("/routing")
		{
			routing.GET("/directions", routingHandler.GetDirections)
			routing.OPTIONS("/directions", routingHandler.GetDirections)
			routing.GET("/distance-matrix", routingHandler.GetDistanceMatrix)
			routing.OPTIONS("/distance-matrix", routingHandler.GetDistanceMatrix)
		}

		// Delivery assignment routes with geofencing
		deliveryAssignments := api.Group("/delivery-assignments")
		{
			deliveryAssignments.POST("", deliveryAssignmentHandler.CreateAssignment)
			deliveryAssignments.GET("/task-types", deliveryAssignmentHandler.GetTaskTypes)
			deliveryAssignments.GET("/agent/:agent_id", deliveryAssignmentHandler.GetAssignmentsByAgent)
			deliveryAssignments.GET("/:assignment_id", deliveryAssignmentHandler.GetAssignmentByID)
			deliveryAssignments.PATCH("/:assignment_id/status", deliveryAssignmentHandler.UpdateAssignmentStatus)
			deliveryAssignments.GET("/agent/:agent_id/geofence-status", deliveryAssignmentHandler.CheckGeofenceStatus)
		}
	}

	// Handle 404
	router.NoRoute(middleware.NotFoundHandler())
	router.NoMethod(middleware.MethodNotAllowedHandler())

	logrus.Info("Router configured successfully")
	return router
}

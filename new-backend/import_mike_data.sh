#!/bin/bash

# Import GPS data for <PERSON> from CSV files
# - gpx_data_2.csv for YESTERDAY
# - gpx_data_4.csv for TODAY

set -e

# Database configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="hgcgh"
DB_PASSWORD=""
DB_NAME="delivery_tracking"

# <PERSON>'s UUID
MIKE_DRIVER_ID="3b35ff6d-d482-4cb5-bbc1-ed78774c59b2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting GPS data import for Mike <PERSON>${NC}"
echo "=" | tr '\n' '=' | head -c 50; echo

# Function to execute SQL
execute_sql() {
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$1"
}

# Function to import CSV data
import_csv_data() {
    local csv_file="$1"
    local date_offset="$2"  # 0 for today, -1 for yesterday
    local description="$3"
    
    echo -e "${YELLOW}📁 Processing $csv_file for $description${NC}"
    
    if [ ! -f "$csv_file" ]; then
        echo -e "${RED}❌ File not found: $csv_file${NC}"
        return 1
    fi
    
    # Count lines (excluding header)
    local total_lines=$(tail -n +2 "$csv_file" | wc -l | tr -d ' ')
    echo -e "${BLUE}📊 Total GPS points: $total_lines${NC}"
    
    # Create temporary SQL file
    local temp_sql="/tmp/import_mike_$(date +%s).sql"
    
    echo "-- Import GPS data for Mike Driver ($description)" > "$temp_sql"
    echo "BEGIN;" >> "$temp_sql"
    
    # Calculate time interval (spread across 24 hours)
    local time_interval_seconds=$(echo "scale=2; 86400 / $total_lines" | bc)
    echo -e "${BLUE}⏱️ Time interval: ${time_interval_seconds} seconds between points${NC}"
    
    # Process CSV and generate SQL
    local counter=0
    tail -n +2 "$csv_file" | while IFS=',' read -r latitude longitude elevation time; do
        # Skip empty lines
        if [ -z "$latitude" ] || [ -z "$longitude" ]; then
            continue
        fi
        
        # Calculate timestamp
        local seconds_offset=$(echo "$counter * $time_interval_seconds" | bc)
        local timestamp_sql="(CURRENT_DATE + INTERVAL '$date_offset days' + INTERVAL '$seconds_offset seconds')"
        
        # Generate varying data
        local speed=$((10 + (counter % 20)))
        local heading=$((counter * 5 % 360))
        local accuracy=$((3 + (counter % 5)))
        local altitude=$((100 + (counter % 50)))
        local battery=$((100 - (counter % 80)))
        if [ $battery -lt 20 ]; then battery=20; fi
        
        # Generate INSERT statement
        echo "INSERT INTO trackingupdates (agent_id, role, trip_id, geom, timestamp, speed_m_s, heading_deg, accuracy_m, altitude_m, battery_pct, source) VALUES ('$MIKE_DRIVER_ID', 'delivery', NULL, ST_GeomFromText('POINT($longitude $latitude)', 4326), $timestamp_sql, $speed, $heading, $accuracy, $altitude, $battery, 'mobile_sdk');" >> "$temp_sql"
        
        counter=$((counter + 1))
        
        # Show progress every 5000 records
        if [ $((counter % 5000)) -eq 0 ]; then
            echo -e "${GREEN}✅ Processed $counter records...${NC}"
        fi
    done
    
    echo "COMMIT;" >> "$temp_sql"
    
    echo -e "${BLUE}🔄 Executing SQL import...${NC}"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$temp_sql"
    
    # Clean up
    rm "$temp_sql"
    
    echo -e "${GREEN}✅ Successfully imported data for $description${NC}"
    echo
}

# Main execution
echo -e "${BLUE}📅 Calculating dates...${NC}"
echo -e "${BLUE}📅 Today: $(date '+%Y-%m-%d')${NC}"
echo -e "${BLUE}📅 Yesterday: $(date -d 'yesterday' '+%Y-%m-%d' 2>/dev/null || date -v-1d '+%Y-%m-%d')${NC}"
echo

# Import yesterday's data (gpx_data_2.csv)
echo -e "${YELLOW}1️⃣ Importing YESTERDAY data (gpx_data_2.csv)${NC}"
import_csv_data "/Users/<USER>/Downloads/delivery-tracking/gpx_data_2.csv" "-1" "YESTERDAY"

# Import today's data (gpx_data_4.csv)
echo -e "${YELLOW}2️⃣ Importing TODAY data (gpx_data_4.csv)${NC}"
import_csv_data "/Users/<USER>/Downloads/delivery-tracking/gpx_data_4.csv" "0" "TODAY"

echo "=" | tr '\n' '=' | head -c 50; echo
echo -e "${GREEN}🎉 Import completed successfully!${NC}"

# Verify the import
echo -e "${BLUE}🔍 Verifying import...${NC}"
total_records=$(execute_sql "SELECT COUNT(*) FROM trackingupdates WHERE agent_id = '$MIKE_DRIVER_ID';" | grep -E '^[[:space:]]*[0-9]+[[:space:]]*$' | tr -d ' ')
echo -e "${GREEN}✅ Database verification: $total_records records found for Mike Driver${NC}"

# Show date distribution
echo -e "${BLUE}📊 Date distribution:${NC}"
execute_sql "SELECT DATE(timestamp) as date, COUNT(*) as records FROM trackingupdates WHERE agent_id = '$MIKE_DRIVER_ID' GROUP BY DATE(timestamp) ORDER BY date;"

echo -e "${GREEN}🎉 All done!${NC}"

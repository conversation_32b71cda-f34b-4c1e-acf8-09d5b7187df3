package repository

import (
	"database/sql"
	"fmt"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type UserRepository struct {
	db *database.DB
}

func NewUserRepository(db *database.DB) *UserRepository {
	return &UserRepository{db: db}
}

// <PERSON><PERSON><PERSON><PERSON> creates a new user
func (r *UserRepository) CreateUser(req *models.CreateUserRequest) (*models.User, error) {
	user := &models.User{
		ID:       uuid.New(),
		Name:     req.Name,
		Email:    req.Email,
		Phone:    req.Phone,
		UserType: req.UserType,
		IsActive: true,
	}

	query := `
		INSERT INTO users (id, name, email, phone, user_type, is_active)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING created_at`

	err := r.db.QueryRow(query, user.ID, user.Name, user.Email, user.Phone, user.UserType, user.IsActive).
		Scan(&user.CreatedAt)
	if err != nil {
		logrus.WithError(err).Error("Failed to create user")
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// GetUserByID retrieves a user by ID
func (r *UserRepository) GetUserByID(id uuid.UUID) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT id, name, email, phone, user_type, created_at, is_active
		FROM users
		WHERE id = $1`

	err := r.db.QueryRow(query, id).Scan(
		&user.ID, &user.Name, &user.Email, &user.Phone,
		&user.UserType, &user.CreatedAt, &user.IsActive,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		logrus.WithError(err).Error("Failed to get user by ID")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// GetUserByEmail retrieves a user by email
func (r *UserRepository) GetUserByEmail(email string) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT id, name, email, phone, user_type, created_at, is_active
		FROM users
		WHERE email = $1`

	err := r.db.QueryRow(query, email).Scan(
		&user.ID, &user.Name, &user.Email, &user.Phone,
		&user.UserType, &user.CreatedAt, &user.IsActive,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		logrus.WithError(err).Error("Failed to get user by email")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// UpdateUser updates user information
func (r *UserRepository) UpdateUser(id uuid.UUID, req *models.UpdateUserRequest) (*models.User, error) {
	// Build dynamic query based on provided fields
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if req.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}
	if req.Email != nil {
		setParts = append(setParts, fmt.Sprintf("email = $%d", argIndex))
		args = append(args, *req.Email)
		argIndex++
	}
	if req.Phone != nil {
		setParts = append(setParts, fmt.Sprintf("phone = $%d", argIndex))
		args = append(args, *req.Phone)
		argIndex++
	}
	if req.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if len(setParts) == 0 {
		return r.GetUserByID(id) // No updates, return current user
	}

	setClause := ""
	for i, part := range setParts {
		if i > 0 {
			setClause += ", "
		}
		setClause += part
	}

	query := fmt.Sprintf(`
		UPDATE Users
		SET %s
		WHERE id = $%d
		RETURNING id, name, email, phone, user_type, created_at, is_active`,
		setClause,
		argIndex,
	)

	args = append(args, id)

	user := &models.User{}
	err := r.db.QueryRow(query, args...).Scan(
		&user.ID, &user.Name, &user.Email, &user.Phone,
		&user.UserType, &user.CreatedAt, &user.IsActive,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		logrus.WithError(err).Error("Failed to update user")
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

// DeleteUser soft deletes a user (sets is_active to false)
func (r *UserRepository) DeleteUser(id uuid.UUID) error {
	query := `UPDATE Users SET is_active = false WHERE id = $1`
	
	result, err := r.db.Exec(query, id)
	if err != nil {
		logrus.WithError(err).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// GetUsersByType retrieves users by type
func (r *UserRepository) GetUsersByType(userType string) ([]*models.User, error) {
	query := `
		SELECT id, name, email, phone, user_type, created_at, is_active
		FROM users
		WHERE user_type = $1 AND is_active = true
		ORDER BY created_at DESC`

	rows, err := r.db.Query(query, userType)
	if err != nil {
		logrus.WithError(err).Error("Failed to get users by type")
		return nil, fmt.Errorf("failed to get users: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		err := rows.Scan(
			&user.ID, &user.Name, &user.Email, &user.Phone,
			&user.UserType, &user.CreatedAt, &user.IsActive,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan user row")
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating user rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return users, nil
}

// CheckEmailExists checks if an email already exists
func (r *UserRepository) CheckEmailExists(email string) (bool, error) {
	var count int
	query := `SELECT COUNT(*) FROM users WHERE email = $1`
	
	err := r.db.QueryRow(query, email).Scan(&count)
	if err != nil {
		logrus.WithError(err).Error("Failed to check email existence")
		return false, fmt.Errorf("failed to check email: %w", err)
	}

	return count > 0, nil
}

// CheckPhoneExists checks if a phone number already exists
func (r *UserRepository) CheckPhoneExists(phone string) (bool, error) {
	var count int
	query := `SELECT COUNT(*) FROM users WHERE phone = $1`
	
	err := r.db.QueryRow(query, phone).Scan(&count)
	if err != nil {
		logrus.WithError(err).Error("Failed to check phone existence")
		return false, fmt.Errorf("failed to check phone: %w", err)
	}

	return count > 0, nil
}

// GetCustomers retrieves all customers
func (r *UserRepository) GetCustomers() ([]*models.User, error) {
	query := `
		SELECT id, name, email, phone, user_type, created_at, is_active
		FROM users
		WHERE user_type = 'customer' AND is_active = true
		ORDER BY name ASC`

	rows, err := r.db.Query(query)
	if err != nil {
		logrus.WithError(err).Error("Failed to get customers")
		return nil, fmt.Errorf("failed to get customers: %w", err)
	}
	defer rows.Close()

	var customers []*models.User
	for rows.Next() {
		user := &models.User{}

		err := rows.Scan(
			&user.ID,
			&user.Name,
			&user.Email,
			&user.Phone,
			&user.UserType,
			&user.CreatedAt,
			&user.IsActive,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan customer row")
			continue
		}

		customers = append(customers, user)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating customer rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return customers, nil
}

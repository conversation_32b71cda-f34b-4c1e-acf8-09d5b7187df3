package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type RouteComplianceRepository struct {
	db *database.DB
}

func NewRouteComplianceRepository(db *database.DB) *RouteComplianceRepository {
	return &RouteComplianceRepository{db: db}
}

// CreateGeofence creates a new geofence
func (r *RouteComplianceRepository) CreateGeofence(geofence *models.Geofence) error {
	geofence.ID = uuid.New()
	geofence.CreatedAt = time.Now()
	geofence.UpdatedAt = time.Now()

	// Convert boundary coordinates to PostGIS polygon
	boundaryJSON, err := json.Marshal(geofence.Boundary)
	if err != nil {
		return fmt.Errorf("failed to marshal boundary: %w", err)
	}

	// Convert allowed hours and restrictions to JSO<PERSON>
	var allowedHoursJSON, restrictionsJSON sql.NullString
	if geofence.AllowedHours != nil {
		data, err := json.Marshal(geofence.AllowedHours)
		if err != nil {
			return fmt.Errorf("failed to marshal allowed hours: %w", err)
		}
		allowedHoursJSON = sql.NullString{String: string(data), Valid: true}
	}

	if geofence.Restrictions != nil {
		data, err := json.Marshal(geofence.Restrictions)
		if err != nil {
			return fmt.Errorf("failed to marshal restrictions: %w", err)
		}
		restrictionsJSON = sql.NullString{String: string(data), Valid: true}
	}

	query := `
		INSERT INTO Geofences (
			id, name, description, geofence_type, boundary, is_active,
			speed_limit_kmh, allowed_hours, restrictions, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, ST_GeogFromGeoJSON($5), $6, $7, $8, $9, $10, $11
		)`

	_, err = r.db.Exec(query,
		geofence.ID, geofence.Name, geofence.Description, geofence.GeofenceType,
		string(boundaryJSON), geofence.IsActive, geofence.SpeedLimitKmh,
		allowedHoursJSON, restrictionsJSON, geofence.CreatedAt, geofence.UpdatedAt,
	)

	if err != nil {
		logrus.WithError(err).Error("Failed to create geofence")
		return fmt.Errorf("failed to create geofence: %w", err)
	}

	logrus.WithField("geofence_id", geofence.ID).Info("Geofence created successfully")
	return nil
}

// GetGeofences retrieves geofences with optional filters
func (r *RouteComplianceRepository) GetGeofences(activeOnly bool, geofenceType string) ([]*models.Geofence, error) {
	query := `
		SELECT 
			id, name, description, geofence_type, ST_AsGeoJSON(boundary) as boundary,
			is_active, speed_limit_kmh, allowed_hours, restrictions,
			created_at, updated_at
		FROM Geofences
		WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	if activeOnly {
		query += fmt.Sprintf(" AND is_active = $%d", argIndex)
		args = append(args, true)
		argIndex++
	}

	if geofenceType != "" {
		query += fmt.Sprintf(" AND geofence_type = $%d", argIndex)
		args = append(args, geofenceType)
		argIndex++
	}

	query += " ORDER BY created_at DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get geofences")
		return nil, fmt.Errorf("failed to get geofences: %w", err)
	}
	defer rows.Close()

	var geofences []*models.Geofence
	for rows.Next() {
		geofence := &models.Geofence{}
		var boundaryJSON string
		var allowedHoursJSON, restrictionsJSON sql.NullString

		err := rows.Scan(
			&geofence.ID, &geofence.Name, &geofence.Description, &geofence.GeofenceType,
			&boundaryJSON, &geofence.IsActive, &geofence.SpeedLimitKmh,
			&allowedHoursJSON, &restrictionsJSON, &geofence.CreatedAt, &geofence.UpdatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan geofence row")
			continue
		}

		// Parse boundary JSON
		if err := json.Unmarshal([]byte(boundaryJSON), &geofence.Boundary); err != nil {
			logrus.WithError(err).Warn("Failed to parse boundary JSON")
		}

		// Parse allowed hours JSON
		if allowedHoursJSON.Valid {
			if err := json.Unmarshal([]byte(allowedHoursJSON.String), &geofence.AllowedHours); err != nil {
				logrus.WithError(err).Warn("Failed to parse allowed hours JSON")
			}
		}

		// Parse restrictions JSON
		if restrictionsJSON.Valid {
			if err := json.Unmarshal([]byte(restrictionsJSON.String), &geofence.Restrictions); err != nil {
				logrus.WithError(err).Warn("Failed to parse restrictions JSON")
			}
		}

		geofences = append(geofences, geofence)
	}

	return geofences, nil
}

// CreatePlannedRoute creates a planned route
func (r *RouteComplianceRepository) CreatePlannedRoute(route *models.PlannedRoute) error {
	route.ID = uuid.New()
	route.CreatedAt = time.Now()
	route.UpdatedAt = time.Now()

	// Convert route points to PostGIS linestring
	routePointsJSON, err := json.Marshal(route.RoutePoints)
	if err != nil {
		return fmt.Errorf("failed to marshal route points: %w", err)
	}

	// Convert waypoints to JSON
	var waypointsJSON sql.NullString
	if route.Waypoints != nil && len(route.Waypoints) > 0 {
		data, err := json.Marshal(route.Waypoints)
		if err != nil {
			return fmt.Errorf("failed to marshal waypoints: %w", err)
		}
		waypointsJSON = sql.NullString{String: string(data), Valid: true}
	}

	query := `
		INSERT INTO PlannedRoutes (
			id, trip_id, route_points, waypoints, estimated_duration_minutes,
			estimated_distance_km, max_deviation_meters, created_at, updated_at
		) VALUES (
			$1, $2, ST_GeogFromGeoJSON($3), $4, $5, $6, $7, $8, $9
		)`

	_, err = r.db.Exec(query,
		route.ID, route.TripID, string(routePointsJSON), waypointsJSON,
		route.EstimatedDurationMinutes, route.EstimatedDistanceKm,
		route.MaxDeviationMeters, route.CreatedAt, route.UpdatedAt,
	)

	if err != nil {
		logrus.WithError(err).Error("Failed to create planned route")
		return fmt.Errorf("failed to create planned route: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"route_id": route.ID,
		"trip_id":  route.TripID,
	}).Info("Planned route created successfully")

	return nil
}

// GetComplianceViolations retrieves compliance violations with filters
func (r *RouteComplianceRepository) GetComplianceViolations(tripID, agentID *uuid.UUID, eventType, severity string, unresolvedOnly bool) ([]*models.RouteComplianceViolation, error) {
	query := `
		SELECT 
			rc.id, rc.trip_id, rc.agent_id, rc.event_type, rc.geofence_id,
			ST_X(rc.location::geometry) as longitude, ST_Y(rc.location::geometry) as latitude,
			rc.speed_kmh, rc.deviation_distance_m, rc.severity, rc.description,
			rc.resolved, rc.resolved_by, rc.resolved_at, rc.resolution_note,
			rc.created_at, u.name as agent_name, g.name as geofence_name
		FROM RouteCompliance rc
		LEFT JOIN Users u ON rc.agent_id = u.id
		LEFT JOIN Geofences g ON rc.geofence_id = g.id
		WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	if tripID != nil {
		query += fmt.Sprintf(" AND rc.trip_id = $%d", argIndex)
		args = append(args, *tripID)
		argIndex++
	}

	if agentID != nil {
		query += fmt.Sprintf(" AND rc.agent_id = $%d", argIndex)
		args = append(args, *agentID)
		argIndex++
	}

	if eventType != "" {
		query += fmt.Sprintf(" AND rc.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	if severity != "" {
		query += fmt.Sprintf(" AND rc.severity = $%d", argIndex)
		args = append(args, severity)
		argIndex++
	}

	if unresolvedOnly {
		query += " AND rc.resolved = FALSE"
	}

	query += " ORDER BY rc.created_at DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get compliance violations")
		return nil, fmt.Errorf("failed to get compliance violations: %w", err)
	}
	defer rows.Close()

	var violations []*models.RouteComplianceViolation
	for rows.Next() {
		violation := &models.RouteComplianceViolation{}
		var longitude, latitude float64
		var agentName, geofenceName sql.NullString

		err := rows.Scan(
			&violation.ID, &violation.TripID, &violation.AgentID, &violation.EventType,
			&violation.GeofenceID, &longitude, &latitude, &violation.SpeedKmh,
			&violation.DeviationDistanceM, &violation.Severity, &violation.Description,
			&violation.Resolved, &violation.ResolvedBy, &violation.ResolvedAt,
			&violation.ResolutionNote, &violation.CreatedAt, &agentName, &geofenceName,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan violation row")
			continue
		}

		violation.Location = models.Point{Latitude: latitude, Longitude: longitude}

		if agentName.Valid {
			violation.AgentName = agentName.String
		}

		if geofenceName.Valid {
			violation.GeofenceName = geofenceName.String
		}

		violations = append(violations, violation)
	}

	return violations, nil
}

// ResolveViolation marks a violation as resolved
func (r *RouteComplianceRepository) ResolveViolation(violationID uuid.UUID, resolutionNote string) error {
	query := `
		UPDATE RouteCompliance 
		SET resolved = TRUE, resolved_at = NOW(), resolution_note = $2
		WHERE id = $1`

	result, err := r.db.Exec(query, violationID, resolutionNote)
	if err != nil {
		logrus.WithError(err).Error("Failed to resolve violation")
		return fmt.Errorf("failed to resolve violation: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("violation not found")
	}

	logrus.WithField("violation_id", violationID).Info("Violation resolved successfully")
	return nil
}

// GetComplianceDashboard retrieves compliance dashboard data
func (r *RouteComplianceRepository) GetComplianceDashboard(agentID *uuid.UUID, days int) (map[string]interface{}, error) {
	// Get compliance summary from the dashboard view
	query := `
		SELECT 
			trip_id, customer_id, agent_id, agent_name, trip_status,
			requires_compliance, compliance_score, route_deviation_count,
			total_violations, critical_violations, high_violations,
			unresolved_violations, last_violation_time
		FROM route_compliance_dashboard`

	args := []interface{}{}
	if agentID != nil {
		query += " WHERE agent_id = $1"
		args = append(args, *agentID)
	}

	query += " ORDER BY last_violation_time DESC NULLS LAST"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get compliance dashboard data")
		return nil, fmt.Errorf("failed to get compliance dashboard data: %w", err)
	}
	defer rows.Close()

	var trips []map[string]interface{}
	totalTrips := 0
	totalViolations := 0
	criticalViolations := 0
	unresolvedViolations := 0

	for rows.Next() {
		var tripID, customerID, agentIDStr, agentName, tripStatus string
		var requiresCompliance bool
		var complianceScore sql.NullFloat64
		var routeDeviationCount, tripTotalViolations, tripCriticalViolations, tripHighViolations, tripUnresolvedViolations int
		var lastViolationTime sql.NullTime

		err := rows.Scan(
			&tripID, &customerID, &agentIDStr, &agentName, &tripStatus,
			&requiresCompliance, &complianceScore, &routeDeviationCount,
			&tripTotalViolations, &tripCriticalViolations, &tripHighViolations,
			&tripUnresolvedViolations, &lastViolationTime,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan dashboard row")
			continue
		}

		trip := map[string]interface{}{
			"trip_id":               tripID,
			"customer_id":           customerID,
			"agent_id":              agentIDStr,
			"agent_name":            agentName,
			"trip_status":           tripStatus,
			"requires_compliance":   requiresCompliance,
			"route_deviation_count": routeDeviationCount,
			"total_violations":      tripTotalViolations,
			"critical_violations":   tripCriticalViolations,
			"high_violations":       tripHighViolations,
			"unresolved_violations": tripUnresolvedViolations,
		}

		if complianceScore.Valid {
			trip["compliance_score"] = complianceScore.Float64
		}

		if lastViolationTime.Valid {
			trip["last_violation_time"] = lastViolationTime.Time
		}

		trips = append(trips, trip)
		totalTrips++
		totalViolations += tripTotalViolations
		criticalViolations += tripCriticalViolations
		unresolvedViolations += tripUnresolvedViolations
	}

	dashboard := map[string]interface{}{
		"summary": map[string]interface{}{
			"total_compliance_trips": totalTrips,
			"total_violations":       totalViolations,
			"critical_violations":    criticalViolations,
			"unresolved_violations":  unresolvedViolations,
		},
		"trips":        trips,
		"last_updated": time.Now(),
	}

	return dashboard, nil
}

// UpdateGeofence updates an existing geofence
func (r *RouteComplianceRepository) UpdateGeofence(geofence *models.Geofence) error {
	geofence.UpdatedAt = time.Now()

	// Convert boundary coordinates to PostGIS polygon
	boundaryJSON, err := json.Marshal(geofence.Boundary)
	if err != nil {
		return fmt.Errorf("failed to marshal boundary: %w", err)
	}

	// Convert allowed hours and restrictions to JSON
	var allowedHoursJSON, restrictionsJSON sql.NullString
	if geofence.AllowedHours != nil {
		data, err := json.Marshal(geofence.AllowedHours)
		if err != nil {
			return fmt.Errorf("failed to marshal allowed hours: %w", err)
		}
		allowedHoursJSON = sql.NullString{String: string(data), Valid: true}
	}

	if geofence.Restrictions != nil {
		data, err := json.Marshal(geofence.Restrictions)
		if err != nil {
			return fmt.Errorf("failed to marshal restrictions: %w", err)
		}
		restrictionsJSON = sql.NullString{String: string(data), Valid: true}
	}

	query := `
		UPDATE Geofences SET
			name = $2, description = $3, geofence_type = $4,
			boundary = ST_GeogFromGeoJSON($5), speed_limit_kmh = $6,
			allowed_hours = $7, restrictions = $8, updated_at = $9
		WHERE id = $1`

	result, err := r.db.Exec(query,
		geofence.ID, geofence.Name, geofence.Description, geofence.GeofenceType,
		string(boundaryJSON), geofence.SpeedLimitKmh,
		allowedHoursJSON, restrictionsJSON, geofence.UpdatedAt,
	)

	if err != nil {
		logrus.WithError(err).Error("Failed to update geofence")
		return fmt.Errorf("failed to update geofence: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("geofence not found")
	}

	return nil
}

// DeleteGeofence deletes a geofence
func (r *RouteComplianceRepository) DeleteGeofence(geofenceID uuid.UUID) error {
	query := `DELETE FROM Geofences WHERE id = $1`

	result, err := r.db.Exec(query, geofenceID)
	if err != nil {
		logrus.WithError(err).Error("Failed to delete geofence")
		return fmt.Errorf("failed to delete geofence: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("geofence not found")
	}

	return nil
}

// GenerateComplianceReport generates a compliance report
func (r *RouteComplianceRepository) GenerateComplianceReport(startDate, endDate time.Time, agentID *uuid.UUID) (map[string]interface{}, error) {
	// Get violation statistics
	query := `
		SELECT
			event_type,
			severity,
			COUNT(*) as count,
			COUNT(CASE WHEN resolved = TRUE THEN 1 END) as resolved_count
		FROM RouteCompliance
		WHERE created_at >= $1 AND created_at <= $2`

	args := []interface{}{startDate, endDate}
	argIndex := 3

	if agentID != nil {
		query += fmt.Sprintf(" AND agent_id = $%d", argIndex)
		args = append(args, *agentID)
		argIndex++
	}

	query += " GROUP BY event_type, severity ORDER BY count DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get violation statistics: %w", err)
	}
	defer rows.Close()

	var violationStats []map[string]interface{}
	totalViolations := 0
	totalResolved := 0

	for rows.Next() {
		var eventType, severity string
		var count, resolvedCount int

		err := rows.Scan(&eventType, &severity, &count, &resolvedCount)
		if err != nil {
			continue
		}

		violationStats = append(violationStats, map[string]interface{}{
			"event_type":     eventType,
			"severity":       severity,
			"count":          count,
			"resolved_count": resolvedCount,
			"resolution_rate": float64(resolvedCount) / float64(count) * 100,
		})

		totalViolations += count
		totalResolved += resolvedCount
	}

	// Get trip compliance scores
	tripQuery := `
		SELECT
			AVG(compliance_score) as avg_compliance_score,
			COUNT(*) as total_trips,
			COUNT(CASE WHEN compliance_score >= 90 THEN 1 END) as excellent_trips,
			COUNT(CASE WHEN compliance_score >= 70 AND compliance_score < 90 THEN 1 END) as good_trips,
			COUNT(CASE WHEN compliance_score < 70 THEN 1 END) as poor_trips
		FROM Trips
		WHERE requires_compliance = TRUE
		  AND created_at >= $1 AND created_at <= $2
		  AND compliance_score IS NOT NULL`

	tripArgs := []interface{}{startDate, endDate}
	if agentID != nil {
		tripQuery += " AND agent_id = $3"
		tripArgs = append(tripArgs, *agentID)
	}

	var avgComplianceScore sql.NullFloat64
	var totalTrips, excellentTrips, goodTrips, poorTrips int

	err = r.db.QueryRow(tripQuery, tripArgs...).Scan(
		&avgComplianceScore, &totalTrips, &excellentTrips, &goodTrips, &poorTrips,
	)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to get trip compliance statistics: %w", err)
	}

	report := map[string]interface{}{
		"period": map[string]interface{}{
			"start_date": startDate,
			"end_date":   endDate,
		},
		"summary": map[string]interface{}{
			"total_violations":    totalViolations,
			"total_resolved":      totalResolved,
			"resolution_rate":     float64(totalResolved) / float64(totalViolations) * 100,
			"total_trips":         totalTrips,
			"avg_compliance_score": avgComplianceScore.Float64,
		},
		"trip_performance": map[string]interface{}{
			"excellent_trips": excellentTrips, // >= 90%
			"good_trips":      goodTrips,      // 70-89%
			"poor_trips":      poorTrips,      // < 70%
		},
		"violation_breakdown": violationStats,
		"generated_at":        time.Now(),
	}

	if agentID != nil {
		report["agent_id"] = agentID.String()
	}

	return report, nil
}

package repository

import (
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"
)

type DeliveryAssignmentRepository struct {
	db *database.DB
}

func NewDeliveryAssignmentRepository(db *database.DB) *DeliveryAssignmentRepository {
	return &DeliveryAssignmentRepository{db: db}
}

// CreateAssignment creates a new delivery assignment with automatic geofencing
func (r *DeliveryAssignmentRepository) CreateAssignment(req models.CreateDeliveryAssignmentRequest) (*models.DeliveryAssignment, error) {
	// First create a trip for this assignment
	tripID := uuid.New()
	
	// Create trip
	tripQuery := `
		INSERT INTO trips (trip_id, trip_type, customer_id, agent_id, pickup_location, drop_location, status, delivery_fee, created_at)
		VALUES ($1, 'delivery', $2, $3, $4, $4, 'requested', $5, NOW())
	`
	
	_, err := r.db.Exec(tripQuery, tripID, req.CustomerID, req.AgentID, 
		fmt.Sprintf("POINT(%f %f)", req.TargetLocation.Longitude, req.TargetLocation.Latitude),
		req.DeliveryFee)
	if err != nil {
		return nil, fmt.Errorf("failed to create trip: %w", err)
	}

	// Create delivery assignment
	assignmentID := uuid.New()
	query := `
		INSERT INTO delivery_assignments (
			id, trip_id, agent_id, customer_id, task_type, task_description,
			target_location, geofence_radius, status, scheduled_date, notes,
			item_details, delivery_fee, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, 
			ST_GeomFromText($7, 4326), $8, $9, $10, $11,
			$12, $13, NOW(), NOW()
		) RETURNING id, created_at, updated_at
	`

	var assignment models.DeliveryAssignment
	err = r.db.QueryRow(query,
		assignmentID, tripID, req.AgentID, req.CustomerID, req.TaskType, req.TaskDescription,
		fmt.Sprintf("POINT(%f %f)", req.TargetLocation.Longitude, req.TargetLocation.Latitude),
		req.GeofenceRadius, models.TripStatusRequested, req.ScheduledDate, req.Notes,
		req.ItemDetails, req.DeliveryFee,
	).Scan(&assignment.ID, &assignment.CreatedAt, &assignment.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to create delivery assignment: %w", err)
	}

	// Fill in the assignment details
	assignment.TripID = tripID
	assignment.AgentID = req.AgentID
	assignment.CustomerID = req.CustomerID
	assignment.TaskType = req.TaskType
	assignment.TaskDescription = req.TaskDescription
	assignment.TargetLocation = req.TargetLocation
	assignment.GeofenceRadius = req.GeofenceRadius
	assignment.Status = models.TripStatusRequested
	assignment.ScheduledDate = req.ScheduledDate
	assignment.Notes = req.Notes
	assignment.ItemDetails = req.ItemDetails
	assignment.DeliveryFee = req.DeliveryFee

	logrus.WithFields(logrus.Fields{
		"assignment_id": assignment.ID,
		"trip_id":      tripID,
		"agent_id":     req.AgentID,
		"task_type":    req.TaskType,
		"geofence_radius": req.GeofenceRadius,
	}).Info("Created delivery assignment with automatic geofencing")

	return &assignment, nil
}

// GetAssignmentsByAgent retrieves all assignments for a specific agent
func (r *DeliveryAssignmentRepository) GetAssignmentsByAgent(agentID uuid.UUID) ([]models.DeliveryAssignment, error) {
	query := `
		SELECT 
			da.id, da.trip_id, da.agent_id, da.customer_id, da.task_type, da.task_description,
			ST_X(da.target_location) as longitude, ST_Y(da.target_location) as latitude,
			da.geofence_radius, da.status, da.scheduled_date, da.completed_at,
			da.notes, da.item_details, da.delivery_fee, da.created_at, da.updated_at,
			c.name as customer_name, c.phone as customer_phone,
			a.name as agent_name
		FROM delivery_assignments da
		JOIN users c ON da.customer_id = c.id
		JOIN users a ON da.agent_id = a.id
		WHERE da.agent_id = $1
		ORDER BY da.created_at DESC
	`

	rows, err := r.db.Query(query, agentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get assignments by agent: %w", err)
	}
	defer rows.Close()

	var assignments []models.DeliveryAssignment
	for rows.Next() {
		var assignment models.DeliveryAssignment
		var longitude, latitude float64

		err := rows.Scan(
			&assignment.ID, &assignment.TripID, &assignment.AgentID, &assignment.CustomerID,
			&assignment.TaskType, &assignment.TaskDescription, &longitude, &latitude,
			&assignment.GeofenceRadius, &assignment.Status, &assignment.ScheduledDate,
			&assignment.CompletedAt, &assignment.Notes, &assignment.ItemDetails,
			&assignment.DeliveryFee, &assignment.CreatedAt, &assignment.UpdatedAt,
			&assignment.CustomerName, &assignment.CustomerPhone, &assignment.AgentName,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan assignment: %w", err)
		}

		assignment.TargetLocation = models.Point{
			Longitude: longitude,
			Latitude:  latitude,
		}

		assignments = append(assignments, assignment)
	}

	return assignments, nil
}

// GetAssignmentByID retrieves a specific assignment by ID
func (r *DeliveryAssignmentRepository) GetAssignmentByID(assignmentID uuid.UUID) (*models.DeliveryAssignment, error) {
	query := `
		SELECT 
			da.id, da.trip_id, da.agent_id, da.customer_id, da.task_type, da.task_description,
			ST_X(da.target_location) as longitude, ST_Y(da.target_location) as latitude,
			da.geofence_radius, da.status, da.scheduled_date, da.completed_at,
			da.notes, da.item_details, da.delivery_fee, da.created_at, da.updated_at,
			c.name as customer_name, c.phone as customer_phone,
			a.name as agent_name
		FROM delivery_assignments da
		JOIN users c ON da.customer_id = c.id
		JOIN users a ON da.agent_id = a.id
		WHERE da.id = $1
	`

	var assignment models.DeliveryAssignment
	var longitude, latitude float64

	err := r.db.QueryRow(query, assignmentID).Scan(
		&assignment.ID, &assignment.TripID, &assignment.AgentID, &assignment.CustomerID,
		&assignment.TaskType, &assignment.TaskDescription, &longitude, &latitude,
		&assignment.GeofenceRadius, &assignment.Status, &assignment.ScheduledDate,
		&assignment.CompletedAt, &assignment.Notes, &assignment.ItemDetails,
		&assignment.DeliveryFee, &assignment.CreatedAt, &assignment.UpdatedAt,
		&assignment.CustomerName, &assignment.CustomerPhone, &assignment.AgentName,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("assignment not found")
		}
		return nil, fmt.Errorf("failed to get assignment: %w", err)
	}

	assignment.TargetLocation = models.Point{
		Longitude: longitude,
		Latitude:  latitude,
	}

	return &assignment, nil
}

// UpdateAssignmentStatus updates the status of a delivery assignment
func (r *DeliveryAssignmentRepository) UpdateAssignmentStatus(assignmentID uuid.UUID, status models.TripStatus) error {
	query := `
		UPDATE delivery_assignments 
		SET status = $1, updated_at = NOW()
		WHERE id = $2
	`

	result, err := r.db.Exec(query, status, assignmentID)
	if err != nil {
		return fmt.Errorf("failed to update assignment status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("assignment not found")
	}

	logrus.WithFields(logrus.Fields{
		"assignment_id": assignmentID,
		"status":       status,
	}).Info("Updated delivery assignment status")

	return nil
}

// CheckGeofenceStatus checks if an agent is within the geofence of their assignments
func (r *DeliveryAssignmentRepository) CheckGeofenceStatus(agentID uuid.UUID, currentLocation models.Point) ([]models.DeliveryAssignment, error) {
	query := `
		SELECT 
			da.id, da.trip_id, da.agent_id, da.customer_id, da.task_type, da.task_description,
			ST_X(da.target_location) as longitude, ST_Y(da.target_location) as latitude,
			da.geofence_radius, da.status, da.scheduled_date, da.completed_at,
			da.notes, da.item_details, da.delivery_fee, da.created_at, da.updated_at,
			c.name as customer_name, c.phone as customer_phone,
			a.name as agent_name,
			is_within_geofence(
				ST_GeomFromText($2, 4326), 
				da.target_location, 
				da.geofence_radius
			) as is_within_geofence,
			calculate_distance_meters(
				ST_GeomFromText($2, 4326), 
				da.target_location
			) as distance_to_target
		FROM delivery_assignments da
		JOIN users c ON da.customer_id = c.id
		JOIN users a ON da.agent_id = a.id
		WHERE da.agent_id = $1
		AND da.status IN ('accepted', 'in_transit', 'out_for_delivery')
		ORDER BY distance_to_target ASC
	`

	currentLocationWKT := fmt.Sprintf("POINT(%f %f)", currentLocation.Longitude, currentLocation.Latitude)
	
	rows, err := r.db.Query(query, agentID, currentLocationWKT)
	if err != nil {
		return nil, fmt.Errorf("failed to check geofence status: %w", err)
	}
	defer rows.Close()

	var assignments []models.DeliveryAssignment
	for rows.Next() {
		var assignment models.DeliveryAssignment
		var longitude, latitude float64
		var distanceToTarget float64

		err := rows.Scan(
			&assignment.ID, &assignment.TripID, &assignment.AgentID, &assignment.CustomerID,
			&assignment.TaskType, &assignment.TaskDescription, &longitude, &latitude,
			&assignment.GeofenceRadius, &assignment.Status, &assignment.ScheduledDate,
			&assignment.CompletedAt, &assignment.Notes, &assignment.ItemDetails,
			&assignment.DeliveryFee, &assignment.CreatedAt, &assignment.UpdatedAt,
			&assignment.CustomerName, &assignment.CustomerPhone, &assignment.AgentName,
			&assignment.IsWithinGeofence, &distanceToTarget,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan assignment with geofence status: %w", err)
		}

		assignment.TargetLocation = models.Point{
			Longitude: longitude,
			Latitude:  latitude,
		}
		assignment.DistanceToTarget = &distanceToTarget

		assignments = append(assignments, assignment)
	}

	return assignments, nil
}

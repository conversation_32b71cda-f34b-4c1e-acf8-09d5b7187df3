package repository

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type TrackingRepository struct {
	db *database.DB
}

func NewTrackingRepository(db *database.DB) *TrackingRepository {
	return &TrackingRepository{db: db}
}

// ProcessLocationPing handles GPS location ping - inserts into TrackingUpdates
// LiveLocations are automatically updated by a database trigger
func (r *TrackingRepository) ProcessLocationPing(req *models.LocationPingRequest) error {
	// Just insert into TrackingUpdates
	// The trigger will handle updating LiveLocations
	insertTrackingQuery := `
		INSERT INTO trackingupdates (
			agent_id, role, trip_id, geom, timestamp, speed_m_s,
			heading_deg, accuracy_m, altitude_m, battery_pct, source
		) VALUES (
			$1, $2, $3, ST_GeomFromText($4, 4326), $5, $6, $7, $8, $9, $10, $11
		)`

	var tripID interface{}
	if req.TripID != nil {
		tripID = *req.TripID
	}

	_, err := r.db.Exec(insertTrackingQuery,
		req.AgentID, req.Role, tripID, req.Location.String(), req.Timestamp,
		req.SpeedMS, req.HeadingDeg, req.AccuracyM, req.AltitudeM, req.BatteryPct, req.Source,
	)
	if err != nil {
		return fmt.Errorf("failed to insert tracking update: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"agent_id": req.AgentID,
		"timestamp": req.Timestamp,
	}).Debug("Inserted tracking update")
	
	return nil
}

// GetLiveLocation retrieves current live location of an agent
func (r *TrackingRepository) GetLiveLocation(agentID uuid.UUID) (*models.LiveLocation, error) {
	location := &models.LiveLocation{}
	query := `
		SELECT 
			l.agent_id, l.role, l.trip_id, 
			ST_X(l.point) as longitude, ST_Y(l.point) as latitude,
			l.timestamp, l.speed_m_s, l.heading_deg, l.accuracy_m, l.altitude_m, 
			l.battery_pct, l.source, l.updated_at,
			u.name, u.simple_id
		FROM LiveLocations l
		INNER JOIN users u ON l.agent_id = u.id
		WHERE l.agent_id = $1`

	var tripID sql.NullString
	var longitude, latitude float64
	var source sql.NullString
	var name sql.NullString
	var simpleID sql.NullString

	err := r.db.QueryRow(query, agentID).Scan(
		&location.AgentID, &location.Role, &tripID,
		&longitude, &latitude,
		&location.Timestamp, &location.SpeedMS, &location.HeadingDeg,
		&location.AccuracyM, &location.AltitudeM, &location.BatteryPct,
		&source, &location.UpdatedAt, &name, &simpleID,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			// Try to get the most recent location from the TrackingUpdates table instead
			return r.getLastTrackingUpdate(agentID)
		}
		logrus.WithError(err).Error("Failed to get live location")
		return nil, fmt.Errorf("failed to get live location: %w", err)
	}

	location.Geom = models.Point{Longitude: longitude, Latitude: latitude}

	if tripID.Valid {
		tripUUID, err := uuid.Parse(tripID.String)
		if err == nil {
			location.TripID = &tripUUID
		}
	}

	if source.Valid {
		sourceStr := source.String
		location.Source = &sourceStr
	}

	// Add user name and simpleID to the location data
	if name.Valid {
		location.Name = name.String
	}

	if simpleID.Valid {
		simpleIDStr := simpleID.String
		location.SimpleID = &simpleIDStr
	}

	return location, nil
}

// getLastTrackingUpdate gets the most recent tracking update for an agent
// This is used as a fallback when there's no entry in LiveLocations
func (r *TrackingRepository) getLastTrackingUpdate(agentID uuid.UUID) (*models.LiveLocation, error) {
	query := `
		SELECT 
			t.agent_id, t.role, t.trip_id,
			ST_X(t.geom::geometry) as longitude, ST_Y(t.geom::geometry) as latitude,
			t.timestamp, 
			COALESCE(t.speed_m_s, 0) as speed_m_s,
			COALESCE(t.heading_deg, 0) as heading_deg,
			t.accuracy_m, t.altitude_m, t.battery_pct, t.source,
			u.name, u.simple_id
		FROM TrackingUpdates t
		INNER JOIN users u ON t.agent_id = u.id
		WHERE t.agent_id = $1
		ORDER BY t.timestamp DESC
		LIMIT 1`

	location := &models.LiveLocation{}
	var tripID sql.NullString
	var longitude, latitude float64
	var speedMS, headingDeg float64
	var source sql.NullString
	var name sql.NullString
	var simpleID sql.NullString

	err := r.db.QueryRow(query, agentID).Scan(
		&location.AgentID, &location.Role, &tripID,
		&longitude, &latitude,
		&location.Timestamp, 
		&speedMS, &headingDeg,
		&location.AccuracyM, &location.AltitudeM, &location.BatteryPct,
		&source, &name, &simpleID,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("live location not found for agent")
		}
		logrus.WithError(err).Error("Failed to get last tracking update")
		return nil, fmt.Errorf("failed to get last tracking update: %w", err)
	}

	location.Geom = models.Point{Longitude: longitude, Latitude: latitude}
	location.SpeedMS = speedMS
	location.HeadingDeg = headingDeg
	location.UpdatedAt = time.Now()

	if tripID.Valid {
		tripUUID, err := uuid.Parse(tripID.String)
		if err == nil {
			location.TripID = &tripUUID
		}
	}

	if source.Valid {
		sourceStr := source.String
		location.Source = &sourceStr
	}

	if name.Valid {
		location.Name = name.String
	}

	if simpleID.Valid {
		simpleIDStr := simpleID.String
		location.SimpleID = &simpleIDStr
	}

	return location, nil
}

// GetTrackingHistory retrieves tracking history for an agent within a date range
func (r *TrackingRepository) GetTrackingHistory(agentID uuid.UUID, startDate, endDate time.Time) ([]*models.TrackingUpdate, error) {
	query := `
		SELECT 
			t.trackpoint_id, t.agent_id, t.role, t.trip_id,
			ST_AsText(t.geom) as geom_text,
			t.timestamp, t.speed_m_s, t.heading_deg, t.accuracy_m, t.altitude_m,
			t.battery_pct, t.is_active, t.source
		FROM trackingupdates t
		WHERE t.agent_id = $1 AND t.timestamp >= $2 AND t.timestamp <= $3
		ORDER BY t.timestamp ASC`

	rows, err := r.db.Query(query, agentID, startDate, endDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get tracking history")
		return nil, fmt.Errorf("failed to get tracking history: %w", err)
	}
	defer rows.Close()

	var trackingUpdates []*models.TrackingUpdate
	for rows.Next() {
		update := &models.TrackingUpdate{}
		var tripID sql.NullString
		var geomText string
		var source sql.NullString

		err := rows.Scan(
			&update.TrackpointID, &update.AgentID, &update.Role, &tripID,
			&geomText,
			&update.Timestamp, &update.SpeedMS, &update.HeadingDeg,
			&update.AccuracyM, &update.AltitudeM, &update.BatteryPct,
			&update.IsActive, &source,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan tracking update row")
			return nil, fmt.Errorf("failed to scan tracking update: %w", err)
		}

		// Parse the WKT geometry text (format: "POINT(longitude latitude)")
		if geomText != "" {
			geomText = strings.TrimPrefix(geomText, "POINT(")
			geomText = strings.TrimSuffix(geomText, ")")
			coords := strings.Split(geomText, " ")
			if len(coords) == 2 {
				longitude, _ := strconv.ParseFloat(coords[0], 64)
				latitude, _ := strconv.ParseFloat(coords[1], 64)
				update.Geom = models.Point{Longitude: longitude, Latitude: latitude}
			}
		}

		if tripID.Valid {
			tripUUID, err := uuid.Parse(tripID.String)
			if err == nil {
				update.TripID = &tripUUID
			}
		}

		if source.Valid {
			sourceEnum := models.Source(source.String)
			update.Source = &sourceEnum
		}

		trackingUpdates = append(trackingUpdates, update)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating tracking update rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return trackingUpdates, nil
}

// GetTrackingHistoryByDate retrieves tracking history for a specific date
func (r *TrackingRepository) GetTrackingHistoryByDate(agentID uuid.UUID, date time.Time) ([]*models.TrackingUpdate, error) {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)
	
	return r.GetTrackingHistory(agentID, startOfDay, endOfDay)
}

// GetActiveAgents retrieves all agents with live location data (ignores time restrictions)
func (r *TrackingRepository) GetActiveAgents(withinMinutes int) ([]*models.LiveLocation, error) {
	query := `
		SELECT
			l.agent_id, l.role, l.trip_id,
			ST_X(l.geom::geometry) as longitude, ST_Y(l.geom::geometry) as latitude,
			l.timestamp, l.speed_m_s, l.heading_deg, l.accuracy_m, l.altitude_m,
			l.battery_pct, l.source, l.updated_at,
			u.name, u.simple_id
		FROM livelocations l
		INNER JOIN users u ON l.agent_id = u.id
		ORDER BY l.updated_at DESC`

	rows, err := r.db.Query(query)
	if err != nil {
		logrus.WithError(err).Error("Failed to get active agents")
		return nil, fmt.Errorf("failed to get active agents: %w", err)
	}
	defer rows.Close()

	var locations []*models.LiveLocation
	for rows.Next() {
		location := &models.LiveLocation{}
		var tripID sql.NullString
		var longitude, latitude float64
		var source sql.NullString
		var name sql.NullString
		var simpleID sql.NullString

		err := rows.Scan(
			&location.AgentID, &location.Role, &tripID,
			&longitude, &latitude,
			&location.Timestamp, &location.SpeedMS, &location.HeadingDeg,
			&location.AccuracyM, &location.AltitudeM, &location.BatteryPct,
			&source, &location.UpdatedAt, &name, &simpleID,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan live location row")
			return nil, fmt.Errorf("failed to scan live location: %w", err)
		}

		location.Geom = models.Point{Longitude: longitude, Latitude: latitude}

		if tripID.Valid {
			tripUUID, err := uuid.Parse(tripID.String)
			if err == nil {
				location.TripID = &tripUUID
			}
		}

		if source.Valid {
			sourceStr := source.String
			location.Source = &sourceStr
		}

		// Add user name and simple_id to the location data
		if name.Valid {
			location.Name = name.String
		}

		if simpleID.Valid {
			simpleIDStr := simpleID.String
			location.SimpleID = &simpleIDStr
		}

		locations = append(locations, location)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating live location rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return locations, nil
}

// GetAgentDeliveryStats retrieves delivery statistics for an agent on a specific date
func (r *TrackingRepository) GetAgentDeliveryStats(agentID uuid.UUID, date time.Time) (map[string]interface{}, error) {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	// Query to get delivery statistics from TripStatusUpdates
	// Since trip_type column doesn't exist yet, we'll assume all trips are deliveries
	query := `
		SELECT
			tsu.status,
			COUNT(*) as count
		FROM tripstatusupdates tsu
		INNER JOIN trips t ON tsu.trip_id = t.trip_id
		WHERE t.agent_id = $1
		  AND tsu.timestamp >= $2
		  AND tsu.timestamp < $3
		GROUP BY tsu.status
		ORDER BY tsu.status`

	rows, err := r.db.Query(query, agentID, startOfDay, endOfDay)
	if err != nil {
		logrus.WithError(err).Error("Failed to get delivery stats")
		return nil, fmt.Errorf("failed to get delivery stats: %w", err)
	}
	defer rows.Close()

	// Initialize stats with zero values
	stats := map[string]interface{}{
		"delivered":     0,
		"failed":        0,
		"rescheduled":   0,
		"in_transit":    0,
		"picked_up":     0,
		"out_for_delivery": 0,
		"accepted":      0,
		"requested":     0,
		"cancelled":     0,
		"returned":      0,
		"total_trips":   0,
	}

	totalTrips := 0
	for rows.Next() {
		var status string
		var count int

		err := rows.Scan(&status, &count)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan delivery stats row")
			continue
		}

		stats[status] = count
		totalTrips += count
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating delivery stats rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	stats["total_trips"] = totalTrips

	// Calculate additional metrics
	delivered := stats["delivered"].(int)
	failed := stats["failed"].(int)

	if totalTrips > 0 {
		stats["success_rate"] = float64(delivered) / float64(totalTrips) * 100
		stats["failure_rate"] = float64(failed) / float64(totalTrips) * 100
	} else {
		stats["success_rate"] = 0.0
		stats["failure_rate"] = 0.0
	}

	return stats, nil
}

// GetAgentsDeliveryCounts retrieves delivery counts for all agents
func (r *TrackingRepository) GetAgentsDeliveryCounts() (map[string]interface{}, error) {
	// Query to get delivery counts for all agents from trips and tripstatusupdates
	query := `
		SELECT
			t.agent_id,
			u.name,
			COALESCE(u.simple_id, '') as simple_id,
			COUNT(DISTINCT t.trip_id) as total_trips,
			COUNT(DISTINCT CASE WHEN tsu.status = 'delivered' THEN t.trip_id END) as delivered_count,
			COUNT(DISTINCT CASE WHEN tsu.status = 'failed' THEN t.trip_id END) as failed_count,
			COUNT(DISTINCT CASE WHEN tsu.status IN ('requested', 'accepted', 'picked_up', 'in_transit', 'out_for_delivery') THEN t.trip_id END) as pending_count,
			COUNT(DISTINCT CASE WHEN tsu.status = 'rescheduled' THEN t.trip_id END) as rescheduled_count
		FROM trips t
		INNER JOIN users u ON t.agent_id = u.id
		LEFT JOIN tripstatusupdates tsu ON t.trip_id = tsu.trip_id
		WHERE t.created_at >= NOW() - INTERVAL '365 days'  -- Last year for testing
		GROUP BY t.agent_id, u.name, u.simple_id
		ORDER BY delivered_count DESC`

	rows, err := r.db.Query(query)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agents delivery counts")
		return nil, fmt.Errorf("failed to get agents delivery counts: %w", err)
	}
	defer rows.Close()

	agentCounts := make([]map[string]interface{}, 0)

	for rows.Next() {
		var agentID, name, simpleID string
		var totalTrips, deliveredCount, failedCount, pendingCount, rescheduledCount int

		err := rows.Scan(&agentID, &name, &simpleID, &totalTrips, &deliveredCount, &failedCount, &pendingCount, &rescheduledCount)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan agent delivery counts row")
			continue
		}

		agentData := map[string]interface{}{
			"agent_id":          agentID,
			"name":              name,
			"simple_id":         simpleID,
			"total_trips":       totalTrips,
			"delivered_count":   deliveredCount,
			"failed_count":      failedCount,
			"pending_count":     pendingCount,
			"rescheduled_count": rescheduledCount,
			"success_rate":      0.0,
		}

		// Calculate success rate
		if totalTrips > 0 {
			agentData["success_rate"] = float64(deliveredCount) / float64(totalTrips) * 100
		}

		agentCounts = append(agentCounts, agentData)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating agent delivery counts rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	result := map[string]interface{}{
		"agents": agentCounts,
		"total_agents": len(agentCounts),
	}

	return result, nil
}

// ArchiveTrackingData archives tracking data for a specific date
func (r *TrackingRepository) ArchiveTrackingData(agentID uuid.UUID, date time.Time) error {
	return r.db.Transaction(func(tx *sql.Tx) error {
		// Get tracking data for the date
		startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
		endOfDay := startOfDay.Add(24 * time.Hour)

		// Aggregate tracking data
		aggregateQuery := `
			SELECT 
				role,
				json_agg(
					json_build_object(
						'longitude', ST_X(geom),
						'latitude', ST_Y(geom),
						'timestamp', timestamp,
						'speed', speed_m_s,
						'heading', heading_deg,
						'accuracy', accuracy_m,
						'altitude', altitude_m,
						'battery_pct', battery_pct
					) ORDER BY timestamp
				) as track_points,
				MIN(timestamp) as start_time,
				MAX(timestamp) as end_time,
				MIN(battery_pct) as battery_min,
				MAX(battery_pct) as battery_max,
				COUNT(DISTINCT trip_id) as trip_count
			FROM TrackingUpdates
			WHERE agent_id = $1 AND timestamp >= $2 AND timestamp < $3 AND is_active = true
			GROUP BY role`

		rows, err := tx.Query(aggregateQuery, agentID, startOfDay, endOfDay)
		if err != nil {
			return fmt.Errorf("failed to aggregate tracking data: %w", err)
		}
		defer rows.Close()

		for rows.Next() {
			var role models.Role
			var trackPointsJSON []byte
			var startTime, endTime sql.NullTime
			var batteryMin, batteryMax sql.NullFloat64
			var tripCount int

			err := rows.Scan(&role, &trackPointsJSON, &startTime, &endTime, &batteryMin, &batteryMax, &tripCount)
			if err != nil {
				return fmt.Errorf("failed to scan aggregate data: %w", err)
			}

			// Parse track points
			var trackPoints models.TrackPoints
			if err := trackPoints.Scan(trackPointsJSON); err != nil {
				return fmt.Errorf("failed to parse track points: %w", err)
			}

			// Insert into TrackingArchives
			archiveQuery := `
				INSERT INTO TrackingArchives (
					agent_id, role, date, track_points, start_time, end_time,
					battery_min, battery_max, trip_count
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
				ON CONFLICT (agent_id, date, role) DO UPDATE SET
					track_points = EXCLUDED.track_points,
					start_time = EXCLUDED.start_time,
					end_time = EXCLUDED.end_time,
					battery_min = EXCLUDED.battery_min,
					battery_max = EXCLUDED.battery_max,
					trip_count = EXCLUDED.trip_count`

			var startTimePtr, endTimePtr *time.Time
			if startTime.Valid {
				startTimePtr = &startTime.Time
			}
			if endTime.Valid {
				endTimePtr = &endTime.Time
			}

			var batteryMinPtr, batteryMaxPtr *float64
			if batteryMin.Valid {
				batteryMinPtr = &batteryMin.Float64
			}
			if batteryMax.Valid {
				batteryMaxPtr = &batteryMax.Float64
			}

			_, err = tx.Exec(archiveQuery,
				agentID, role, date, trackPoints, startTimePtr, endTimePtr,
				batteryMinPtr, batteryMaxPtr, tripCount,
			)
			if err != nil {
				return fmt.Errorf("failed to insert archive data: %w", err)
			}
		}

		return nil
	})
}

// GetTripStatusUpdates retrieves trip status updates for an agent within a date range
func (r *TrackingRepository) GetTripStatusUpdates(agentID uuid.UUID, startDate, endDate time.Time) ([]*models.TripStatusUpdate, error) {
	query := `
		SELECT
			tsu.id, tsu.trip_id, tsu.status, tsu.timestamp,
			ST_X(tsu.location::geometry) as longitude,
			ST_Y(tsu.location::geometry) as latitude,
			tsu.updated_by, tsu.note, tsu.geocoded_name
		FROM tripstatusupdates tsu
		INNER JOIN trips t ON tsu.trip_id = t.trip_id
		WHERE t.agent_id = $1 AND tsu.timestamp >= $2 AND tsu.timestamp <= $3
		ORDER BY tsu.timestamp ASC`

	rows, err := r.db.Query(query, agentID, startDate, endDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip status updates")
		return nil, fmt.Errorf("failed to get trip status updates: %w", err)
	}
	defer rows.Close()

	var statusUpdates []*models.TripStatusUpdate
	for rows.Next() {
		var (
			id                    uuid.UUID
			tripID                uuid.UUID
			status                string
			timestamp             time.Time
			longitude, latitude   sql.NullFloat64
			updatedBy             sql.NullString
			note                  sql.NullString
			geocodedName          sql.NullString
		)

		err := rows.Scan(&id, &tripID, &status, &timestamp, &longitude, &latitude, &updatedBy, &note, &geocodedName)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan trip status update row")
			continue
		}

		statusUpdate := &models.TripStatusUpdate{
			ID:        id,
			TripID:    tripID,
			Status:    models.TripStatus(status),
			Timestamp: timestamp,
		}

		// Set location if available
		if longitude.Valid && latitude.Valid {
			statusUpdate.Location = &models.Point{
				Longitude: longitude.Float64,
				Latitude:  latitude.Float64,
			}
		}

		// Set updated_by if available
		if updatedBy.Valid {
			updatedByUUID, err := uuid.Parse(updatedBy.String)
			if err == nil {
				statusUpdate.UpdatedBy = &updatedByUUID
			}
		}

		// Set note if available
		if note.Valid {
			statusUpdate.Note = &note.String
		}

		// Set geocoded_name if available
		if geocodedName.Valid {
			statusUpdate.GeocodedName = &geocodedName.String
		}

		statusUpdates = append(statusUpdates, statusUpdate)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating trip status update rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return statusUpdates, nil
}

// UpdateGeocodedName updates the geocoded_name for a trip status update
func (r *TrackingRepository) UpdateGeocodedName(eventID uuid.UUID, geocodedName string) error {
	query := `UPDATE tripstatusupdates SET geocoded_name = $1 WHERE id = $2`

	result, err := r.db.Exec(query, geocodedName, eventID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"event_id":      eventID,
			"geocoded_name": geocodedName,
		}).Error("Failed to update geocoded name")
		return fmt.Errorf("failed to update geocoded name: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logrus.WithError(err).Error("Failed to get rows affected")
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		logrus.WithField("event_id", eventID).Warn("No rows updated - event ID not found")
		return fmt.Errorf("event ID not found: %s", eventID)
	}

	logrus.WithFields(logrus.Fields{
		"event_id":      eventID,
		"geocoded_name": geocodedName,
		"rows_affected": rowsAffected,
	}).Info("Successfully updated geocoded name")

	return nil
}

// GetAgentProfileWithName retrieves agent profile information including name from Users table
func (r *TrackingRepository) GetAgentProfileWithName(agentID uuid.UUID) (map[string]interface{}, error) {
	query := `
		SELECT
			u.id,
			u.name,
			u.email,
			u.phone,
			u.user_type,
			ap.vehicle_type,
			ap.vehicle_no,
			ap.license_no,
			ap.rating,
			ap.is_available
		FROM users u
		LEFT JOIN agentprofiles ap ON u.id = ap.user_id
		WHERE u.id = $1 AND u.is_active = true`

	var profile struct {
		ID          uuid.UUID `db:"id"`
		Name        string    `db:"name"`
		Email       string    `db:"email"`
		Phone       string    `db:"phone"`
		UserType    string    `db:"user_type"`
		VehicleType *string   `db:"vehicle_type"`
		VehicleNo   *string   `db:"vehicle_no"`
		LicenseNo   *string   `db:"license_no"`
		Rating      *float64  `db:"rating"`
		IsAvailable *bool     `db:"is_available"`
	}

	err := r.db.QueryRow(query, agentID).Scan(
		&profile.ID, &profile.Name, &profile.Email, &profile.Phone, &profile.UserType,
		&profile.VehicleType, &profile.VehicleNo, &profile.LicenseNo,
		&profile.Rating, &profile.IsAvailable,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("agent not found")
		}
		logrus.WithError(err).Error("Failed to get agent profile with name")
		return nil, fmt.Errorf("failed to get agent profile: %w", err)
	}

	// Convert to map for JSON response
	result := map[string]interface{}{
		"id":        profile.ID,
		"name":      profile.Name,
		"email":     profile.Email,
		"phone":     profile.Phone,
		"user_type": profile.UserType,
	}

	// Add agent profile fields if they exist
	if profile.VehicleType != nil {
		result["vehicle_type"] = *profile.VehicleType
	}
	if profile.VehicleNo != nil {
		result["vehicle_no"] = *profile.VehicleNo
	}
	if profile.LicenseNo != nil {
		result["license_no"] = *profile.LicenseNo
	}
	if profile.Rating != nil {
		result["rating"] = *profile.Rating
	}
	if profile.IsAvailable != nil {
		result["is_available"] = *profile.IsAvailable
	}

	return result, nil
}

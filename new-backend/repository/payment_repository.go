package repository

import (
	"database/sql"
	"fmt"
	"time"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type PaymentRepository struct {
	db *database.DB
}

func NewPaymentRepository(db *database.DB) *PaymentRepository {
	return &PaymentRepository{db: db}
}

// CreatePayment creates a new payment record
func (r *PaymentRepository) CreatePayment(req *models.CreatePaymentRequest) (*models.Payment, error) {
	payment := &models.Payment{
		PaymentID:      uuid.New(),
		TripID:         req.TripID,
		Amount:         req.Amount,
		Status:         models.PaymentStatusPending,
		PaymentMode:    req.PaymentMode,
		TransactionRef: req.TransactionRef,
	}

	query := `
		INSERT INTO Payments (payment_id, trip_id, amount, status, payment_mode, transaction_ref)
		VALUES ($1, $2, $3, $4, $5, $6)`

	_, err := r.db.Exec(query, payment.PaymentID, payment.TripID, payment.Amount,
		payment.Status, payment.PaymentMode, payment.TransactionRef)
	if err != nil {
		logrus.WithError(err).Error("Failed to create payment")
		return nil, fmt.Errorf("failed to create payment: %w", err)
	}

	return payment, nil
}

// GetPaymentByID retrieves a payment by ID
func (r *PaymentRepository) GetPaymentByID(paymentID uuid.UUID) (*models.Payment, error) {
	payment := &models.Payment{}
	query := `
		SELECT payment_id, trip_id, amount, status, payment_mode, transaction_ref, paid_at
		FROM Payments
		WHERE payment_id = $1`

	var paidAt sql.NullTime

	err := r.db.QueryRow(query, paymentID).Scan(
		&payment.PaymentID, &payment.TripID, &payment.Amount,
		&payment.Status, &payment.PaymentMode, &payment.TransactionRef, &paidAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("payment not found")
		}
		logrus.WithError(err).Error("Failed to get payment by ID")
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	if paidAt.Valid {
		payment.PaidAt = &paidAt.Time
	}

	return payment, nil
}

// GetPaymentByTripID retrieves a payment by trip ID
func (r *PaymentRepository) GetPaymentByTripID(tripID uuid.UUID) (*models.Payment, error) {
	payment := &models.Payment{}
	query := `
		SELECT payment_id, trip_id, amount, status, payment_mode, transaction_ref, paid_at
		FROM Payments
		WHERE trip_id = $1`

	var paidAt sql.NullTime

	err := r.db.QueryRow(query, tripID).Scan(
		&payment.PaymentID, &payment.TripID, &payment.Amount,
		&payment.Status, &payment.PaymentMode, &payment.TransactionRef, &paidAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("payment not found")
		}
		logrus.WithError(err).Error("Failed to get payment by trip ID")
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	if paidAt.Valid {
		payment.PaidAt = &paidAt.Time
	}

	return payment, nil
}

// UpdatePaymentStatus updates the status of a payment
func (r *PaymentRepository) UpdatePaymentStatus(paymentID uuid.UUID, status models.PaymentStatus, transactionRef *string) error {
	var query string
	var args []interface{}

	if status == models.PaymentStatusPaid {
		query = `
			UPDATE Payments 
			SET status = $2, paid_at = CURRENT_TIMESTAMP, transaction_ref = $3
			WHERE payment_id = $1`
		args = []interface{}{paymentID, status, transactionRef}
	} else {
		query = `
			UPDATE Payments 
			SET status = $2, transaction_ref = $3
			WHERE payment_id = $1`
		args = []interface{}{paymentID, status, transactionRef}
	}

	result, err := r.db.Exec(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to update payment status")
		return fmt.Errorf("failed to update payment status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("payment not found")
	}

	return nil
}

// GetPaymentsByStatus retrieves payments by status
func (r *PaymentRepository) GetPaymentsByStatus(status models.PaymentStatus) ([]*models.Payment, error) {
	query := `
		SELECT payment_id, trip_id, amount, status, payment_mode, transaction_ref, paid_at
		FROM Payments
		WHERE status = $1
		ORDER BY payment_id DESC`

	rows, err := r.db.Query(query, status)
	if err != nil {
		logrus.WithError(err).Error("Failed to get payments by status")
		return nil, fmt.Errorf("failed to get payments: %w", err)
	}
	defer rows.Close()

	var payments []*models.Payment
	for rows.Next() {
		payment := &models.Payment{}
		var paidAt sql.NullTime

		err := rows.Scan(
			&payment.PaymentID, &payment.TripID, &payment.Amount,
			&payment.Status, &payment.PaymentMode, &payment.TransactionRef, &paidAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan payment row")
			return nil, fmt.Errorf("failed to scan payment: %w", err)
		}

		if paidAt.Valid {
			payment.PaidAt = &paidAt.Time
		}

		payments = append(payments, payment)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating payment rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return payments, nil
}

// GetPaymentsByDateRange retrieves payments within a date range
func (r *PaymentRepository) GetPaymentsByDateRange(startDate, endDate time.Time) ([]*models.Payment, error) {
	query := `
		SELECT payment_id, trip_id, amount, status, payment_mode, transaction_ref, paid_at
		FROM Payments p
		JOIN Trips t ON p.trip_id = t.trip_id
		WHERE t.created_at >= $1 AND t.created_at <= $2
		ORDER BY t.created_at DESC`

	rows, err := r.db.Query(query, startDate, endDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get payments by date range")
		return nil, fmt.Errorf("failed to get payments: %w", err)
	}
	defer rows.Close()

	var payments []*models.Payment
	for rows.Next() {
		payment := &models.Payment{}
		var paidAt sql.NullTime

		err := rows.Scan(
			&payment.PaymentID, &payment.TripID, &payment.Amount,
			&payment.Status, &payment.PaymentMode, &payment.TransactionRef, &paidAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan payment row")
			return nil, fmt.Errorf("failed to scan payment: %w", err)
		}

		if paidAt.Valid {
			payment.PaidAt = &paidAt.Time
		}

		payments = append(payments, payment)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating payment rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return payments, nil
}

// GetPaymentStats retrieves payment statistics
func (r *PaymentRepository) GetPaymentStats() (map[string]interface{}, error) {
	query := `
		SELECT 
			COUNT(*) as total_payments,
			COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_payments,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
			COALESCE(SUM(CASE WHEN status = 'paid' THEN amount END), 0) as total_paid_amount,
			COALESCE(SUM(CASE WHEN status = 'pending' THEN amount END), 0) as total_pending_amount,
			COALESCE(AVG(CASE WHEN status = 'paid' THEN amount END), 0) as avg_payment_amount
		FROM Payments`

	var totalPayments, paidPayments, pendingPayments, failedPayments int
	var totalPaidAmount, totalPendingAmount, avgPaymentAmount float64

	err := r.db.QueryRow(query).Scan(
		&totalPayments, &paidPayments, &pendingPayments, &failedPayments,
		&totalPaidAmount, &totalPendingAmount, &avgPaymentAmount,
	)
	if err != nil {
		logrus.WithError(err).Error("Failed to get payment stats")
		return nil, fmt.Errorf("failed to get payment stats: %w", err)
	}

	successRate := 0.0
	if totalPayments > 0 {
		successRate = float64(paidPayments) / float64(totalPayments) * 100
	}

	stats := map[string]interface{}{
		"total_payments":        totalPayments,
		"paid_payments":         paidPayments,
		"pending_payments":      pendingPayments,
		"failed_payments":       failedPayments,
		"total_paid_amount":     totalPaidAmount,
		"total_pending_amount":  totalPendingAmount,
		"avg_payment_amount":    avgPaymentAmount,
		"payment_success_rate":  successRate,
	}

	return stats, nil
}

// GetUserPaymentHistory retrieves payment history for a user (customer or agent)
func (r *PaymentRepository) GetUserPaymentHistory(userID uuid.UUID, userType models.UserType) ([]*models.Payment, error) {
	var query string
	if userType == models.UserTypeCustomer {
		query = `
			SELECT p.payment_id, p.trip_id, p.amount, p.status, p.payment_mode, p.transaction_ref, p.paid_at
			FROM Payments p
			JOIN Trips t ON p.trip_id = t.trip_id
			WHERE t.customer_id = $1
			ORDER BY t.created_at DESC`
	} else {
		query = `
			SELECT p.payment_id, p.trip_id, p.amount, p.status, p.payment_mode, p.transaction_ref, p.paid_at
			FROM Payments p
			JOIN Trips t ON p.trip_id = t.trip_id
			WHERE t.agent_id = $1
			ORDER BY t.created_at DESC`
	}

	rows, err := r.db.Query(query, userID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user payment history")
		return nil, fmt.Errorf("failed to get payment history: %w", err)
	}
	defer rows.Close()

	var payments []*models.Payment
	for rows.Next() {
		payment := &models.Payment{}
		var paidAt sql.NullTime

		err := rows.Scan(
			&payment.PaymentID, &payment.TripID, &payment.Amount,
			&payment.Status, &payment.PaymentMode, &payment.TransactionRef, &paidAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan payment row")
			return nil, fmt.Errorf("failed to scan payment: %w", err)
		}

		if paidAt.Valid {
			payment.PaidAt = &paidAt.Time
		}

		payments = append(payments, payment)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating payment rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return payments, nil
}

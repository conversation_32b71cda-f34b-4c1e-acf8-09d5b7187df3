package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type TripRepository struct {
	db *database.DB
}

func NewTripRepository(db *database.DB) *TripRepository {
	return &TripRepository{db: db}
}

// CreateTrip creates a new trip
func (r *TripRepository) CreateTrip(req *models.CreateTripRequest) (*models.Trip, error) {
	trip := &models.Trip{
		TripID:         uuid.New(),
		TripType:       req.TripType,
		CustomerID:     req.CustomerID,
		AgentID:        req.AgentID,
		PickupLocation: req.PickupLocation,
		DropLocation:   req.DropLocation,
		Status:         models.TripStatusRequested,
		ItemDetails:    req.ItemDetails,
		FareEstimate:   req.FareEstimate,
		DeliveryFee:    req.DeliveryFee,
	}

	query := `
		INSERT INTO trips (
			trip_id, trip_type, customer_id, agent_id, pickup_location, drop_location,
			status, item_details, fare_estimate, delivery_fee
		) VALUES (
			$1, $2, $3, $4, ST_GeomFromText($5, 4326), ST_GeomFromText($6, 4326),
			$7, $8, $9, $10
		)
		RETURNING created_at`

	err := r.db.QueryRow(query,
		trip.TripID, trip.TripType, trip.CustomerID, trip.AgentID,
		trip.PickupLocation.String(), trip.DropLocation.String(),
		trip.Status, trip.ItemDetails, trip.FareEstimate, trip.DeliveryFee,
	).Scan(&trip.CreatedAt)
	if err != nil {
		logrus.WithError(err).Error("Failed to create trip")
		return nil, fmt.Errorf("failed to create trip: %w", err)
	}

	return trip, nil
}

// GetTripByID retrieves a trip by ID
func (r *TripRepository) GetTripByID(tripID uuid.UUID) (*models.Trip, error) {
	trip := &models.Trip{}
	query := `
		SELECT 
			trip_id, trip_type, customer_id, agent_id,
			ST_X(pickup_location) as pickup_lon, ST_Y(pickup_location) as pickup_lat,
			ST_X(drop_location) as drop_lon, ST_Y(drop_location) as drop_lat,
			status, item_details, fare_estimate, delivery_fee, created_at, completed_at
		FROM trips
		WHERE trip_id = $1`

	var pickupLon, pickupLat, dropLon, dropLat float64
	var completedAt sql.NullTime

	err := r.db.QueryRow(query, tripID).Scan(
		&trip.TripID, &trip.TripType, &trip.CustomerID, &trip.AgentID,
		&pickupLon, &pickupLat, &dropLon, &dropLat,
		&trip.Status, &trip.ItemDetails, &trip.FareEstimate, &trip.DeliveryFee,
		&trip.CreatedAt, &completedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trip not found")
		}
		logrus.WithError(err).Error("Failed to get trip by ID")
		return nil, fmt.Errorf("failed to get trip: %w", err)
	}

	trip.PickupLocation = models.Point{Longitude: pickupLon, Latitude: pickupLat}
	trip.DropLocation = models.Point{Longitude: dropLon, Latitude: dropLat}

	if completedAt.Valid {
		trip.CompletedAt = &completedAt.Time
	}

	return trip, nil
}

// SetTemperatureRequirements sets temperature requirements for a trip
func (r *TripRepository) SetTemperatureRequirements(tripID uuid.UUID, requirements map[string]interface{}) error {
	requirementsJSON, err := json.Marshal(requirements)
	if err != nil {
		return fmt.Errorf("failed to marshal temperature requirements: %w", err)
	}

	query := `
		UPDATE Trips
		SET temperature_requirements = $2, is_temperature_sensitive = true, updated_at = CURRENT_TIMESTAMP
		WHERE trip_id = $1`

	result, err := r.db.Exec(query, tripID, string(requirementsJSON))
	if err != nil {
		logrus.WithError(err).Error("Failed to set temperature requirements")
		return fmt.Errorf("failed to set temperature requirements: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("trip not found")
	}

	logrus.WithFields(logrus.Fields{
		"trip_id": tripID,
		"requirements": requirements,
	}).Info("Temperature requirements set successfully")

	return nil
}

// GetTripsByDateRange retrieves trips within a date range
func (r *TripRepository) GetTripsByDateRange(startDate, endDate time.Time) ([]*models.Trip, error) {
	query := `
		SELECT
			trip_id, trip_type, customer_id, agent_id,
			ST_X(pickup_location::geometry) as pickup_lng, ST_Y(pickup_location::geometry) as pickup_lat,
			ST_X(drop_location::geometry) as drop_lng, ST_Y(drop_location::geometry) as drop_lat,
			status, item_details, fare_estimate, delivery_fee,
			temperature_requirements, is_temperature_sensitive,
			created_at, completed_at
		FROM Trips
		WHERE created_at >= $1 AND created_at <= $2
		ORDER BY created_at DESC`

	rows, err := r.db.Query(query, startDate, endDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trips by date range")
		return nil, fmt.Errorf("failed to get trips by date range: %w", err)
	}
	defer rows.Close()

	var trips []*models.Trip
	for rows.Next() {
		trip := &models.Trip{}
		var pickupLng, pickupLat, dropLng, dropLat float64
		var itemDetailsJSON, tempRequirementsJSON sql.NullString

		err := rows.Scan(
			&trip.TripID, &trip.TripType, &trip.CustomerID, &trip.AgentID,
			&pickupLng, &pickupLat, &dropLng, &dropLat,
			&trip.Status, &itemDetailsJSON, &trip.FareEstimate, &trip.DeliveryFee,
			&tempRequirementsJSON, &trip.IsTemperatureSensitive,
			&trip.CreatedAt, &trip.CompletedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan trip row")
			continue
		}

		// Set location points
		trip.PickupLocation = models.Point{Latitude: pickupLat, Longitude: pickupLng}
		trip.DropLocation = models.Point{Latitude: dropLat, Longitude: dropLng}

		// Parse item details JSON
		if itemDetailsJSON.Valid {
			if err := json.Unmarshal([]byte(itemDetailsJSON.String), &trip.ItemDetails); err != nil {
				logrus.WithError(err).Warn("Failed to parse item details JSON")
			}
		}

		// Parse temperature requirements JSON
		if tempRequirementsJSON.Valid {
			if err := json.Unmarshal([]byte(tempRequirementsJSON.String), &trip.TemperatureRequirements); err != nil {
				logrus.WithError(err).Warn("Failed to parse temperature requirements JSON")
			}
		}

		trips = append(trips, trip)
	}

	return trips, nil
}

// UpdateTripStatus updates the status of a trip and creates a status update record
func (r *TripRepository) UpdateTripStatus(req *models.TripStatusUpdateRequest) error {
	return r.db.Transaction(func(tx *sql.Tx) error {
		// Update trip status
		updateTripQuery := `UPDATE Trips SET status = $1 WHERE trip_id = $2`
		_, err := tx.Exec(updateTripQuery, req.Status, req.TripID)
		if err != nil {
			return fmt.Errorf("failed to update trip status: %w", err)
		}

		// Insert status update record
		insertStatusQuery := `
			INSERT INTO tripstatusupdates (trip_id, status, location, updated_by, note)
			VALUES ($1, $2, $3, $4, $5)`

		var locationStr interface{}
		if req.Location != nil {
			locationStr = fmt.Sprintf("ST_GeomFromText('%s', 4326)", req.Location.String())
		}

		_, err = tx.Exec(insertStatusQuery, req.TripID, req.Status, locationStr, req.UpdatedBy, req.Note)
		if err != nil {
			return fmt.Errorf("failed to insert status update: %w", err)
		}

		// If trip is completed, update completed_at timestamp
		if req.Status == models.TripStatusDelivered || req.Status == models.TripStatusCancelled || req.Status == models.TripStatusReturned {
			completeQuery := `UPDATE Trips SET completed_at = CURRENT_TIMESTAMP WHERE trip_id = $1`
			_, err = tx.Exec(completeQuery, req.TripID)
			if err != nil {
				return fmt.Errorf("failed to update completed_at: %w", err)
			}
		}

		return nil
	})
}

// GetTripStatusUpdates retrieves all status updates for a trip
func (r *TripRepository) GetTripStatusUpdates(tripID uuid.UUID) ([]*models.TripStatusUpdate, error) {
	query := `
		SELECT
			id, trip_id, status, timestamp,
			ST_X(location) as longitude, ST_Y(location) as latitude,
			updated_by, note, geocoded_name
		FROM tripstatusupdates
		WHERE trip_id = $1
		ORDER BY timestamp ASC`

	rows, err := r.db.Query(query, tripID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip status updates")
		return nil, fmt.Errorf("failed to get status updates: %w", err)
	}
	defer rows.Close()

	var updates []*models.TripStatusUpdate
	for rows.Next() {
		update := &models.TripStatusUpdate{}
		var longitude, latitude sql.NullFloat64
		var updatedBy sql.NullString
		var geocodedName sql.NullString

		err := rows.Scan(
			&update.ID, &update.TripID, &update.Status, &update.Timestamp,
			&longitude, &latitude, &updatedBy, &update.Note, &geocodedName,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan status update row")
			return nil, fmt.Errorf("failed to scan status update: %w", err)
		}

		if longitude.Valid && latitude.Valid {
			update.Location = &models.Point{
				Longitude: longitude.Float64,
				Latitude:  latitude.Float64,
			}
		}

		if updatedBy.Valid {
			userID, err := uuid.Parse(updatedBy.String)
			if err == nil {
				update.UpdatedBy = &userID
			}
		}

		if geocodedName.Valid {
			update.GeocodedName = &geocodedName.String
		}

		updates = append(updates, update)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating status update rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return updates, nil
}

// GetTripsByUser retrieves trips for a specific user (customer or agent)
func (r *TripRepository) GetTripsByUser(userID uuid.UUID, userType string, limit, offset int) ([]*models.Trip, error) {
	var whereClause string
	if userType == models.UserTypeCustomerStr {
		whereClause = "customer_id = $1"
	} else {
		whereClause = "agent_id = $1"
	}

	query := fmt.Sprintf(`
		SELECT 
			trip_id, trip_type, customer_id, agent_id,
			ST_X(pickup_location) as pickup_lon, ST_Y(pickup_location) as pickup_lat,
			ST_X(drop_location) as drop_lon, ST_Y(drop_location) as drop_lat,
			status, item_details, fare_estimate, delivery_fee, created_at, completed_at
		FROM trips
		WHERE %s
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3`, whereClause)

	rows, err := r.db.Query(query, userID, limit, offset)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trips by user")
		return nil, fmt.Errorf("failed to get trips: %w", err)
	}
	defer rows.Close()

	var trips []*models.Trip
	for rows.Next() {
		trip := &models.Trip{}
		var pickupLon, pickupLat, dropLon, dropLat float64
		var completedAt sql.NullTime

		err := rows.Scan(
			&trip.TripID, &trip.TripType, &trip.CustomerID, &trip.AgentID,
			&pickupLon, &pickupLat, &dropLon, &dropLat,
			&trip.Status, &trip.ItemDetails, &trip.FareEstimate, &trip.DeliveryFee,
			&trip.CreatedAt, &completedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan trip row")
			return nil, fmt.Errorf("failed to scan trip: %w", err)
		}

		trip.PickupLocation = models.Point{Longitude: pickupLon, Latitude: pickupLat}
		trip.DropLocation = models.Point{Longitude: dropLon, Latitude: dropLat}

		if completedAt.Valid {
			trip.CompletedAt = &completedAt.Time
		}

		trips = append(trips, trip)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating trip rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return trips, nil
}

// GetActiveTrips retrieves all active trips (not completed or cancelled)
func (r *TripRepository) GetActiveTrips() ([]*models.Trip, error) {
	query := `
		SELECT 
			trip_id, trip_type, customer_id, agent_id,
			ST_X(pickup_location) as pickup_lon, ST_Y(pickup_location) as pickup_lat,
			ST_X(drop_location) as drop_lon, ST_Y(drop_location) as drop_lat,
			status, item_details, fare_estimate, delivery_fee, created_at, completed_at
		FROM trips
		WHERE status NOT IN ('completed', 'cancelled', 'delivered')
		ORDER BY created_at DESC`

	rows, err := r.db.Query(query)
	if err != nil {
		logrus.WithError(err).Error("Failed to get active trips")
		return nil, fmt.Errorf("failed to get active trips: %w", err)
	}
	defer rows.Close()

	var trips []*models.Trip
	for rows.Next() {
		trip := &models.Trip{}
		var pickupLon, pickupLat, dropLon, dropLat float64
		var completedAt sql.NullTime

		err := rows.Scan(
			&trip.TripID, &trip.TripType, &trip.CustomerID, &trip.AgentID,
			&pickupLon, &pickupLat, &dropLon, &dropLat,
			&trip.Status, &trip.ItemDetails, &trip.FareEstimate, &trip.DeliveryFee,
			&trip.CreatedAt, &completedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan active trip row")
			return nil, fmt.Errorf("failed to scan trip: %w", err)
		}

		trip.PickupLocation = models.Point{Longitude: pickupLon, Latitude: pickupLat}
		trip.DropLocation = models.Point{Longitude: dropLon, Latitude: dropLat}

		if completedAt.Valid {
			trip.CompletedAt = &completedAt.Time
		}

		trips = append(trips, trip)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating active trip rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return trips, nil
}

// SetComplianceRequired sets whether a trip requires compliance monitoring
func (r *TripRepository) SetComplianceRequired(tripID uuid.UUID, required bool) error {
	query := `
		UPDATE Trips
		SET requires_compliance = $2, updated_at = CURRENT_TIMESTAMP
		WHERE trip_id = $1`

	result, err := r.db.Exec(query, tripID, required)
	if err != nil {
		logrus.WithError(err).Error("Failed to set compliance required")
		return fmt.Errorf("failed to set compliance required: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("trip not found")
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":  tripID,
		"required": required,
	}).Info("Compliance requirement updated successfully")

	return nil
}

// AssignDelivery creates a new delivery assignment
func (r *TripRepository) AssignDelivery(agentID, customerID uuid.UUID, pickupLocation, dropLocation, itemDetails map[string]interface{}, deliveryFee float64, scheduledDate, notes string) (uuid.UUID, error) {
	tripID := uuid.New()

	// Convert locations to PostGIS points
	pickupLat, _ := pickupLocation["latitude"].(float64)
	pickupLng, _ := pickupLocation["longitude"].(float64)
	dropLat, _ := dropLocation["latitude"].(float64)
	dropLng, _ := dropLocation["longitude"].(float64)

	// Convert item details to JSON
	itemDetailsJSON, _ := json.Marshal(itemDetails)

	// Parse scheduled date (for future use)
	_ = scheduledDate // Currently not used in the query

	query := `
		INSERT INTO trips (
			trip_id, customer_id, agent_id,
			pickup_location, drop_location,
			item_details, delivery_fee,
			created_at
		) VALUES (
			$1, $2, $3,
			ST_SetSRID(ST_MakePoint($4, $5), 4326),
			ST_SetSRID(ST_MakePoint($6, $7), 4326),
			$8, $9, NOW()
		)`

	_, err := r.db.Exec(query, tripID, customerID, agentID, pickupLng, pickupLat, dropLng, dropLat, itemDetailsJSON, deliveryFee)
	if err != nil {
		logrus.WithError(err).Error("Failed to create delivery assignment")
		return uuid.Nil, fmt.Errorf("failed to create delivery assignment: %w", err)
	}

	// Create initial status update
	statusQuery := `
		INSERT INTO tripstatusupdates (
			id, trip_id, status, timestamp,
			location, updated_by, note
		) VALUES (
			$1, $2, 'requested', NOW(),
			ST_SetSRID(ST_MakePoint($3, $4), 4326),
			$5, $6
		)`

	statusID := uuid.New()
	statusNote := "Delivery assigned to agent"
	if notes != "" {
		statusNote = notes
	}

	_, err = r.db.Exec(statusQuery, statusID, tripID, pickupLng, pickupLat, agentID, statusNote)
	if err != nil {
		logrus.WithError(err).Error("Failed to create initial status update")
		// Don't fail the whole operation, just log the error
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":    tripID,
		"agent_id":   agentID,
		"customer_id": customerID,
	}).Info("Delivery assigned successfully")

	return tripID, nil
}

// GetAgentDeliveriesByDate retrieves all deliveries assigned to an agent for a specific date
func (r *TripRepository) GetAgentDeliveriesByDate(agentID uuid.UUID, date time.Time) ([]*models.Trip, error) {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	query := `
		SELECT
			t.trip_id, t.trip_type, t.customer_id, t.agent_id,
			ST_X(ST_Transform(t.pickup_location::geometry, 4326)) as pickup_lon,
			ST_Y(ST_Transform(t.pickup_location::geometry, 4326)) as pickup_lat,
			ST_X(ST_Transform(t.drop_location::geometry, 4326)) as drop_lon,
			ST_Y(ST_Transform(t.drop_location::geometry, 4326)) as drop_lat,
			t.status, t.item_details, t.fare_estimate, t.delivery_fee,
			t.created_at, t.completed_at,
			u.name as customer_name, u.phone as customer_phone
		FROM trips t
		LEFT JOIN users u ON t.customer_id = u.id
		WHERE t.agent_id = $1
		AND t.created_at >= $2
		AND t.created_at < $3
		ORDER BY t.created_at ASC`

	rows, err := r.db.Query(query, agentID, startOfDay, endOfDay)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agent deliveries by date")
		return nil, fmt.Errorf("failed to get agent deliveries: %w", err)
	}
	defer rows.Close()

	var trips []*models.Trip
	for rows.Next() {
		trip := &models.Trip{}
		var pickupLon, pickupLat, dropLon, dropLat float64
		var completedAt sql.NullTime
		var customerName, customerPhone sql.NullString

		err := rows.Scan(
			&trip.TripID, &trip.TripType, &trip.CustomerID, &trip.AgentID,
			&pickupLon, &pickupLat, &dropLon, &dropLat,
			&trip.Status, &trip.ItemDetails, &trip.FareEstimate, &trip.DeliveryFee,
			&trip.CreatedAt, &completedAt, &customerName, &customerPhone,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan agent delivery row")
			continue
		}

		trip.PickupLocation = models.Point{Longitude: pickupLon, Latitude: pickupLat}
		trip.DropLocation = models.Point{Longitude: dropLon, Latitude: dropLat}

		if completedAt.Valid {
			trip.CompletedAt = &completedAt.Time
		}

		// Add customer info to trip for convenience
		if customerName.Valid {
			trip.CustomerName = customerName.String
		}
		if customerPhone.Valid {
			trip.CustomerPhone = customerPhone.String
		}

		trips = append(trips, trip)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating agent delivery rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return trips, nil
}

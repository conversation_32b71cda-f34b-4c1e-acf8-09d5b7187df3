package repository

import (
	"database/sql"
	"fmt"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type RatingRepository struct {
	db *database.DB
}

func NewRatingRepository(db *database.DB) *RatingRepository {
	return &RatingRepository{db: db}
}

// CreateRating creates a new rating
func (r *RatingRepository) CreateRating(req *models.CreateRatingRequest) (*models.Rating, error) {
	// Check if rating already exists for this trip and user combination
	existsQuery := `
		SELECT COUNT(*) FROM Ratings 
		WHERE trip_id = $1 AND from_user = $2 AND to_user = $3`
	
	var count int
	err := r.db.QueryRow(existsQuery, req.TripID, req.FromUser, req.ToUser).Scan(&count)
	if err != nil {
		logrus.WithError(err).Error("Failed to check existing rating")
		return nil, fmt.Errorf("failed to check existing rating: %w", err)
	}

	if count > 0 {
		return nil, fmt.Errorf("rating already exists for this trip and user combination")
	}

	rating := &models.Rating{
		ID:       uuid.New(),
		TripID:   req.TripID,
		FromUser: req.FromUser,
		ToUser:   req.ToUser,
		Rating:   req.Rating,
		Review:   req.Review,
	}

	query := `
		INSERT INTO Ratings (id, trip_id, from_user, to_user, rating, review)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING created_at`

	err = r.db.QueryRow(query, rating.ID, rating.TripID, rating.FromUser,
		rating.ToUser, rating.Rating, rating.Review).Scan(&rating.CreatedAt)
	if err != nil {
		logrus.WithError(err).Error("Failed to create rating")
		return nil, fmt.Errorf("failed to create rating: %w", err)
	}

	// Update agent's average rating
	if err := r.updateAgentRating(rating.ToUser); err != nil {
		logrus.WithError(err).Warn("Failed to update agent rating")
	}

	return rating, nil
}

// GetRatingByID retrieves a rating by ID
func (r *RatingRepository) GetRatingByID(ratingID uuid.UUID) (*models.Rating, error) {
	rating := &models.Rating{}
	query := `
		SELECT id, trip_id, from_user, to_user, rating, review, created_at
		FROM Ratings
		WHERE id = $1`

	err := r.db.QueryRow(query, ratingID).Scan(
		&rating.ID, &rating.TripID, &rating.FromUser, &rating.ToUser,
		&rating.Rating, &rating.Review, &rating.CreatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("rating not found")
		}
		logrus.WithError(err).Error("Failed to get rating by ID")
		return nil, fmt.Errorf("failed to get rating: %w", err)
	}

	return rating, nil
}

// GetRatingsByUser retrieves ratings for a user (either given or received)
func (r *RatingRepository) GetRatingsByUser(userID uuid.UUID, asReceiver bool) ([]*models.Rating, error) {
	var query string
	if asReceiver {
		query = `
			SELECT id, trip_id, from_user, to_user, rating, review, created_at
			FROM Ratings
			WHERE to_user = $1
			ORDER BY created_at DESC`
	} else {
		query = `
			SELECT id, trip_id, from_user, to_user, rating, review, created_at
			FROM Ratings
			WHERE from_user = $1
			ORDER BY created_at DESC`
	}

	rows, err := r.db.Query(query, userID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get ratings by user")
		return nil, fmt.Errorf("failed to get ratings: %w", err)
	}
	defer rows.Close()

	var ratings []*models.Rating
	for rows.Next() {
		rating := &models.Rating{}
		err := rows.Scan(
			&rating.ID, &rating.TripID, &rating.FromUser, &rating.ToUser,
			&rating.Rating, &rating.Review, &rating.CreatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan rating row")
			return nil, fmt.Errorf("failed to scan rating: %w", err)
		}
		ratings = append(ratings, rating)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating rating rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ratings, nil
}

// GetRatingsByTrip retrieves all ratings for a trip
func (r *RatingRepository) GetRatingsByTrip(tripID uuid.UUID) ([]*models.Rating, error) {
	query := `
		SELECT id, trip_id, from_user, to_user, rating, review, created_at
		FROM Ratings
		WHERE trip_id = $1
		ORDER BY created_at DESC`

	rows, err := r.db.Query(query, tripID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get ratings by trip")
		return nil, fmt.Errorf("failed to get ratings: %w", err)
	}
	defer rows.Close()

	var ratings []*models.Rating
	for rows.Next() {
		rating := &models.Rating{}
		err := rows.Scan(
			&rating.ID, &rating.TripID, &rating.FromUser, &rating.ToUser,
			&rating.Rating, &rating.Review, &rating.CreatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan rating row")
			return nil, fmt.Errorf("failed to scan rating: %w", err)
		}
		ratings = append(ratings, rating)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating rating rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ratings, nil
}

// GetUserRatingStats retrieves rating statistics for a user
func (r *RatingRepository) GetUserRatingStats(userID uuid.UUID) (map[string]interface{}, error) {
	query := `
		SELECT 
			COUNT(*) as total_ratings,
			AVG(rating) as avg_rating,
			COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
			COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
			COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
			COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
			COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star
		FROM Ratings
		WHERE to_user = $1`

	var totalRatings, fiveStar, fourStar, threeStar, twoStar, oneStar int
	var avgRating sql.NullFloat64

	err := r.db.QueryRow(query, userID).Scan(
		&totalRatings, &avgRating, &fiveStar, &fourStar, &threeStar, &twoStar, &oneStar,
	)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user rating stats")
		return nil, fmt.Errorf("failed to get rating stats: %w", err)
	}

	stats := map[string]interface{}{
		"total_ratings": totalRatings,
		"five_star":     fiveStar,
		"four_star":     fourStar,
		"three_star":    threeStar,
		"two_star":      twoStar,
		"one_star":      oneStar,
	}

	if avgRating.Valid {
		stats["avg_rating"] = avgRating.Float64
	} else {
		stats["avg_rating"] = nil
	}

	return stats, nil
}

// GetTopRatedAgents retrieves top-rated agents
func (r *RatingRepository) GetTopRatedAgents(limit int) ([]map[string]interface{}, error) {
	query := `
		SELECT 
			u.id,
			u.name,
			u.email,
			COUNT(r.rating) as total_ratings,
			AVG(r.rating) as avg_rating
		FROM Users u
		JOIN Ratings r ON u.id = r.to_user
		WHERE u.user_type = 'agent' AND u.is_active = true
		GROUP BY u.id, u.name, u.email
		HAVING COUNT(r.rating) >= 5  -- At least 5 ratings
		ORDER BY AVG(r.rating) DESC, COUNT(r.rating) DESC
		LIMIT $1`

	rows, err := r.db.Query(query, limit)
	if err != nil {
		logrus.WithError(err).Error("Failed to get top rated agents")
		return nil, fmt.Errorf("failed to get top rated agents: %w", err)
	}
	defer rows.Close()

	var agents []map[string]interface{}
	for rows.Next() {
		var userID uuid.UUID
		var name, email string
		var totalRatings int
		var avgRating float64

		err := rows.Scan(&userID, &name, &email, &totalRatings, &avgRating)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan top rated agent row")
			return nil, fmt.Errorf("failed to scan agent: %w", err)
		}

		agent := map[string]interface{}{
			"user_id":       userID,
			"name":          name,
			"email":         email,
			"total_ratings": totalRatings,
			"avg_rating":    avgRating,
		}

		agents = append(agents, agent)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating top rated agent rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return agents, nil
}

// updateAgentRating updates an agent's average rating in the AgentProfiles table
func (r *RatingRepository) updateAgentRating(agentID uuid.UUID) error {
	query := `
		UPDATE AgentProfiles 
		SET rating = (
			SELECT COALESCE(AVG(rating), 0) 
			FROM Ratings 
			WHERE to_user = $1
		)
		WHERE user_id = $1`

	_, err := r.db.Exec(query, agentID)
	if err != nil {
		return fmt.Errorf("failed to update agent rating: %w", err)
	}

	return nil
}

// DeleteRating deletes a rating (soft delete by setting review to null and rating to 0)
func (r *RatingRepository) DeleteRating(ratingID uuid.UUID) error {
	// Get the rating first to know which agent's rating to update
	rating, err := r.GetRatingByID(ratingID)
	if err != nil {
		return err
	}

	query := `DELETE FROM Ratings WHERE id = $1`
	
	result, err := r.db.Exec(query, ratingID)
	if err != nil {
		logrus.WithError(err).Error("Failed to delete rating")
		return fmt.Errorf("failed to delete rating: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("rating not found")
	}

	// Update agent's average rating
	if err := r.updateAgentRating(rating.ToUser); err != nil {
		logrus.WithError(err).Warn("Failed to update agent rating after deletion")
	}

	return nil
}

// GetRecentRatings retrieves recent ratings across the system
func (r *RatingRepository) GetRecentRatings(limit int) ([]*models.Rating, error) {
	query := `
		SELECT id, trip_id, from_user, to_user, rating, review, created_at
		FROM Ratings
		ORDER BY created_at DESC
		LIMIT $1`

	rows, err := r.db.Query(query, limit)
	if err != nil {
		logrus.WithError(err).Error("Failed to get recent ratings")
		return nil, fmt.Errorf("failed to get recent ratings: %w", err)
	}
	defer rows.Close()

	var ratings []*models.Rating
	for rows.Next() {
		rating := &models.Rating{}
		err := rows.Scan(
			&rating.ID, &rating.TripID, &rating.FromUser, &rating.ToUser,
			&rating.Rating, &rating.Review, &rating.CreatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan recent rating row")
			return nil, fmt.Errorf("failed to scan rating: %w", err)
		}
		ratings = append(ratings, rating)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating recent rating rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ratings, nil
}

package repository

import (
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"
)

type MarkerSettingsRepository struct {
	db *database.DB
}

func NewMarkerSettingsRepository(db *database.DB) *MarkerSettingsRepository {
	return &MarkerSettingsRepository{db: db}
}

// GetMarkerSettings retrieves all custom marker settings for a user
func (r *MarkerSettingsRepository) GetMarkerSettings(userID uuid.UUID) ([]*models.CustomMarkerSetting, error) {
	query := `
		SELECT id, user_id, marker_type, icon_text, icon_image_url, color, size, 
		       border_color, border_width, enabled, created_at, updated_at
		FROM custom_marker_settings 
		WHERE user_id = $1 AND enabled = true
		ORDER BY marker_type`

	rows, err := r.db.Query(query, userID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get marker settings")
		return nil, fmt.Errorf("failed to get marker settings: %w", err)
	}
	defer rows.Close()

	var settings []*models.CustomMarkerSetting
	for rows.Next() {
		var setting models.CustomMarkerSetting
		err := rows.Scan(
			&setting.ID, &setting.UserID, &setting.MarkerType,
			&setting.IconText, &setting.IconImageURL, &setting.Color,
			&setting.Size, &setting.BorderColor, &setting.BorderWidth,
			&setting.Enabled, &setting.CreatedAt, &setting.UpdatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan marker setting row")
			continue
		}
		settings = append(settings, &setting)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating marker setting rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return settings, nil
}

// GetMarkerSettingByType retrieves a specific marker setting for a user and marker type
func (r *MarkerSettingsRepository) GetMarkerSettingByType(userID uuid.UUID, markerType string) (*models.CustomMarkerSetting, error) {
	query := `
		SELECT id, user_id, marker_type, icon_text, icon_image_url, color, size, 
		       border_color, border_width, enabled, created_at, updated_at
		FROM custom_marker_settings 
		WHERE user_id = $1 AND marker_type = $2`

	var setting models.CustomMarkerSetting
	err := r.db.QueryRow(query, userID, markerType).Scan(
		&setting.ID, &setting.UserID, &setting.MarkerType,
		&setting.IconText, &setting.IconImageURL, &setting.Color,
		&setting.Size, &setting.BorderColor, &setting.BorderWidth,
		&setting.Enabled, &setting.CreatedAt, &setting.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // No custom setting found
		}
		logrus.WithError(err).Error("Failed to get marker setting by type")
		return nil, fmt.Errorf("failed to get marker setting: %w", err)
	}

	return &setting, nil
}

// SaveMarkerSetting creates or updates a custom marker setting
func (r *MarkerSettingsRepository) SaveMarkerSetting(setting *models.CustomMarkerSetting) error {
	query := `
		INSERT INTO custom_marker_settings (user_id, marker_type, icon_text, icon_image_url, 
		                                   color, size, border_color, border_width, enabled)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT (user_id, marker_type) 
		DO UPDATE SET 
			icon_text = EXCLUDED.icon_text,
			icon_image_url = EXCLUDED.icon_image_url,
			color = EXCLUDED.color,
			size = EXCLUDED.size,
			border_color = EXCLUDED.border_color,
			border_width = EXCLUDED.border_width,
			enabled = EXCLUDED.enabled,
			updated_at = CURRENT_TIMESTAMP
		RETURNING id, created_at, updated_at`

	err := r.db.QueryRow(query,
		setting.UserID, setting.MarkerType, setting.IconText, setting.IconImageURL,
		setting.Color, setting.Size, setting.BorderColor, setting.BorderWidth,
		setting.Enabled,
	).Scan(&setting.ID, &setting.CreatedAt, &setting.UpdatedAt)

	if err != nil {
		logrus.WithError(err).Error("Failed to save marker setting")
		return fmt.Errorf("failed to save marker setting: %w", err)
	}

	return nil
}

// DeleteMarkerSetting deletes a custom marker setting
func (r *MarkerSettingsRepository) DeleteMarkerSetting(userID uuid.UUID, markerType string) error {
	query := `DELETE FROM custom_marker_settings WHERE user_id = $1 AND marker_type = $2`

	result, err := r.db.Exec(query, userID, markerType)
	if err != nil {
		logrus.WithError(err).Error("Failed to delete marker setting")
		return fmt.Errorf("failed to delete marker setting: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("marker setting not found")
	}

	return nil
}

package repository

import (
	"database/sql"
	"fmt"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type AgentRepository struct {
	db *database.DB
}

func NewAgentRepository(db *database.DB) *AgentRepository {
	return &AgentRepository{db: db}
}

// CreateAgentProfile creates a new agent profile
func (r *AgentRepository) CreateAgentProfile(userID uuid.UUID, vehicleType, vehicleNo, licenseNo *string) (*models.AgentProfile, error) {
	profile := &models.AgentProfile{
		UserID:      userID,
		VehicleType: vehicleType,
		VehicleNo:   vehicleNo,
		LicenseNo:   licenseNo,
		Rating:      0.0,
		IsAvailable: false,
	}

	query := `
		INSERT INTO AgentProfiles (user_id, vehicle_type, vehicle_no, license_no, rating, is_available)
		VALUES ($1, $2, $3, $4, $5, $6)`

	_, err := r.db.Exec(query, profile.UserID, profile.VehicleType, profile.VehicleNo, 
		profile.LicenseNo, profile.Rating, profile.IsAvailable)
	if err != nil {
		logrus.WithError(err).Error("Failed to create agent profile")
		return nil, fmt.Errorf("failed to create agent profile: %w", err)
	}

	return profile, nil
}

// GetAgentProfile retrieves an agent profile by user ID
func (r *AgentRepository) GetAgentProfile(userID uuid.UUID) (*models.AgentProfile, error) {
	profile := &models.AgentProfile{}
	query := `
		SELECT user_id, vehicle_type, vehicle_no, license_no, rating, is_available
		FROM AgentProfiles
		WHERE user_id = $1`

	err := r.db.QueryRow(query, userID).Scan(
		&profile.UserID, &profile.VehicleType, &profile.VehicleNo,
		&profile.LicenseNo, &profile.Rating, &profile.IsAvailable,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("agent profile not found")
		}
		logrus.WithError(err).Error("Failed to get agent profile")
		return nil, fmt.Errorf("failed to get agent profile: %w", err)
	}

	return profile, nil
}

// UpdateAgentProfile updates an agent profile
func (r *AgentRepository) UpdateAgentProfile(userID uuid.UUID, vehicleType, vehicleNo, licenseNo *string) (*models.AgentProfile, error) {
	query := `
		UPDATE AgentProfiles
		SET vehicle_type = $2, vehicle_no = $3, license_no = $4
		WHERE user_id = $1
		RETURNING user_id, vehicle_type, vehicle_no, license_no, rating, is_available`

	profile := &models.AgentProfile{}
	err := r.db.QueryRow(query, userID, vehicleType, vehicleNo, licenseNo).Scan(
		&profile.UserID, &profile.VehicleType, &profile.VehicleNo,
		&profile.LicenseNo, &profile.Rating, &profile.IsAvailable,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("agent profile not found")
		}
		logrus.WithError(err).Error("Failed to update agent profile")
		return nil, fmt.Errorf("failed to update agent profile: %w", err)
	}

	return profile, nil
}

// UpdateAgentAvailability updates an agent's availability status
func (r *AgentRepository) UpdateAgentAvailability(userID uuid.UUID, isAvailable bool) error {
	query := `UPDATE AgentProfiles SET is_available = $2 WHERE user_id = $1`
	
	result, err := r.db.Exec(query, userID, isAvailable)
	if err != nil {
		logrus.WithError(err).Error("Failed to update agent availability")
		return fmt.Errorf("failed to update agent availability: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("agent profile not found")
	}

	return nil
}

// GetAvailableAgents retrieves all available agents
func (r *AgentRepository) GetAvailableAgents() ([]*models.AgentProfile, error) {
	query := `
		SELECT ap.user_id, ap.vehicle_type, ap.vehicle_no, ap.license_no, ap.rating, ap.is_available
		FROM AgentProfiles ap
		JOIN Users u ON ap.user_id = u.id
		WHERE ap.is_available = true AND u.is_active = true
		ORDER BY ap.rating DESC`

	rows, err := r.db.Query(query)
	if err != nil {
		logrus.WithError(err).Error("Failed to get available agents")
		return nil, fmt.Errorf("failed to get available agents: %w", err)
	}
	defer rows.Close()

	var agents []*models.AgentProfile
	for rows.Next() {
		agent := &models.AgentProfile{}
		err := rows.Scan(
			&agent.UserID, &agent.VehicleType, &agent.VehicleNo,
			&agent.LicenseNo, &agent.Rating, &agent.IsAvailable,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan agent profile row")
			return nil, fmt.Errorf("failed to scan agent profile: %w", err)
		}
		agents = append(agents, agent)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating agent profile rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return agents, nil
}

// GetAgentsByVehicleType retrieves agents by vehicle type
func (r *AgentRepository) GetAgentsByVehicleType(vehicleType string) ([]*models.AgentProfile, error) {
	query := `
		SELECT ap.user_id, ap.vehicle_type, ap.vehicle_no, ap.license_no, ap.rating, ap.is_available
		FROM AgentProfiles ap
		JOIN Users u ON ap.user_id = u.id
		WHERE ap.vehicle_type = $1 AND u.is_active = true
		ORDER BY ap.rating DESC`

	rows, err := r.db.Query(query, vehicleType)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agents by vehicle type")
		return nil, fmt.Errorf("failed to get agents by vehicle type: %w", err)
	}
	defer rows.Close()

	var agents []*models.AgentProfile
	for rows.Next() {
		agent := &models.AgentProfile{}
		err := rows.Scan(
			&agent.UserID, &agent.VehicleType, &agent.VehicleNo,
			&agent.LicenseNo, &agent.Rating, &agent.IsAvailable,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan agent profile row")
			return nil, fmt.Errorf("failed to scan agent profile: %w", err)
		}
		agents = append(agents, agent)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating agent profile rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return agents, nil
}

// UpdateAgentRating updates an agent's rating
func (r *AgentRepository) UpdateAgentRating(userID uuid.UUID, newRating float64) error {
	query := `UPDATE AgentProfiles SET rating = $2 WHERE user_id = $1`
	
	result, err := r.db.Exec(query, userID, newRating)
	if err != nil {
		logrus.WithError(err).Error("Failed to update agent rating")
		return fmt.Errorf("failed to update agent rating: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("agent profile not found")
	}

	return nil
}

// GetAgentStats retrieves statistics for an agent
func (r *AgentRepository) GetAgentStats(userID uuid.UUID) (map[string]interface{}, error) {
	query := `
		SELECT 
			COUNT(t.trip_id) as total_trips,
			COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_trips,
			COUNT(CASE WHEN t.status = 'cancelled' THEN 1 END) as cancelled_trips,
			AVG(CASE WHEN r.rating IS NOT NULL THEN r.rating END) as avg_rating,
			COUNT(r.rating) as total_ratings
		FROM Users u
		LEFT JOIN Trips t ON u.id = t.agent_id
		LEFT JOIN Ratings r ON t.trip_id = r.trip_id AND r.to_user = u.id
		WHERE u.id = $1 AND u.user_type = 'agent'
		GROUP BY u.id`

	var totalTrips, completedTrips, cancelledTrips, totalRatings int
	var avgRating sql.NullFloat64

	err := r.db.QueryRow(query, userID).Scan(
		&totalTrips, &completedTrips, &cancelledTrips, &avgRating, &totalRatings,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			// Agent exists but has no trips/ratings
			return map[string]interface{}{
				"total_trips":     0,
				"completed_trips": 0,
				"cancelled_trips": 0,
				"avg_rating":      nil,
				"total_ratings":   0,
				"completion_rate": 0.0,
			}, nil
		}
		logrus.WithError(err).Error("Failed to get agent stats")
		return nil, fmt.Errorf("failed to get agent stats: %w", err)
	}

	completionRate := 0.0
	if totalTrips > 0 {
		completionRate = float64(completedTrips) / float64(totalTrips) * 100
	}

	stats := map[string]interface{}{
		"total_trips":     totalTrips,
		"completed_trips": completedTrips,
		"cancelled_trips": cancelledTrips,
		"total_ratings":   totalRatings,
		"completion_rate": completionRate,
	}

	if avgRating.Valid {
		stats["avg_rating"] = avgRating.Float64
	} else {
		stats["avg_rating"] = nil
	}

	return stats, nil
}

// GetNearbyAgents retrieves agents within a certain radius of a location
func (r *AgentRepository) GetNearbyAgents(latitude, longitude, radiusKm float64) ([]*models.AgentProfile, error) {
	query := `
		SELECT DISTINCT ap.user_id, ap.vehicle_type, ap.vehicle_no, ap.license_no, ap.rating, ap.is_available
		FROM AgentProfiles ap
		JOIN Users u ON ap.user_id = u.id
		JOIN LiveLocations ll ON ap.user_id = ll.agent_id
		WHERE ap.is_available = true 
		AND u.is_active = true
		AND ST_DWithin(
			ll.geom,
			ST_GeomFromText('POINT(%f %f)', 4326),
			%f * 1000  -- Convert km to meters
		)
		ORDER BY ST_Distance(ll.geom, ST_GeomFromText('POINT(%f %f)', 4326)) ASC`

	formattedQuery := fmt.Sprintf(query, longitude, latitude, radiusKm, longitude, latitude)

	rows, err := r.db.Query(formattedQuery)
	if err != nil {
		logrus.WithError(err).Error("Failed to get nearby agents")
		return nil, fmt.Errorf("failed to get nearby agents: %w", err)
	}
	defer rows.Close()

	var agents []*models.AgentProfile
	for rows.Next() {
		agent := &models.AgentProfile{}
		err := rows.Scan(
			&agent.UserID, &agent.VehicleType, &agent.VehicleNo,
			&agent.LicenseNo, &agent.Rating, &agent.IsAvailable,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan nearby agent row")
			return nil, fmt.Errorf("failed to scan agent profile: %w", err)
		}
		agents = append(agents, agent)
	}

	if err = rows.Err(); err != nil {
		logrus.WithError(err).Error("Error iterating nearby agent rows")
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return agents, nil
}

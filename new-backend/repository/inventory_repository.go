package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type InventoryRepository struct {
	db *database.DB
}

func NewInventoryRepository(db *database.DB) *InventoryRepository {
	return &InventoryRepository{db: db}
}

// GetInventoryDashboard retrieves inventory dashboard data
func (r *InventoryRepository) GetInventoryDashboard(warehouseID *uuid.UUID, category string, lowStockOnly bool) (map[string]interface{}, error) {
	query := `
		SELECT 
			inventory_id, warehouse_name, warehouse_code, sku, product_name, category,
			current_stock, reserved_stock, available_stock, reorder_point, status,
			expiry_date, total_alerts, low_stock_alerts, out_of_stock_alerts,
			expiry_alerts, last_restocked_at, updated_at
		FROM inventory_dashboard
		WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	if warehouseID != nil {
		// We need to join with warehouses to filter by warehouse_id
		query = `
			SELECT 
				id.inventory_id, id.warehouse_name, id.warehouse_code, id.sku, id.product_name, id.category,
				id.current_stock, id.reserved_stock, id.available_stock, id.reorder_point, id.status,
				id.expiry_date, id.total_alerts, id.low_stock_alerts, id.out_of_stock_alerts,
				id.expiry_alerts, id.last_restocked_at, id.updated_at
			FROM inventory_dashboard id
			JOIN Inventory i ON id.inventory_id = i.id
			WHERE i.warehouse_id = $1`
		args = append(args, *warehouseID)
		argIndex++
	}

	if category != "" {
		query += fmt.Sprintf(" AND category = $%d", argIndex)
		args = append(args, category)
		argIndex++
	}

	if lowStockOnly {
		query += " AND available_stock <= reorder_point"
	}

	query += " ORDER BY updated_at DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get inventory dashboard data")
		return nil, fmt.Errorf("failed to get inventory dashboard data: %w", err)
	}
	defer rows.Close()

	var inventoryItems []map[string]interface{}
	totalItems := 0
	lowStockItems := 0
	outOfStockItems := 0
	totalAlerts := 0

	for rows.Next() {
		var inventoryID, warehouseName, warehouseCode, sku, productName, category, status string
		var currentStock, reservedStock, availableStock, reorderPoint int
		var totalItemAlerts, lowStockAlerts, outOfStockAlerts, expiryAlerts int
		var expiryDate sql.NullTime
		var lastRestockedAt, updatedAt sql.NullTime

		err := rows.Scan(
			&inventoryID, &warehouseName, &warehouseCode, &sku, &productName, &category,
			&currentStock, &reservedStock, &availableStock, &reorderPoint, &status,
			&expiryDate, &totalItemAlerts, &lowStockAlerts, &outOfStockAlerts,
			&expiryAlerts, &lastRestockedAt, &updatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan inventory dashboard row")
			continue
		}

		item := map[string]interface{}{
			"inventory_id":       inventoryID,
			"warehouse_name":     warehouseName,
			"warehouse_code":     warehouseCode,
			"sku":                sku,
			"product_name":       productName,
			"category":           category,
			"current_stock":      currentStock,
			"reserved_stock":     reservedStock,
			"available_stock":    availableStock,
			"reorder_point":      reorderPoint,
			"status":             status,
			"total_alerts":       totalItemAlerts,
			"low_stock_alerts":   lowStockAlerts,
			"out_of_stock_alerts": outOfStockAlerts,
			"expiry_alerts":      expiryAlerts,
		}

		if expiryDate.Valid {
			item["expiry_date"] = expiryDate.Time.Format("2006-01-02")
		}

		if lastRestockedAt.Valid {
			item["last_restocked_at"] = lastRestockedAt.Time
		}

		if updatedAt.Valid {
			item["updated_at"] = updatedAt.Time
		}

		inventoryItems = append(inventoryItems, item)
		totalItems++
		totalAlerts += totalItemAlerts

		if availableStock <= reorderPoint {
			lowStockItems++
		}
		if availableStock <= 0 {
			outOfStockItems++
		}
	}

	dashboard := map[string]interface{}{
		"summary": map[string]interface{}{
			"total_items":       totalItems,
			"low_stock_items":   lowStockItems,
			"out_of_stock_items": outOfStockItems,
			"total_alerts":      totalAlerts,
		},
		"inventory_items": inventoryItems,
		"filters": map[string]interface{}{
			"warehouse_id":   warehouseID,
			"category":       category,
			"low_stock_only": lowStockOnly,
		},
		"last_updated": time.Now(),
	}

	return dashboard, nil
}

// GetInventoryAlerts retrieves inventory alerts with filters
func (r *InventoryRepository) GetInventoryAlerts(warehouseID *uuid.UUID, alertType, severity string, unresolvedOnly bool) ([]*models.InventoryAlert, error) {
	query := `
		SELECT 
			ia.id, ia.inventory_id, ia.alert_type, ia.severity, ia.current_stock,
			ia.threshold_value, ia.message, ia.resolved, ia.resolved_by,
			ia.resolved_at, ia.resolution_note, ia.created_at,
			p.name as product_name, p.sku, w.name as warehouse_name
		FROM InventoryAlerts ia
		JOIN Inventory i ON ia.inventory_id = i.id
		JOIN Products p ON i.product_id = p.id
		JOIN Warehouses w ON i.warehouse_id = w.id
		WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	if warehouseID != nil {
		query += fmt.Sprintf(" AND i.warehouse_id = $%d", argIndex)
		args = append(args, *warehouseID)
		argIndex++
	}

	if alertType != "" {
		query += fmt.Sprintf(" AND ia.alert_type = $%d", argIndex)
		args = append(args, alertType)
		argIndex++
	}

	if severity != "" {
		query += fmt.Sprintf(" AND ia.severity = $%d", argIndex)
		args = append(args, severity)
		argIndex++
	}

	if unresolvedOnly {
		query += " AND ia.resolved = FALSE"
	}

	query += " ORDER BY ia.created_at DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get inventory alerts")
		return nil, fmt.Errorf("failed to get inventory alerts: %w", err)
	}
	defer rows.Close()

	var alerts []*models.InventoryAlert
	for rows.Next() {
		alert := &models.InventoryAlert{}
		var productName, sku, warehouseName string

		err := rows.Scan(
			&alert.ID, &alert.InventoryID, &alert.AlertType, &alert.Severity,
			&alert.CurrentStock, &alert.ThresholdValue, &alert.Message,
			&alert.Resolved, &alert.ResolvedBy, &alert.ResolvedAt,
			&alert.ResolutionNote, &alert.CreatedAt,
			&productName, &sku, &warehouseName,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan inventory alert row")
			continue
		}

		alert.ProductName = productName
		alert.SKU = sku
		alert.WarehouseName = warehouseName

		alerts = append(alerts, alert)
	}

	return alerts, nil
}

// RecordInventoryMovement records an inventory movement
func (r *InventoryRepository) RecordInventoryMovement(movement *models.InventoryMovement) error {
	movement.ID = uuid.New()
	movement.CreatedAt = time.Now()

	query := `
		INSERT INTO InventoryMovements (
			id, inventory_id, movement_type, quantity, reference_type,
			reference_id, reason, cost_per_unit, total_cost, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`

	_, err := r.db.Exec(query,
		movement.ID, movement.InventoryID, movement.MovementType, movement.Quantity,
		movement.ReferenceType, movement.ReferenceID, movement.Reason,
		movement.CostPerUnit, movement.TotalCost, movement.CreatedAt,
	)

	if err != nil {
		logrus.WithError(err).Error("Failed to record inventory movement")
		return fmt.Errorf("failed to record inventory movement: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"movement_id":   movement.ID,
		"inventory_id":  movement.InventoryID,
		"movement_type": movement.MovementType,
		"quantity":      movement.Quantity,
	}).Info("Inventory movement recorded successfully")

	return nil
}

// UpdateInventory updates inventory levels
func (r *InventoryRepository) UpdateInventory(inventoryID uuid.UUID, updateData *models.InventoryUpdate) error {
	setParts := []string{"updated_at = CURRENT_TIMESTAMP"}
	args := []interface{}{}
	argIndex := 1

	if updateData.CurrentStock != nil {
		setParts = append(setParts, fmt.Sprintf("current_stock = $%d", argIndex))
		args = append(args, *updateData.CurrentStock)
		argIndex++
	}

	if updateData.ReservedStock != nil {
		setParts = append(setParts, fmt.Sprintf("reserved_stock = $%d", argIndex))
		args = append(args, *updateData.ReservedStock)
		argIndex++
	}

	if updateData.ReorderPoint != nil {
		setParts = append(setParts, fmt.Sprintf("reorder_point = $%d", argIndex))
		args = append(args, *updateData.ReorderPoint)
		argIndex++
	}

	if updateData.MaxStockLevel != nil {
		setParts = append(setParts, fmt.Sprintf("max_stock_level = $%d", argIndex))
		args = append(args, *updateData.MaxStockLevel)
		argIndex++
	}

	if updateData.UnitCost != nil {
		setParts = append(setParts, fmt.Sprintf("unit_cost = $%d", argIndex))
		args = append(args, *updateData.UnitCost)
		argIndex++
	}

	if updateData.ExpiryDate != nil {
		setParts = append(setParts, fmt.Sprintf("expiry_date = $%d", argIndex))
		args = append(args, *updateData.ExpiryDate)
		argIndex++
	}

	if updateData.BatchNumber != nil {
		setParts = append(setParts, fmt.Sprintf("batch_number = $%d", argIndex))
		args = append(args, *updateData.BatchNumber)
		argIndex++
	}

	if len(setParts) == 1 { // Only updated_at
		return fmt.Errorf("no fields to update")
	}

	query := fmt.Sprintf("UPDATE Inventory SET %s WHERE id = $%d", 
		fmt.Sprintf("%s", setParts), argIndex)
	args = append(args, inventoryID)

	result, err := r.db.Exec(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to update inventory")
		return fmt.Errorf("failed to update inventory: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("inventory not found")
	}

	logrus.WithField("inventory_id", inventoryID).Info("Inventory updated successfully")
	return nil
}

// GetInventoryMovements retrieves inventory movement history
func (r *InventoryRepository) GetInventoryMovements(inventoryID uuid.UUID, days int) ([]*models.InventoryMovement, error) {
	query := `
		SELECT 
			id, inventory_id, movement_type, quantity, reference_type,
			reference_id, reason, cost_per_unit, total_cost, created_at
		FROM InventoryMovements
		WHERE inventory_id = $1 AND created_at >= NOW() - INTERVAL '%d days'
		ORDER BY created_at DESC`

	formattedQuery := fmt.Sprintf(query, days)
	rows, err := r.db.Query(formattedQuery, inventoryID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get inventory movements")
		return nil, fmt.Errorf("failed to get inventory movements: %w", err)
	}
	defer rows.Close()

	var movements []*models.InventoryMovement
	for rows.Next() {
		movement := &models.InventoryMovement{}

		err := rows.Scan(
			&movement.ID, &movement.InventoryID, &movement.MovementType,
			&movement.Quantity, &movement.ReferenceType, &movement.ReferenceID,
			&movement.Reason, &movement.CostPerUnit, &movement.TotalCost,
			&movement.CreatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan inventory movement row")
			continue
		}

		movements = append(movements, movement)
	}

	return movements, nil
}

// CreateProduct creates a new product
func (r *InventoryRepository) CreateProduct(product *models.Product) error {
	product.ID = uuid.New()
	product.CreatedAt = time.Now()
	product.UpdatedAt = time.Now()

	// Convert JSON fields
	var dimensionsJSON, supplierInfoJSON, storageRequirementsJSON sql.NullString

	if product.Dimensions != nil {
		data, err := json.Marshal(product.Dimensions)
		if err != nil {
			return fmt.Errorf("failed to marshal dimensions: %w", err)
		}
		dimensionsJSON = sql.NullString{String: string(data), Valid: true}
	}

	if product.SupplierInfo != nil {
		data, err := json.Marshal(product.SupplierInfo)
		if err != nil {
			return fmt.Errorf("failed to marshal supplier info: %w", err)
		}
		supplierInfoJSON = sql.NullString{String: string(data), Valid: true}
	}

	if product.StorageRequirements != nil {
		data, err := json.Marshal(product.StorageRequirements)
		if err != nil {
			return fmt.Errorf("failed to marshal storage requirements: %w", err)
		}
		storageRequirementsJSON = sql.NullString{String: string(data), Valid: true}
	}

	query := `
		INSERT INTO Products (
			id, sku, name, description, category, unit_of_measure,
			unit_price, weight_kg, dimensions, supplier_info,
			shelf_life_days, storage_requirements, is_active,
			created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`

	_, err := r.db.Exec(query,
		product.ID, product.SKU, product.Name, product.Description,
		product.Category, product.UnitOfMeasure, product.UnitPrice,
		product.WeightKg, dimensionsJSON, supplierInfoJSON,
		product.ShelfLifeDays, storageRequirementsJSON, product.IsActive,
		product.CreatedAt, product.UpdatedAt,
	)

	if err != nil {
		logrus.WithError(err).Error("Failed to create product")
		return fmt.Errorf("failed to create product: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"product_id": product.ID,
		"sku":        product.SKU,
		"name":       product.Name,
	}).Info("Product created successfully")

	return nil
}

// GetProducts retrieves products with optional filters
func (r *InventoryRepository) GetProducts(category string, activeOnly bool) ([]*models.Product, error) {
	query := `
		SELECT 
			id, sku, name, description, category, unit_of_measure,
			unit_price, weight_kg, dimensions, supplier_info,
			shelf_life_days, storage_requirements, is_active,
			created_at, updated_at
		FROM Products
		WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	if category != "" {
		query += fmt.Sprintf(" AND category = $%d", argIndex)
		args = append(args, category)
		argIndex++
	}

	if activeOnly {
		query += " AND is_active = TRUE"
	}

	query += " ORDER BY name ASC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get products")
		return nil, fmt.Errorf("failed to get products: %w", err)
	}
	defer rows.Close()

	var products []*models.Product
	for rows.Next() {
		product := &models.Product{}
		var dimensionsJSON, supplierInfoJSON, storageRequirementsJSON sql.NullString

		err := rows.Scan(
			&product.ID, &product.SKU, &product.Name, &product.Description,
			&product.Category, &product.UnitOfMeasure, &product.UnitPrice,
			&product.WeightKg, &dimensionsJSON, &supplierInfoJSON,
			&product.ShelfLifeDays, &storageRequirementsJSON, &product.IsActive,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan product row")
			continue
		}

		// Parse JSON fields
		if dimensionsJSON.Valid {
			if err := json.Unmarshal([]byte(dimensionsJSON.String), &product.Dimensions); err != nil {
				logrus.WithError(err).Warn("Failed to parse dimensions JSON")
			}
		}

		if supplierInfoJSON.Valid {
			if err := json.Unmarshal([]byte(supplierInfoJSON.String), &product.SupplierInfo); err != nil {
				logrus.WithError(err).Warn("Failed to parse supplier info JSON")
			}
		}

		if storageRequirementsJSON.Valid {
			if err := json.Unmarshal([]byte(storageRequirementsJSON.String), &product.StorageRequirements); err != nil {
				logrus.WithError(err).Warn("Failed to parse storage requirements JSON")
			}
		}

		products = append(products, product)
	}

	return products, nil
}

// ResolveInventoryAlert marks an inventory alert as resolved
func (r *InventoryRepository) ResolveInventoryAlert(alertID uuid.UUID, resolutionNote string) error {
	query := `
		UPDATE InventoryAlerts 
		SET resolved = TRUE, resolved_at = NOW(), resolution_note = $2
		WHERE id = $1`

	result, err := r.db.Exec(query, alertID, resolutionNote)
	if err != nil {
		logrus.WithError(err).Error("Failed to resolve inventory alert")
		return fmt.Errorf("failed to resolve inventory alert: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("inventory alert not found")
	}

	logrus.WithField("alert_id", alertID).Info("Inventory alert resolved successfully")
	return nil
}

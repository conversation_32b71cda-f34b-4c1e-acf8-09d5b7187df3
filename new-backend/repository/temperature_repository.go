package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"delivery-tracking-backend/database"
	"delivery-tracking-backend/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type TemperatureRepository struct {
	db *database.DB
}

func NewTemperatureRepository(db *database.DB) *TemperatureRepository {
	return &TemperatureRepository{db: db}
}

// CreateTemperatureReading creates a new temperature reading
func (r *TemperatureRepository) CreateTemperatureReading(reading *models.TemperatureReading) error {
	reading.ID = uuid.New()
	reading.CreatedAt = time.Now()

	query := `
		INSERT INTO TemperatureReadings (
			id, trip_id, agent_id, sensor_id, temperature_c, humidity_pct,
			battery_level, signal_strength, location, recorded_at, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, ST_SetSRID(ST_MakePoint($9, $10), 4326), $11, $12)`

	_, err := r.db.Exec(query,
		reading.ID, reading.TripID, reading.AgentID, reading.SensorID,
		reading.TemperatureC, reading.HumidityPct, reading.BatteryLevel,
		reading.SignalStrength, reading.Location.Longitude, reading.Location.Latitude,
		reading.RecordedAt, reading.CreatedAt,
	)

	if err != nil {
		logrus.WithError(err).Error("Failed to create temperature reading")
		return fmt.Errorf("failed to create temperature reading: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"reading_id":  reading.ID,
		"trip_id":     reading.TripID,
		"temperature": reading.TemperatureC,
	}).Info("Temperature reading created successfully")

	return nil
}

// GetTemperatureHistory retrieves temperature readings for a trip within the specified hours
func (r *TemperatureRepository) GetTemperatureHistory(tripID uuid.UUID, hours int) ([]*models.TemperatureReading, error) {
	query := `
		SELECT 
			id, trip_id, agent_id, sensor_id, temperature_c, humidity_pct,
			battery_level, signal_strength, ST_X(location) as longitude, 
			ST_Y(location) as latitude, recorded_at, created_at
		FROM TemperatureReadings
		WHERE trip_id = $1 AND recorded_at >= NOW() - INTERVAL '%d hours'
		ORDER BY recorded_at DESC`

	formattedQuery := fmt.Sprintf(query, hours)
	rows, err := r.db.Query(formattedQuery, tripID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get temperature history")
		return nil, fmt.Errorf("failed to get temperature history: %w", err)
	}
	defer rows.Close()

	var readings []*models.TemperatureReading
	for rows.Next() {
		reading := &models.TemperatureReading{}
		var longitude, latitude float64

		err := rows.Scan(
			&reading.ID, &reading.TripID, &reading.AgentID, &reading.SensorID,
			&reading.TemperatureC, &reading.HumidityPct, &reading.BatteryLevel,
			&reading.SignalStrength, &longitude, &latitude,
			&reading.RecordedAt, &reading.CreatedAt,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan temperature reading")
			continue
		}

		reading.Location = models.Point{Latitude: latitude, Longitude: longitude}
		readings = append(readings, reading)
	}

	return readings, nil
}

// GetTemperatureAlerts retrieves temperature alerts based on filters
func (r *TemperatureRepository) GetTemperatureAlerts(tripID, agentID *uuid.UUID, severity string, unresolvedOnly bool) ([]*models.TemperatureAlert, error) {
	query := `
		SELECT 
			ta.id, ta.trip_id, ta.agent_id, ta.alert_type, ta.temperature_c,
			ta.humidity_pct, ta.threshold_min, ta.threshold_max, ta.severity,
			ST_X(ta.location) as longitude, ST_Y(ta.location) as latitude,
			ta.resolved_at, ta.created_at, u.name as agent_name
		FROM TemperatureAlerts ta
		LEFT JOIN Users u ON ta.agent_id = u.id
		WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	if tripID != nil {
		query += fmt.Sprintf(" AND ta.trip_id = $%d", argIndex)
		args = append(args, *tripID)
		argIndex++
	}

	if agentID != nil {
		query += fmt.Sprintf(" AND ta.agent_id = $%d", argIndex)
		args = append(args, *agentID)
		argIndex++
	}

	if severity != "" {
		query += fmt.Sprintf(" AND ta.severity = $%d", argIndex)
		args = append(args, severity)
		argIndex++
	}

	if unresolvedOnly {
		query += " AND ta.resolved_at IS NULL"
	}

	query += " ORDER BY ta.created_at DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get temperature alerts")
		return nil, fmt.Errorf("failed to get temperature alerts: %w", err)
	}
	defer rows.Close()

	var alerts []*models.TemperatureAlert
	for rows.Next() {
		alert := &models.TemperatureAlert{}
		var longitude, latitude sql.NullFloat64
		var agentName sql.NullString

		err := rows.Scan(
			&alert.ID, &alert.TripID, &alert.AgentID, &alert.AlertType,
			&alert.TemperatureC, &alert.HumidityPct, &alert.ThresholdMin,
			&alert.ThresholdMax, &alert.Severity, &longitude, &latitude,
			&alert.ResolvedAt, &alert.CreatedAt, &agentName,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan temperature alert")
			continue
		}

		if longitude.Valid && latitude.Valid {
			alert.Location = &models.Point{Latitude: latitude.Float64, Longitude: longitude.Float64}
		}

		if agentName.Valid {
			alert.AgentName = agentName.String
		}

		alerts = append(alerts, alert)
	}

	return alerts, nil
}

// ResolveTemperatureAlert marks a temperature alert as resolved
func (r *TemperatureRepository) ResolveTemperatureAlert(alertID uuid.UUID, resolutionNote string) error {
	query := `
		UPDATE TemperatureAlerts 
		SET resolved_at = NOW(), resolution_note = $2
		WHERE id = $1`

	result, err := r.db.Exec(query, alertID, resolutionNote)
	if err != nil {
		logrus.WithError(err).Error("Failed to resolve temperature alert")
		return fmt.Errorf("failed to resolve temperature alert: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("temperature alert not found")
	}

	logrus.WithField("alert_id", alertID).Info("Temperature alert resolved successfully")
	return nil
}

// GetTemperatureDashboard retrieves temperature monitoring dashboard data
func (r *TemperatureRepository) GetTemperatureDashboard(agentID *uuid.UUID) (map[string]interface{}, error) {
	query := `
		SELECT 
			trip_id, customer_id, agent_id, agent_name, trip_status,
			temperature_requirements, is_temperature_sensitive,
			current_temperature, current_humidity, last_reading_time,
			total_alerts, critical_alerts, high_alerts, unresolved_alerts
		FROM temperature_monitoring_dashboard`

	args := []interface{}{}
	if agentID != nil {
		query += " WHERE agent_id = $1"
		args = append(args, *agentID)
	}

	query += " ORDER BY last_reading_time DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logrus.WithError(err).Error("Failed to get temperature dashboard data")
		return nil, fmt.Errorf("failed to get temperature dashboard data: %w", err)
	}
	defer rows.Close()

	var trips []map[string]interface{}
	totalTrips := 0
	totalAlerts := 0
	criticalAlerts := 0
	unresolvedAlerts := 0

	for rows.Next() {
		var tripID, customerID, agentIDStr, agentName, tripStatus string
		var tempRequirements sql.NullString
		var isTemperatureSensitive bool
		var currentTemp, currentHumidity sql.NullFloat64
		var lastReadingTime sql.NullTime
		var tripTotalAlerts, tripCriticalAlerts, tripHighAlerts, tripUnresolvedAlerts int

		err := rows.Scan(
			&tripID, &customerID, &agentIDStr, &agentName, &tripStatus,
			&tempRequirements, &isTemperatureSensitive,
			&currentTemp, &currentHumidity, &lastReadingTime,
			&tripTotalAlerts, &tripCriticalAlerts, &tripHighAlerts, &tripUnresolvedAlerts,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan dashboard row")
			continue
		}

		trip := map[string]interface{}{
			"trip_id":                  tripID,
			"customer_id":              customerID,
			"agent_id":                 agentIDStr,
			"agent_name":               agentName,
			"trip_status":              tripStatus,
			"is_temperature_sensitive": isTemperatureSensitive,
			"total_alerts":             tripTotalAlerts,
			"critical_alerts":          tripCriticalAlerts,
			"high_alerts":              tripHighAlerts,
			"unresolved_alerts":        tripUnresolvedAlerts,
		}

		if tempRequirements.Valid {
			var requirements map[string]interface{}
			if err := json.Unmarshal([]byte(tempRequirements.String), &requirements); err == nil {
				trip["temperature_requirements"] = requirements
			}
		}

		if currentTemp.Valid {
			trip["current_temperature"] = currentTemp.Float64
		}

		if currentHumidity.Valid {
			trip["current_humidity"] = currentHumidity.Float64
		}

		if lastReadingTime.Valid {
			trip["last_reading_time"] = lastReadingTime.Time
		}

		trips = append(trips, trip)
		totalTrips++
		totalAlerts += tripTotalAlerts
		criticalAlerts += tripCriticalAlerts
		unresolvedAlerts += tripUnresolvedAlerts
	}

	// Get recent temperature alerts summary
	alertsSummary, err := r.getRecentAlertsSummary(agentID)
	if err != nil {
		logrus.WithError(err).Warn("Failed to get recent alerts summary")
		alertsSummary = map[string]interface{}{}
	}

	dashboard := map[string]interface{}{
		"summary": map[string]interface{}{
			"total_temperature_sensitive_trips": totalTrips,
			"total_alerts":                      totalAlerts,
			"critical_alerts":                   criticalAlerts,
			"unresolved_alerts":                 unresolvedAlerts,
		},
		"trips":           trips,
		"recent_alerts":   alertsSummary,
		"last_updated":    time.Now(),
	}

	return dashboard, nil
}

// getRecentAlertsSummary gets a summary of recent temperature alerts
func (r *TemperatureRepository) getRecentAlertsSummary(agentID *uuid.UUID) (map[string]interface{}, error) {
	query := `
		SELECT 
			alert_type,
			severity,
			COUNT(*) as count,
			AVG(temperature_c) as avg_temperature,
			MIN(created_at) as first_alert,
			MAX(created_at) as last_alert
		FROM TemperatureAlerts
		WHERE created_at >= NOW() - INTERVAL '24 hours'`

	args := []interface{}{}
	if agentID != nil {
		query += " AND agent_id = $1"
		args = append(args, *agentID)
	}

	query += " GROUP BY alert_type, severity ORDER BY count DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent alerts summary: %w", err)
	}
	defer rows.Close()

	var alerts []map[string]interface{}
	for rows.Next() {
		var alertType, severity string
		var count int
		var avgTemp float64
		var firstAlert, lastAlert time.Time

		err := rows.Scan(&alertType, &severity, &count, &avgTemp, &firstAlert, &lastAlert)
		if err != nil {
			continue
		}

		alerts = append(alerts, map[string]interface{}{
			"alert_type":      alertType,
			"severity":        severity,
			"count":           count,
			"avg_temperature": avgTemp,
			"first_alert":     firstAlert,
			"last_alert":      lastAlert,
		})
	}

	return map[string]interface{}{
		"alerts_by_type": alerts,
		"period_hours":   24,
	}, nil
}

package middleware

import (
	"delivery-tracking-backend/config"

	"github.com/gin-gonic/gin"
	"github.com/rs/cors"
)

// CORSMiddleware returns a CORS middleware configured with the application settings
func CORSMiddleware(cfg *config.Config) gin.HandlerFunc {
	c := cors.New(cors.Options{
		AllowedOrigins:   cfg.CORS.AllowedOrigins,
		AllowedMethods:   cfg.CORS.AllowedMethods,
		AllowedHeaders:   cfg.CORS.AllowedHeaders,
		AllowCredentials: true,
		Debug:            cfg.Server.GinMode == "debug",
	})

	return gin.WrapH(c.Handler(gin.Default().Handler()))
}

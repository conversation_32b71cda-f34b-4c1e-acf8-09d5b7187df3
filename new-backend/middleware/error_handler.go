package middleware

import (
	"net/http"

	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ErrorHandlerMiddleware handles panics and errors
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			logrus.WithFields(logrus.Fields{
				"error":      err,
				"path":       c.Request.URL.Path,
				"method":     c.Request.Method,
				"client_ip":  c.ClientIP(),
				"user_agent": c.Request.UserAgent(),
			}).Error("Panic recovered")

			c.<PERSON>(http.StatusInternalServerError, utils.ErrorResponse(
				"Internal server error",
				"An unexpected error occurred. Please try again later.",
			))
		}
		c.Abort()
	})
}

// NotFoundHandler handles 404 errors
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>(http.StatusNotFound, utils.ErrorResponse(
			"Endpoint not found",
			"The requested endpoint does not exist",
		))
	}
}

// MethodNotAllowedHandler handles 405 errors
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, utils.ErrorResponse(
			"Method not allowed",
			"The HTTP method is not allowed for this endpoint",
		))
	}
}

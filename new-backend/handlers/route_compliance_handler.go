package handlers

import (
	"net/http"
	"strconv"
	"time"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type RouteComplianceHandler struct {
	complianceRepo *repository.RouteComplianceRepository
	tripRepo       *repository.TripRepository
}

// GeofenceRequest represents a request to create or update a geofence
type GeofenceRequest struct {
	Name            string                 `json:"name" validate:"required"`
	Description     string                 `json:"description"`
	GeofenceType    string                 `json:"geofence_type" validate:"required"`
	Boundary        [][]float64            `json:"boundary" validate:"required"` // Array of [lng, lat] coordinates
	SpeedLimitKmh   *int                   `json:"speed_limit_kmh,omitempty"`
	AllowedHours    map[string]interface{} `json:"allowed_hours,omitempty"`
	Restrictions    map[string]interface{} `json:"restrictions,omitempty"`
}

// PlannedRouteRequest represents a request to create a planned route
type PlannedRouteRequest struct {
	TripID                  string      `json:"trip_id" validate:"required"`
	RoutePoints             [][]float64        `json:"route_points" validate:"required"` // Array of [lng, lat] coordinates
	Waypoints               []models.Waypoint  `json:"waypoints,omitempty"`
	EstimatedDurationMinutes int         `json:"estimated_duration_minutes"`
	EstimatedDistanceKm     float64     `json:"estimated_distance_km"`
	MaxDeviationMeters      int         `json:"max_deviation_meters"`
}



func NewRouteComplianceHandler(complianceRepo *repository.RouteComplianceRepository, tripRepo *repository.TripRepository) *RouteComplianceHandler {
	return &RouteComplianceHandler{
		complianceRepo: complianceRepo,
		tripRepo:       tripRepo,
	}
}

// CreateGeofence creates a new geofence
// POST /api/route-compliance/geofences
func (h *RouteComplianceHandler) CreateGeofence(c *gin.Context) {
	var request GeofenceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid geofence request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("Geofence validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Create geofence
	geofence := &models.Geofence{
		Name:          request.Name,
		Description:   request.Description,
		GeofenceType:  request.GeofenceType,
		Boundary:      request.Boundary,
		SpeedLimitKmh: request.SpeedLimitKmh,
		AllowedHours:  request.AllowedHours,
		Restrictions:  request.Restrictions,
		IsActive:      true,
	}

	if err := h.complianceRepo.CreateGeofence(geofence); err != nil {
		logrus.WithError(err).Error("Failed to create geofence")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create geofence", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"geofence_id":   geofence.ID,
		"name":          geofence.Name,
		"geofence_type": geofence.GeofenceType,
	}).Info("Geofence created successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Geofence created successfully", geofence))
}

// GetGeofences retrieves all geofences
// GET /api/route-compliance/geofences
func (h *RouteComplianceHandler) GetGeofences(c *gin.Context) {
	activeOnly := c.Query("active_only") == "true"
	geofenceType := c.Query("type")

	geofences, err := h.complianceRepo.GetGeofences(activeOnly, geofenceType)
	if err != nil {
		logrus.WithError(err).Error("Failed to get geofences")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get geofences", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Geofences retrieved successfully", map[string]interface{}{
		"geofences":    geofences,
		"total_count":  len(geofences),
		"active_only":  activeOnly,
		"filter_type":  geofenceType,
	}))
}

// CreatePlannedRoute creates a planned route for a trip
// POST /api/route-compliance/planned-routes
func (h *RouteComplianceHandler) CreatePlannedRoute(c *gin.Context) {
	var request PlannedRouteRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid planned route request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("Planned route validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Parse trip ID
	tripID, err := uuid.Parse(request.TripID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	// Create planned route
	plannedRoute := &models.PlannedRoute{
		TripID:                   tripID,
		RoutePoints:              request.RoutePoints,
		Waypoints:                request.Waypoints,
		EstimatedDurationMinutes: request.EstimatedDurationMinutes,
		EstimatedDistanceKm:      request.EstimatedDistanceKm,
		MaxDeviationMeters:       request.MaxDeviationMeters,
	}

	if err := h.complianceRepo.CreatePlannedRoute(plannedRoute); err != nil {
		logrus.WithError(err).Error("Failed to create planned route")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create planned route", err.Error()))
		return
	}

	// Update trip to require compliance
	if err := h.tripRepo.SetComplianceRequired(tripID, true); err != nil {
		logrus.WithError(err).Warn("Failed to set compliance required for trip")
	}

	logrus.WithFields(logrus.Fields{
		"route_id": plannedRoute.ID,
		"trip_id":  tripID,
	}).Info("Planned route created successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Planned route created successfully", plannedRoute))
}

// GetComplianceViolations retrieves compliance violations
// GET /api/route-compliance/violations
func (h *RouteComplianceHandler) GetComplianceViolations(c *gin.Context) {
	tripIDStr := c.Query("trip_id")
	agentIDStr := c.Query("agent_id")
	eventType := c.Query("event_type")
	severity := c.Query("severity")
	unresolvedOnly := c.Query("unresolved_only") == "true"

	var tripID, agentID *uuid.UUID
	var err error

	if tripIDStr != "" {
		id, err := uuid.Parse(tripIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
			return
		}
		tripID = &id
	}

	if agentIDStr != "" {
		id, err := uuid.Parse(agentIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
			return
		}
		agentID = &id
	}

	violations, err := h.complianceRepo.GetComplianceViolations(tripID, agentID, eventType, severity, unresolvedOnly)
	if err != nil {
		logrus.WithError(err).Error("Failed to get compliance violations")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get compliance violations", err.Error()))
		return
	}

	response := map[string]interface{}{
		"violations":      violations,
		"total_count":     len(violations),
		"trip_id":         tripIDStr,
		"agent_id":        agentIDStr,
		"event_type":      eventType,
		"severity":        severity,
		"unresolved_only": unresolvedOnly,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Compliance violations retrieved successfully", response))
}

// ResolveViolation marks a compliance violation as resolved
// PUT /api/route-compliance/violations/:violation_id/resolve
func (h *RouteComplianceHandler) ResolveViolation(c *gin.Context) {
	violationIDStr := c.Param("violation_id")
	violationID, err := uuid.Parse(violationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid violation ID format", err.Error()))
		return
	}

	// Get resolution note from request body
	var request struct {
		ResolutionNote string `json:"resolution_note"`
	}
	c.ShouldBindJSON(&request)

	// Resolve the violation
	if err := h.complianceRepo.ResolveViolation(violationID, request.ResolutionNote); err != nil {
		logrus.WithError(err).Error("Failed to resolve compliance violation")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to resolve violation", err.Error()))
		return
	}

	logrus.WithField("violation_id", violationID).Info("Compliance violation resolved successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Violation resolved successfully", map[string]interface{}{
		"violation_id":    violationID,
		"resolved_at":     time.Now(),
		"resolution_note": request.ResolutionNote,
	}))
}

// GetComplianceDashboard retrieves compliance dashboard data
// GET /api/route-compliance/dashboard
func (h *RouteComplianceHandler) GetComplianceDashboard(c *gin.Context) {
	agentIDStr := c.Query("agent_id")
	daysStr := c.DefaultQuery("days", "7")

	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 7
	}

	var agentID *uuid.UUID
	if agentIDStr != "" {
		id, err := uuid.Parse(agentIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
			return
		}
		agentID = &id
	}

	// Get dashboard data
	dashboardData, err := h.complianceRepo.GetComplianceDashboard(agentID, days)
	if err != nil {
		logrus.WithError(err).Error("Failed to get compliance dashboard data")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get compliance dashboard data", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Compliance dashboard data retrieved successfully", dashboardData))
}

// GetComplianceReport generates a compliance report
// GET /api/route-compliance/report
func (h *RouteComplianceHandler) GetComplianceReport(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	agentIDStr := c.Query("agent_id")

	// Parse dates
	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid start date format", "Use YYYY-MM-DD format"))
			return
		}
	} else {
		startDate = time.Now().AddDate(0, 0, -30) // Default to last 30 days
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid end date format", "Use YYYY-MM-DD format"))
			return
		}
	} else {
		endDate = time.Now()
	}

	var agentID *uuid.UUID
	if agentIDStr != "" {
		id, err := uuid.Parse(agentIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
			return
		}
		agentID = &id
	}

	// Generate report
	report, err := h.complianceRepo.GenerateComplianceReport(startDate, endDate, agentID)
	if err != nil {
		logrus.WithError(err).Error("Failed to generate compliance report")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to generate compliance report", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Compliance report generated successfully", report))
}

// UpdateGeofence updates an existing geofence
// PUT /api/route-compliance/geofences/:geofence_id
func (h *RouteComplianceHandler) UpdateGeofence(c *gin.Context) {
	geofenceIDStr := c.Param("geofence_id")
	geofenceID, err := uuid.Parse(geofenceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid geofence ID format", err.Error()))
		return
	}

	var request GeofenceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid geofence update request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Update geofence
	geofence := &models.Geofence{
		ID:            geofenceID,
		Name:          request.Name,
		Description:   request.Description,
		GeofenceType:  request.GeofenceType,
		Boundary:      request.Boundary,
		SpeedLimitKmh: request.SpeedLimitKmh,
		AllowedHours:  request.AllowedHours,
		Restrictions:  request.Restrictions,
	}

	if err := h.complianceRepo.UpdateGeofence(geofence); err != nil {
		logrus.WithError(err).Error("Failed to update geofence")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update geofence", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Geofence updated successfully", geofence))
}

// DeleteGeofence deletes a geofence
// DELETE /api/route-compliance/geofences/:geofence_id
func (h *RouteComplianceHandler) DeleteGeofence(c *gin.Context) {
	geofenceIDStr := c.Param("geofence_id")
	geofenceID, err := uuid.Parse(geofenceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid geofence ID format", err.Error()))
		return
	}

	if err := h.complianceRepo.DeleteGeofence(geofenceID); err != nil {
		logrus.WithError(err).Error("Failed to delete geofence")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to delete geofence", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Geofence deleted successfully", map[string]interface{}{
		"geofence_id": geofenceID,
		"deleted_at":  time.Now(),
	}))
}

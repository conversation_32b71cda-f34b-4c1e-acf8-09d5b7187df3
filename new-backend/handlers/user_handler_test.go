package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockUserRepository is a mock implementation of UserRepository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) CreateUser(req *models.CreateUserRequest) (*models.User, error) {
	args := m.Called(req)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByID(id uuid.UUID) (*models.User, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByEmail(email string) (*models.User, error) {
	args := m.Called(email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) UpdateUser(id uuid.UUID, req *models.UpdateUserRequest) (*models.User, error) {
	args := m.Called(id, req)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) DeleteUser(id uuid.UUID) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockUserRepository) GetUsersByType(userType models.UserType) ([]*models.User, error) {
	args := m.Called(userType)
	return args.Get(0).([]*models.User), args.Error(1)
}

func (m *MockUserRepository) CheckEmailExists(email string) (bool, error) {
	args := m.Called(email)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) CheckPhoneExists(phone string) (bool, error) {
	args := m.Called(phone)
	return args.Bool(0), args.Error(1)
}

// MockAgentRepository is a mock implementation of AgentRepository
type MockAgentRepository struct {
	mock.Mock
}

func (m *MockAgentRepository) CreateAgentProfile(userID uuid.UUID, vehicleType, vehicleNo, licenseNo *string) (*models.AgentProfile, error) {
	args := m.Called(userID, vehicleType, vehicleNo, licenseNo)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AgentProfile), args.Error(1)
}

func (m *MockAgentRepository) GetAgentProfile(userID uuid.UUID) (*models.AgentProfile, error) {
	args := m.Called(userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.AgentProfile), args.Error(1)
}

func (m *MockAgentRepository) UpdateAgentProfile(userID uuid.UUID, vehicleType, vehicleNo, licenseNo *string) (*models.AgentProfile, error) {
	args := m.Called(userID, vehicleType, vehicleNo, licenseNo)
	return args.Get(0).(*models.AgentProfile), args.Error(1)
}

func (m *MockAgentRepository) UpdateAgentAvailability(userID uuid.UUID, isAvailable bool) error {
	args := m.Called(userID, isAvailable)
	return args.Error(0)
}

func (m *MockAgentRepository) GetAvailableAgents() ([]*models.AgentProfile, error) {
	args := m.Called()
	return args.Get(0).([]*models.AgentProfile), args.Error(1)
}

func (m *MockAgentRepository) GetAgentsByVehicleType(vehicleType string) ([]*models.AgentProfile, error) {
	args := m.Called(vehicleType)
	return args.Get(0).([]*models.AgentProfile), args.Error(1)
}

func (m *MockAgentRepository) UpdateAgentRating(userID uuid.UUID, newRating float64) error {
	args := m.Called(userID, newRating)
	return args.Error(0)
}

func (m *MockAgentRepository) GetAgentStats(userID uuid.UUID) (map[string]interface{}, error) {
	args := m.Called(userID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockAgentRepository) GetNearbyAgents(latitude, longitude, radiusKm float64) ([]*models.AgentProfile, error) {
	args := m.Called(latitude, longitude, radiusKm)
	return args.Get(0).([]*models.AgentProfile), args.Error(1)
}

func TestUserHandler_RegisterUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    models.CreateUserRequest
		setupMocks     func(*MockUserRepository, *MockAgentRepository)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful customer registration",
			requestBody: models.CreateUserRequest{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Phone:    "+1234567890",
				UserType: models.UserTypeCustomer,
			},
			setupMocks: func(userRepo *MockUserRepository, agentRepo *MockAgentRepository) {
				userRepo.On("CheckEmailExists", "<EMAIL>").Return(false, nil)
				userRepo.On("CheckPhoneExists", "+1234567890").Return(false, nil)
				userRepo.On("CreateUser", mock.AnythingOfType("*models.CreateUserRequest")).Return(&models.User{
					ID:       uuid.New(),
					Name:     "John Doe",
					Email:    "<EMAIL>",
					Phone:    "+1234567890",
					UserType: models.UserTypeCustomer,
					IsActive: true,
				}, nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "successful agent registration",
			requestBody: models.CreateUserRequest{
				Name:     "Jane Agent",
				Email:    "<EMAIL>",
				Phone:    "+1234567891",
				UserType: models.UserTypeAgent,
			},
			setupMocks: func(userRepo *MockUserRepository, agentRepo *MockAgentRepository) {
				userRepo.On("CheckEmailExists", "<EMAIL>").Return(false, nil)
				userRepo.On("CheckPhoneExists", "+1234567891").Return(false, nil)
				userID := uuid.New()
				userRepo.On("CreateUser", mock.AnythingOfType("*models.CreateUserRequest")).Return(&models.User{
					ID:       userID,
					Name:     "Jane Agent",
					Email:    "<EMAIL>",
					Phone:    "+1234567891",
					UserType: models.UserTypeAgent,
					IsActive: true,
				}, nil)
				agentRepo.On("CreateAgentProfile", userID, (*string)(nil), (*string)(nil), (*string)(nil)).Return(&models.AgentProfile{
					UserID:      userID,
					Rating:      0.0,
					IsAvailable: false,
				}, nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "email already exists",
			requestBody: models.CreateUserRequest{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Phone:    "+1234567890",
				UserType: models.UserTypeCustomer,
			},
			setupMocks: func(userRepo *MockUserRepository, agentRepo *MockAgentRepository) {
				userRepo.On("CheckEmailExists", "<EMAIL>").Return(true, nil)
			},
			expectedStatus: http.StatusConflict,
			expectedError:  "Email already exists",
		},
		{
			name: "invalid request body",
			requestBody: models.CreateUserRequest{
				Name:     "", // Invalid: empty name
				Email:    "invalid-email",
				Phone:    "+1234567890",
				UserType: models.UserTypeCustomer,
			},
			setupMocks:     func(userRepo *MockUserRepository, agentRepo *MockAgentRepository) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Validation failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			userRepo := new(MockUserRepository)
			agentRepo := new(MockAgentRepository)
			tt.setupMocks(userRepo, agentRepo)

			// Create handler
			handler := NewUserHandler(userRepo, agentRepo)

			// Setup request
			body, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/users/register", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Setup response recorder
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// Execute handler
			handler.RegisterUser(c)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response utils.APIResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			if tt.expectedError != "" {
				assert.False(t, response.Success)
				assert.Contains(t, response.Message, tt.expectedError)
			} else {
				assert.True(t, response.Success)
				assert.NotNil(t, response.Data)
			}

			// Verify mock expectations
			userRepo.AssertExpectations(t)
			agentRepo.AssertExpectations(t)
		})
	}
}

package handlers

import (
	"net/http"
	"strconv"
	"time"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type TripHandler struct {
	tripRepo *repository.TripRepository
	userRepo *repository.UserRepository
}

func NewTripHandler(tripRepo *repository.TripRepository, userRepo *repository.UserRepository) *TripHandler {
	return &TripHandler{
		tripRepo: tripRepo,
		userRepo: userRepo,
	}
}

// CreateTrip creates a new trip
// POST /api/trip/create
func (h *TripHandler) CreateTrip(c *gin.Context) {
	var req models.CreateTripRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid create trip request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Create trip validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Verify customer exists
	customer, err := h.userRepo.GetUserByID(req.CustomerID)
	if err != nil {
		logrus.WithError(err).Error("Customer not found")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Customer not found", err.Error()))
		return
	}

	if customer.UserType != models.UserTypeCustomerStr {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid customer", "User is not a customer"))
		return
	}

	// Verify agent exists
	agent, err := h.userRepo.GetUserByID(req.AgentID)
	if err != nil {
		logrus.WithError(err).Error("Agent not found")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Agent not found", err.Error()))
		return
	}

	if agent.UserType != models.UserTypeAgentStr {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent", "User is not an agent"))
		return
	}

	// Create the trip
	trip, err := h.tripRepo.CreateTrip(&req)
	if err != nil {
		logrus.WithError(err).Error("Failed to create trip")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create trip", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":     trip.TripID,
		"customer_id": trip.CustomerID,
		"agent_id":    trip.AgentID,
		"trip_type":   trip.TripType,
	}).Info("Trip created successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Trip created successfully", trip))
}

// GetTrip retrieves a trip by ID
// GET /api/trip/:trip_id
func (h *TripHandler) GetTrip(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := uuid.Parse(tripIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid trip ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	trip, err := h.tripRepo.GetTripByID(tripID)
	if err != nil {
		if err.Error() == "trip not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Trip not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get trip")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip retrieved successfully", trip))
}

// UpdateTripStatus updates the status of a trip
// POST /api/trip/status-update
func (h *TripHandler) UpdateTripStatus(c *gin.Context) {
	var req models.TripStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid trip status update request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Trip status update validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Verify trip exists
	trip, err := h.tripRepo.GetTripByID(req.TripID)
	if err != nil {
		if err.Error() == "trip not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Trip not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get trip for status update")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip", err.Error()))
		return
	}

	// Validate status transition
	if !isValidStatusTransition(trip.Status, req.Status) {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid status transition", 
			"Cannot change from "+string(trip.Status)+" to "+string(req.Status)))
		return
	}

	// Update trip status
	if err := h.tripRepo.UpdateTripStatus(&req); err != nil {
		logrus.WithError(err).Error("Failed to update trip status")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update trip status", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":    req.TripID,
		"old_status": trip.Status,
		"new_status": req.Status,
		"updated_by": req.UpdatedBy,
	}).Info("Trip status updated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip status updated successfully", nil))
}

// GetTripStatusUpdates retrieves all status updates for a trip
// GET /api/trip/:trip_id/status-updates
func (h *TripHandler) GetTripStatusUpdates(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := uuid.Parse(tripIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid trip ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	statusUpdates, err := h.tripRepo.GetTripStatusUpdates(tripID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip status updates")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get status updates", err.Error()))
		return
	}

	response := map[string]interface{}{
		"trip_id":        tripID,
		"total_updates":  len(statusUpdates),
		"status_updates": statusUpdates,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip status updates retrieved successfully", response))
}

// GetTripHistory retrieves trip history for a user
// GET /api/trip/history/:user_id
func (h *TripHandler) GetTripHistory(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	// Get user to determine type
	user, err := h.userRepo.GetUserByID(userID)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("User not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get user", err.Error()))
		return
	}

	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Get trip history
	trips, err := h.tripRepo.GetTripsByUser(userID, user.UserType, limit, offset)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip history")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip history", err.Error()))
		return
	}

	response := map[string]interface{}{
		"user_id":     userID,
		"user_type":   user.UserType,
		"total_trips": len(trips),
		"trips":       trips,
		"pagination": map[string]interface{}{
			"limit":  limit,
			"offset": offset,
		},
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip history retrieved successfully", response))
}

// GetActiveTrips retrieves all active trips
// GET /api/trip/active
func (h *TripHandler) GetActiveTrips(c *gin.Context) {
	activeTrips, err := h.tripRepo.GetActiveTrips()
	if err != nil {
		logrus.WithError(err).Error("Failed to get active trips")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get active trips", err.Error()))
		return
	}

	response := map[string]interface{}{
		"total_active_trips": len(activeTrips),
		"trips":              activeTrips,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Active trips retrieved successfully", response))
}

// AcceptTrip allows an agent to accept a trip
// POST /api/trip/:trip_id/accept
func (h *TripHandler) AcceptTrip(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := uuid.Parse(tripIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid trip ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	var req struct {
		AgentID uuid.UUID `json:"agent_id" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid accept trip request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Accept trip validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Get trip and verify it can be accepted
	trip, err := h.tripRepo.GetTripByID(tripID)
	if err != nil {
		if err.Error() == "trip not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Trip not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get trip for acceptance")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip", err.Error()))
		return
	}

	if trip.Status != models.TripStatusRequested {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Trip cannot be accepted", 
			"Trip status is "+string(trip.Status)))
		return
	}

	// Verify agent
	agent, err := h.userRepo.GetUserByID(req.AgentID)
	if err != nil {
		logrus.WithError(err).Error("Agent not found")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Agent not found", err.Error()))
		return
	}

	if agent.UserType != models.UserTypeAgentStr {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent", "User is not an agent"))
		return
	}

	// Update trip status to accepted
	statusUpdateReq := &models.TripStatusUpdateRequest{
		TripID:    tripID,
		Status:    models.TripStatusAccepted,
		UpdatedBy: &req.AgentID,
		Note:      stringPtr("Trip accepted by agent"),
	}

	if err := h.tripRepo.UpdateTripStatus(statusUpdateReq); err != nil {
		logrus.WithError(err).Error("Failed to accept trip")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to accept trip", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":  tripID,
		"agent_id": req.AgentID,
	}).Info("Trip accepted successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip accepted successfully", nil))
}

// Helper function to validate status transitions
func isValidStatusTransition(currentStatus, newStatus models.TripStatus) bool {
	validTransitions := map[models.TripStatus][]models.TripStatus{
		models.TripStatusRequested: {models.TripStatusAccepted, models.TripStatusCancelled},
		models.TripStatusAccepted:  {models.TripStatusPickedUp, models.TripStatusCancelled},
		models.TripStatusPickedUp:  {models.TripStatusInTransit, models.TripStatusCancelled},
		models.TripStatusInTransit:      {models.TripStatusOutForDelivery, models.TripStatusDelivered, models.TripStatusFailed, models.TripStatusCancelled},
		models.TripStatusOutForDelivery: {models.TripStatusDelivered, models.TripStatusFailed, models.TripStatusRescheduled},
		models.TripStatusDelivered:      {}, // Terminal state
		models.TripStatusFailed:         {models.TripStatusRescheduled, models.TripStatusReturned},
		models.TripStatusRescheduled:    {models.TripStatusOutForDelivery, models.TripStatusDelivered, models.TripStatusFailed},
		models.TripStatusReturned:       {}, // Terminal state
		models.TripStatusCancelled: {}, // Terminal state
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, allowedStatus := range allowedStatuses {
		if newStatus == allowedStatus {
			return true
		}
	}

	return false
}

// AssignDelivery creates a new delivery assignment for an agent
// POST /api/trip/assign-delivery
func (h *TripHandler) AssignDelivery(c *gin.Context) {
	var request struct {
		AgentID         string                 `json:"agent_id" binding:"required"`
		CustomerID      string                 `json:"customer_id" binding:"required"`
		PickupLocation  map[string]interface{} `json:"pickup_location" binding:"required"`
		DropLocation    map[string]interface{} `json:"drop_location" binding:"required"`
		ItemDetails     map[string]interface{} `json:"item_details"`
		DeliveryFee     float64                `json:"delivery_fee"`
		ScheduledDate   string                 `json:"scheduled_date"` // YYYY-MM-DD format
		Notes           string                 `json:"notes"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request data", err.Error()))
		return
	}

	// Parse UUIDs
	agentID, err := uuid.Parse(request.AgentID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	customerID, err := uuid.Parse(request.CustomerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid customer ID", "Customer ID must be a valid UUID"))
		return
	}

	// Create trip assignment
	tripID, err := h.tripRepo.AssignDelivery(agentID, customerID, request.PickupLocation, request.DropLocation, request.ItemDetails, request.DeliveryFee, request.ScheduledDate, request.Notes)
	if err != nil {
		logrus.WithError(err).Error("Failed to assign delivery")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to assign delivery", err.Error()))
		return
	}

	response := map[string]interface{}{
		"trip_id":    tripID,
		"agent_id":   agentID,
		"customer_id": customerID,
		"status":     "assigned",
		"message":    "Delivery assigned successfully",
	}

	c.JSON(http.StatusCreated, utils.SuccessResponse("Delivery assigned successfully", response))
}

// GetAgentDeliveries retrieves assigned deliveries for an agent on a specific date
// GET /api/trip/agent/:agent_id/deliveries?date=YYYY-MM-DD
func (h *TripHandler) GetAgentDeliveries(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	dateStr := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	// Parse the date
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid date format", "Date must be in YYYY-MM-DD format"))
		return
	}

	// Get deliveries for the agent on the specified date
	deliveries, err := h.tripRepo.GetAgentDeliveriesByDate(agentID, date)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agent deliveries")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get deliveries", err.Error()))
		return
	}

	response := map[string]interface{}{
		"agent_id":   agentID,
		"date":       dateStr,
		"deliveries": deliveries,
		"count":      len(deliveries),
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Agent deliveries retrieved successfully", response))
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

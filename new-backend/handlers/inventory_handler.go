package handlers

import (
	"net/http"
	"strconv"
	"time"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type InventoryHandler struct {
	inventoryRepo *repository.InventoryRepository
	tripRepo      *repository.TripRepository
}

// InventoryMovementRequest represents a request to record inventory movement
type InventoryMovementRequest struct {
	InventoryID    string  `json:"inventory_id" validate:"required"`
	MovementType   string  `json:"movement_type" validate:"required"` // 'inbound', 'outbound', 'transfer', 'adjustment'
	Quantity       int     `json:"quantity" validate:"required"`
	ReferenceType  string  `json:"reference_type,omitempty"`          // 'trip', 'purchase_order', 'adjustment'
	ReferenceID    string  `json:"reference_id,omitempty"`
	Reason         string  `json:"reason,omitempty"`
	CostPerUnit    float64 `json:"cost_per_unit,omitempty"`
}

// InventoryUpdateRequest represents a request to update inventory levels
type InventoryUpdateRequest struct {
	CurrentStock    *int    `json:"current_stock,omitempty"`
	ReservedStock   *int    `json:"reserved_stock,omitempty"`
	ReorderPoint    *int    `json:"reorder_point,omitempty"`
	MaxStockLevel   *int    `json:"max_stock_level,omitempty"`
	UnitCost        *float64 `json:"unit_cost,omitempty"`
	ExpiryDate      *string `json:"expiry_date,omitempty"` // YYYY-MM-DD format
	BatchNumber     *string `json:"batch_number,omitempty"`
}

// ProductRequest represents a request to create or update a product
type ProductRequest struct {
	SKU                  string                 `json:"sku" validate:"required"`
	Name                 string                 `json:"name" validate:"required"`
	Description          string                 `json:"description,omitempty"`
	Category             string                 `json:"category,omitempty"`
	UnitOfMeasure        string                 `json:"unit_of_measure,omitempty"`
	UnitPrice            float64                `json:"unit_price,omitempty"`
	WeightKg             float64                `json:"weight_kg,omitempty"`
	Dimensions           map[string]interface{} `json:"dimensions,omitempty"`
	SupplierInfo         map[string]interface{} `json:"supplier_info,omitempty"`
	ShelfLifeDays        *int                   `json:"shelf_life_days,omitempty"`
	StorageRequirements  map[string]interface{} `json:"storage_requirements,omitempty"`
}

func NewInventoryHandler(inventoryRepo *repository.InventoryRepository, tripRepo *repository.TripRepository) *InventoryHandler {
	return &InventoryHandler{
		inventoryRepo: inventoryRepo,
		tripRepo:      tripRepo,
	}
}

// GetInventoryDashboard retrieves inventory dashboard data
// GET /api/inventory/dashboard
func (h *InventoryHandler) GetInventoryDashboard(c *gin.Context) {
	warehouseIDStr := c.Query("warehouse_id")
	category := c.Query("category")
	lowStockOnly := c.Query("low_stock_only") == "true"

	var warehouseID *uuid.UUID
	if warehouseIDStr != "" {
		id, err := uuid.Parse(warehouseIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid warehouse ID format", err.Error()))
			return
		}
		warehouseID = &id
	}

	// Get dashboard data
	dashboardData, err := h.inventoryRepo.GetInventoryDashboard(warehouseID, category, lowStockOnly)
	if err != nil {
		logrus.WithError(err).Error("Failed to get inventory dashboard data")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get inventory dashboard data", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Inventory dashboard data retrieved successfully", dashboardData))
}

// GetInventoryAlerts retrieves inventory alerts
// GET /api/inventory/alerts
func (h *InventoryHandler) GetInventoryAlerts(c *gin.Context) {
	warehouseIDStr := c.Query("warehouse_id")
	alertType := c.Query("alert_type")
	severity := c.Query("severity")
	unresolvedOnly := c.Query("unresolved_only") == "true"

	var warehouseID *uuid.UUID
	if warehouseIDStr != "" {
		id, err := uuid.Parse(warehouseIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid warehouse ID format", err.Error()))
			return
		}
		warehouseID = &id
	}

	alerts, err := h.inventoryRepo.GetInventoryAlerts(warehouseID, alertType, severity, unresolvedOnly)
	if err != nil {
		logrus.WithError(err).Error("Failed to get inventory alerts")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get inventory alerts", err.Error()))
		return
	}

	response := map[string]interface{}{
		"alerts":         alerts,
		"total_count":    len(alerts),
		"warehouse_id":   warehouseIDStr,
		"alert_type":     alertType,
		"severity":       severity,
		"unresolved_only": unresolvedOnly,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Inventory alerts retrieved successfully", response))
}

// RecordInventoryMovement records an inventory movement
// POST /api/inventory/movements
func (h *InventoryHandler) RecordInventoryMovement(c *gin.Context) {
	var request InventoryMovementRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid inventory movement request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("Inventory movement validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Parse inventory ID
	inventoryID, err := uuid.Parse(request.InventoryID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid inventory ID format", err.Error()))
		return
	}

	// Parse reference ID if provided
	var referenceID *uuid.UUID
	if request.ReferenceID != "" {
		id, err := uuid.Parse(request.ReferenceID)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid reference ID format", err.Error()))
			return
		}
		referenceID = &id
	}

	// Create inventory movement
	movement := &models.InventoryMovement{
		InventoryID:   inventoryID,
		MovementType:  request.MovementType,
		Quantity:      request.Quantity,
		ReferenceType: request.ReferenceType,
		ReferenceID:   referenceID,
		Reason:        request.Reason,
		CostPerUnit:   request.CostPerUnit,
		TotalCost:     request.CostPerUnit * float64(request.Quantity),
	}

	if err := h.inventoryRepo.RecordInventoryMovement(movement); err != nil {
		logrus.WithError(err).Error("Failed to record inventory movement")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to record inventory movement", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"movement_id":   movement.ID,
		"inventory_id":  inventoryID,
		"movement_type": request.MovementType,
		"quantity":      request.Quantity,
	}).Info("Inventory movement recorded successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Inventory movement recorded successfully", movement))
}

// UpdateInventory updates inventory levels
// PUT /api/inventory/:inventory_id
func (h *InventoryHandler) UpdateInventory(c *gin.Context) {
	inventoryIDStr := c.Param("inventory_id")
	inventoryID, err := uuid.Parse(inventoryIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid inventory ID format", err.Error()))
		return
	}

	var request InventoryUpdateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid inventory update request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Parse expiry date if provided
	var expiryDate *time.Time
	if request.ExpiryDate != nil {
		date, err := time.Parse("2006-01-02", *request.ExpiryDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid expiry date format", "Use YYYY-MM-DD format"))
			return
		}
		expiryDate = &date
	}

	// Update inventory
	updateData := &models.InventoryUpdate{
		CurrentStock:   request.CurrentStock,
		ReservedStock:  request.ReservedStock,
		ReorderPoint:   request.ReorderPoint,
		MaxStockLevel:  request.MaxStockLevel,
		UnitCost:       request.UnitCost,
		ExpiryDate:     expiryDate,
		BatchNumber:    request.BatchNumber,
	}

	if err := h.inventoryRepo.UpdateInventory(inventoryID, updateData); err != nil {
		logrus.WithError(err).Error("Failed to update inventory")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update inventory", err.Error()))
		return
	}

	logrus.WithField("inventory_id", inventoryID).Info("Inventory updated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Inventory updated successfully", map[string]interface{}{
		"inventory_id": inventoryID,
		"updated_at":   time.Now(),
	}))
}

// GetInventoryMovements retrieves inventory movement history
// GET /api/inventory/:inventory_id/movements
func (h *InventoryHandler) GetInventoryMovements(c *gin.Context) {
	inventoryIDStr := c.Param("inventory_id")
	inventoryID, err := uuid.Parse(inventoryIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid inventory ID format", err.Error()))
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	movements, err := h.inventoryRepo.GetInventoryMovements(inventoryID, days)
	if err != nil {
		logrus.WithError(err).Error("Failed to get inventory movements")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get inventory movements", err.Error()))
		return
	}

	response := map[string]interface{}{
		"movements":    movements,
		"total_count":  len(movements),
		"inventory_id": inventoryID,
		"days":         days,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Inventory movements retrieved successfully", response))
}

// CreateProduct creates a new product
// POST /api/inventory/products
func (h *InventoryHandler) CreateProduct(c *gin.Context) {
	var request ProductRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid product request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("Product validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Create product
	product := &models.Product{
		SKU:                 request.SKU,
		Name:                request.Name,
		Description:         request.Description,
		Category:            request.Category,
		UnitOfMeasure:       request.UnitOfMeasure,
		UnitPrice:           request.UnitPrice,
		WeightKg:            request.WeightKg,
		Dimensions:          request.Dimensions,
		SupplierInfo:        request.SupplierInfo,
		ShelfLifeDays:       request.ShelfLifeDays,
		StorageRequirements: request.StorageRequirements,
		IsActive:            true,
	}

	if err := h.inventoryRepo.CreateProduct(product); err != nil {
		logrus.WithError(err).Error("Failed to create product")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create product", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"product_id": product.ID,
		"sku":        product.SKU,
		"name":       product.Name,
	}).Info("Product created successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Product created successfully", product))
}

// GetProducts retrieves products with optional filters
// GET /api/inventory/products
func (h *InventoryHandler) GetProducts(c *gin.Context) {
	category := c.Query("category")
	activeOnly := c.Query("active_only") != "false" // Default to true

	products, err := h.inventoryRepo.GetProducts(category, activeOnly)
	if err != nil {
		logrus.WithError(err).Error("Failed to get products")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get products", err.Error()))
		return
	}

	response := map[string]interface{}{
		"products":    products,
		"total_count": len(products),
		"category":    category,
		"active_only": activeOnly,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Products retrieved successfully", response))
}

// ResolveInventoryAlert marks an inventory alert as resolved
// PUT /api/inventory/alerts/:alert_id/resolve
func (h *InventoryHandler) ResolveInventoryAlert(c *gin.Context) {
	alertIDStr := c.Param("alert_id")
	alertID, err := uuid.Parse(alertIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid alert ID format", err.Error()))
		return
	}

	// Get resolution note from request body
	var request struct {
		ResolutionNote string `json:"resolution_note"`
	}
	c.ShouldBindJSON(&request)

	// Resolve the alert
	if err := h.inventoryRepo.ResolveInventoryAlert(alertID, request.ResolutionNote); err != nil {
		logrus.WithError(err).Error("Failed to resolve inventory alert")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to resolve alert", err.Error()))
		return
	}

	logrus.WithField("alert_id", alertID).Info("Inventory alert resolved successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Alert resolved successfully", map[string]interface{}{
		"alert_id":        alertID,
		"resolved_at":     time.Now(),
		"resolution_note": request.ResolutionNote,
	}))
}

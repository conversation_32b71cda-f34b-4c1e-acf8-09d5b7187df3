package handlers

import (
	"net/http"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type UserHandler struct {
	userRepo  *repository.UserRepository
	agentRepo *repository.AgentRepository
}

func NewUserHandler(userRepo *repository.UserRepository, agentRepo *repository.AgentRepository) *UserHandler {
	return &UserHandler{
		userRepo:  userRepo,
		agentRepo: agentRepo,
	}
}

// RegisterUser creates a new user
// POST /api/users/register
func (h *UserHandler) RegisterUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid register user request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Register user validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Check if email already exists
	emailExists, err := h.userRepo.CheckEmailExists(req.Email)
	if err != nil {
		logrus.WithError(err).Error("Failed to check email existence")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to check email", err.Error()))
		return
	}
	if emailExists {
		c.JSON(http.StatusConflict, utils.ErrorResponse("Email already exists", "A user with this email already exists"))
		return
	}

	// Check if phone already exists
	phoneExists, err := h.userRepo.CheckPhoneExists(req.Phone)
	if err != nil {
		logrus.WithError(err).Error("Failed to check phone existence")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to check phone", err.Error()))
		return
	}
	if phoneExists {
		c.JSON(http.StatusConflict, utils.ErrorResponse("Phone already exists", "A user with this phone number already exists"))
		return
	}

	// Create the user
	user, err := h.userRepo.CreateUser(&req)
	if err != nil {
		logrus.WithError(err).Error("Failed to create user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create user", err.Error()))
		return
	}

	// If user is an agent, create agent profile
	if req.UserType == models.UserTypeAgentStr {
		_, err := h.agentRepo.CreateAgentProfile(user.ID, nil, nil, nil)
		if err != nil {
			logrus.WithError(err).Error("Failed to create agent profile")
			// Don't fail the entire registration, just log the error
			logrus.WithField("user_id", user.ID).Warn("User created but agent profile creation failed")
		}
	}

	logrus.WithFields(logrus.Fields{
		"user_id":   user.ID,
		"email":     user.Email,
		"user_type": user.UserType,
	}).Info("User registered successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("User registered successfully", user))
}

// GetUser retrieves a user by ID
// GET /api/users/:id
func (h *UserHandler) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	user, err := h.userRepo.GetUserByID(userID)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("User not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get user", err.Error()))
		return
	}

	// If user is an agent, include agent profile
	response := map[string]interface{}{
		"user": user,
	}

	if user.UserType == models.UserTypeAgentStr {
		agentProfile, err := h.agentRepo.GetAgentProfile(userID)
		if err != nil {
			logrus.WithError(err).Warn("Failed to get agent profile")
		} else {
			response["agent_profile"] = agentProfile
		}
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("User retrieved successfully", response))
}

// UpdateUser updates user information
// PUT /api/users/:id
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid update user request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Update user validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Check if email already exists (if being updated)
	if req.Email != nil {
		emailExists, err := h.userRepo.CheckEmailExists(*req.Email)
		if err != nil {
			logrus.WithError(err).Error("Failed to check email existence")
			c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to check email", err.Error()))
			return
		}
		if emailExists {
			// Check if it's the same user's email
			existingUser, err := h.userRepo.GetUserByEmail(*req.Email)
			if err == nil && existingUser.ID != userID {
				c.JSON(http.StatusConflict, utils.ErrorResponse("Email already exists", "A different user with this email already exists"))
				return
			}
		}
	}

	// Check if phone already exists (if being updated)
	if req.Phone != nil {
		phoneExists, err := h.userRepo.CheckPhoneExists(*req.Phone)
		if err != nil {
			logrus.WithError(err).Error("Failed to check phone existence")
			c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to check phone", err.Error()))
			return
		}
		if phoneExists {
			// Check if it's the same user's phone
			currentUser, err := h.userRepo.GetUserByID(userID)
			if err == nil && currentUser.Phone != *req.Phone {
				c.JSON(http.StatusConflict, utils.ErrorResponse("Phone already exists", "A different user with this phone number already exists"))
				return
			}
		}
	}

	// Update the user
	user, err := h.userRepo.UpdateUser(userID, &req)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("User not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to update user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update user", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"user_id": userID,
		"email":   user.Email,
	}).Info("User updated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("User updated successfully", user))
}

// GetAvailableAgents retrieves all available agents
// GET /api/agents/available
func (h *UserHandler) GetAvailableAgents(c *gin.Context) {
	// Parse query parameters
	vehicleType := c.Query("vehicle_type")
	
	var agents []*models.AgentProfile
	var err error

	if vehicleType != "" {
		agents, err = h.agentRepo.GetAgentsByVehicleType(vehicleType)
	} else {
		agents, err = h.agentRepo.GetAvailableAgents()
	}

	if err != nil {
		logrus.WithError(err).Error("Failed to get available agents")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get available agents", err.Error()))
		return
	}

	response := map[string]interface{}{
		"total_agents": len(agents),
		"agents":       agents,
	}

	if vehicleType != "" {
		response["vehicle_type"] = vehicleType
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Available agents retrieved successfully", response))
}

// UpdateAgentAvailability updates an agent's availability status
// PUT /api/agents/:id/availability
func (h *UserHandler) UpdateAgentAvailability(c *gin.Context) {
	agentIDStr := c.Param("id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid agent ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	var req struct {
		IsAvailable bool `json:"is_available"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid availability update request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Verify agent exists
	user, err := h.userRepo.GetUserByID(agentID)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Agent not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get agent")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get agent", err.Error()))
		return
	}

	if user.UserType != models.UserTypeAgentStr {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent", "User is not an agent"))
		return
	}

	// Update availability
	if err := h.agentRepo.UpdateAgentAvailability(agentID, req.IsAvailable); err != nil {
		if err.Error() == "agent profile not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Agent profile not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to update agent availability")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update availability", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"agent_id":     agentID,
		"is_available": req.IsAvailable,
	}).Info("Agent availability updated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Agent availability updated successfully", nil))
}

// UpdateAgentProfile updates an agent's profile information
// PUT /api/agents/:id/profile
func (h *UserHandler) UpdateAgentProfile(c *gin.Context) {
	agentIDStr := c.Param("id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid agent ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	var req struct {
		VehicleType *string `json:"vehicle_type,omitempty"`
		VehicleNo   *string `json:"vehicle_no,omitempty"`
		LicenseNo   *string `json:"license_no,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid agent profile update request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Verify agent exists
	user, err := h.userRepo.GetUserByID(agentID)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Agent not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get agent")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get agent", err.Error()))
		return
	}

	if user.UserType != models.UserTypeAgentStr {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent", "User is not an agent"))
		return
	}

	// Update agent profile
	profile, err := h.agentRepo.UpdateAgentProfile(agentID, req.VehicleType, req.VehicleNo, req.LicenseNo)
	if err != nil {
		if err.Error() == "agent profile not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Agent profile not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to update agent profile")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update agent profile", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"agent_id": agentID,
	}).Info("Agent profile updated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Agent profile updated successfully", profile))
}

// GetAgentStats retrieves statistics for an agent
// GET /api/agents/:id/stats
func (h *UserHandler) GetAgentStats(c *gin.Context) {
	agentIDStr := c.Param("id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid agent ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	// Verify agent exists
	user, err := h.userRepo.GetUserByID(agentID)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Agent not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get agent")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get agent", err.Error()))
		return
	}

	if user.UserType != models.UserTypeAgentStr {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent", "User is not an agent"))
		return
	}

	// Get agent statistics
	stats, err := h.agentRepo.GetAgentStats(agentID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agent stats")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get agent stats", err.Error()))
		return
	}

	stats["agent_id"] = agentID

	c.JSON(http.StatusOK, utils.SuccessResponse("Agent stats retrieved successfully", stats))
}

// GetCustomers retrieves all customers for delivery assignment
// GET /api/user/customers
func (h *UserHandler) GetCustomers(c *gin.Context) {
	customers, err := h.userRepo.GetCustomers()
	if err != nil {
		logrus.WithError(err).Error("Failed to get customers")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get customers", err.Error()))
		return
	}

	response := map[string]interface{}{
		"customers": customers,
		"count":     len(customers),
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Customers retrieved successfully", response))
}

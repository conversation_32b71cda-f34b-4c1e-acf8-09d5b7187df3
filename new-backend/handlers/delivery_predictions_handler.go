package handlers

import (
	"math"
	"net/http"
	"strconv"
	"time"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type DeliveryPredictionsHandler struct {
	tripRepo     *repository.TripRepository
	trackingRepo *repository.TrackingRepository
}

// ETARequest represents a request for ETA calculation
type ETARequest struct {
	PickupLatitude    float64 `json:"pickup_latitude" validate:"required,min=-90,max=90"`
	PickupLongitude   float64 `json:"pickup_longitude" validate:"required,min=-180,max=180"`
	DeliveryLatitude  float64 `json:"delivery_latitude" validate:"required,min=-90,max=90"`
	DeliveryLongitude float64 `json:"delivery_longitude" validate:"required,min=-180,max=180"`
	AgentID           string  `json:"agent_id,omitempty"`
	TimeOfDay         string  `json:"time_of_day,omitempty"` // "morning", "afternoon", "evening", "night"
	DayOfWeek         string  `json:"day_of_week,omitempty"` // "monday", "tuesday", etc.
}

// DeliveryPrediction represents a delivery time prediction
type DeliveryPrediction struct {
	EstimatedDuration   int       `json:"estimated_duration_minutes"`
	EstimatedArrival    time.Time `json:"estimated_arrival"`
	Confidence          float64   `json:"confidence_percentage"`
	Distance            float64   `json:"distance_km"`
	AverageSpeed        float64   `json:"average_speed_kmh"`
	TrafficFactor       float64   `json:"traffic_factor"`
	HistoricalDataCount int       `json:"historical_data_count"`
	Factors             []string  `json:"factors"`
}

func NewDeliveryPredictionsHandler(tripRepo *repository.TripRepository, trackingRepo *repository.TrackingRepository) *DeliveryPredictionsHandler {
	return &DeliveryPredictionsHandler{
		tripRepo:     tripRepo,
		trackingRepo: trackingRepo,
	}
}

// CalculateETA calculates estimated time of arrival for a delivery
// POST /api/delivery-predictions/eta
func (h *DeliveryPredictionsHandler) CalculateETA(c *gin.Context) {
	var request ETARequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid ETA request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("ETA request validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Calculate distance between pickup and delivery points
	distance := h.calculateDistance(
		request.PickupLatitude, request.PickupLongitude,
		request.DeliveryLatitude, request.DeliveryLongitude,
	)

	// Get historical delivery data for similar routes
	historicalData, err := h.getHistoricalDeliveryData(request)
	if err != nil {
		logrus.WithError(err).Error("Failed to get historical delivery data")
		// Continue with default calculations if historical data fails
	}

	// Calculate prediction
	prediction := h.calculateDeliveryPrediction(request, distance, historicalData)

	logrus.WithFields(logrus.Fields{
		"distance":           distance,
		"estimated_duration": prediction.EstimatedDuration,
		"confidence":         prediction.Confidence,
	}).Info("ETA calculated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("ETA calculated successfully", prediction))
}

// GetDeliveryStats retrieves delivery statistics for analytics
// GET /api/delivery-predictions/stats
func (h *DeliveryPredictionsHandler) GetDeliveryStats(c *gin.Context) {
	agentIDStr := c.Query("agent_id")
	daysStr := c.DefaultQuery("days", "30")
	
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	var agentID *uuid.UUID
	if agentIDStr != "" {
		id, err := uuid.Parse(agentIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
			return
		}
		agentID = &id
	}

	// Get delivery statistics
	stats, err := h.getDeliveryStatistics(agentID, days)
	if err != nil {
		logrus.WithError(err).Error("Failed to get delivery statistics")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get delivery statistics", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Delivery statistics retrieved successfully", stats))
}

// GetAgentPerformance retrieves agent performance metrics
// GET /api/delivery-predictions/agent-performance/:agent_id
func (h *DeliveryPredictionsHandler) GetAgentPerformance(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	// Get agent performance metrics
	performance, err := h.getAgentPerformanceMetrics(agentID, days)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agent performance metrics")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get agent performance metrics", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Agent performance metrics retrieved successfully", performance))
}

// calculateDistance calculates the distance between two points using Haversine formula
func (h *DeliveryPredictionsHandler) calculateDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const earthRadius = 6371 // Earth's radius in kilometers

	// Convert degrees to radians
	lat1Rad := lat1 * math.Pi / 180
	lon1Rad := lon1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lon2Rad := lon2 * math.Pi / 180

	// Haversine formula
	dlat := lat2Rad - lat1Rad
	dlon := lon2Rad - lon1Rad

	a := math.Sin(dlat/2)*math.Sin(dlat/2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Sin(dlon/2)*math.Sin(dlon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}

// getHistoricalDeliveryData retrieves historical delivery data for similar routes
func (h *DeliveryPredictionsHandler) getHistoricalDeliveryData(request ETARequest) ([]models.Trip, error) {
	// Get completed trips within a radius of the pickup/delivery points
	// This is a simplified version - in production, you'd use more sophisticated matching
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -90) // Last 90 days

	trips, err := h.tripRepo.GetTripsByDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Filter trips that are similar to the requested route
	var similarTrips []models.Trip
	for _, trip := range trips {
		if trip.Status == models.TripStatusDelivered {
			// Check if pickup and delivery points are within reasonable distance
			pickupDistance := h.calculateDistance(
				request.PickupLatitude, request.PickupLongitude,
				trip.PickupLocation.Latitude, trip.PickupLocation.Longitude,
			)
			deliveryDistance := h.calculateDistance(
				request.DeliveryLatitude, request.DeliveryLongitude,
				trip.DropLocation.Latitude, trip.DropLocation.Longitude,
			)

			// Consider trips within 5km radius as similar
			if pickupDistance <= 5.0 && deliveryDistance <= 5.0 {
				similarTrips = append(similarTrips, *trip)
			}
		}
	}

	return similarTrips, nil
}

// calculateDeliveryPrediction calculates the delivery prediction based on various factors
func (h *DeliveryPredictionsHandler) calculateDeliveryPrediction(request ETARequest, distance float64, historicalData []models.Trip) *DeliveryPrediction {
	// Base calculation using average speed
	baseSpeed := 25.0 // km/h - average delivery speed in urban areas
	baseDuration := (distance / baseSpeed) * 60 // minutes

	// Adjust based on time of day
	timeOfDayFactor := h.getTimeOfDayFactor(request.TimeOfDay)
	
	// Adjust based on day of week
	dayOfWeekFactor := h.getDayOfWeekFactor(request.DayOfWeek)

	// Calculate historical average if we have data
	historicalAverage := baseDuration
	confidence := 60.0 // Base confidence

	if len(historicalData) > 0 {
		totalDuration := 0.0
		validTrips := 0

		for _, trip := range historicalData {
			if trip.CompletedAt != nil {
				duration := trip.CompletedAt.Sub(trip.CreatedAt).Minutes()
				if duration > 0 && duration < 480 { // Reasonable delivery time (< 8 hours)
					totalDuration += duration
					validTrips++
				}
			}
		}

		if validTrips > 0 {
			historicalAverage = totalDuration / float64(validTrips)
			confidence = 80.0 + float64(validTrips)*2 // Higher confidence with more data
			if confidence > 95.0 {
				confidence = 95.0
			}
		}
	}

	// Apply factors
	adjustedDuration := historicalAverage * timeOfDayFactor * dayOfWeekFactor

	// Calculate estimated arrival time
	estimatedArrival := time.Now().Add(time.Duration(adjustedDuration) * time.Minute)

	// Determine factors that influenced the prediction
	factors := []string{"historical_data", "distance_calculation"}
	if timeOfDayFactor != 1.0 {
		factors = append(factors, "time_of_day_adjustment")
	}
	if dayOfWeekFactor != 1.0 {
		factors = append(factors, "day_of_week_adjustment")
	}

	return &DeliveryPrediction{
		EstimatedDuration:   int(adjustedDuration),
		EstimatedArrival:    estimatedArrival,
		Confidence:          confidence,
		Distance:            distance,
		AverageSpeed:        distance / (adjustedDuration / 60), // km/h
		TrafficFactor:       timeOfDayFactor * dayOfWeekFactor,
		HistoricalDataCount: len(historicalData),
		Factors:             factors,
	}
}

// getTimeOfDayFactor returns a factor based on time of day
func (h *DeliveryPredictionsHandler) getTimeOfDayFactor(timeOfDay string) float64 {
	switch timeOfDay {
	case "morning": // 6-12 PM - moderate traffic
		return 1.2
	case "afternoon": // 12-6 PM - heavy traffic
		return 1.5
	case "evening": // 6-10 PM - peak traffic
		return 1.8
	case "night": // 10 PM-6 AM - light traffic
		return 0.8
	default:
		// Auto-detect based on current time
		hour := time.Now().Hour()
		if hour >= 6 && hour < 12 {
			return 1.2
		} else if hour >= 12 && hour < 18 {
			return 1.5
		} else if hour >= 18 && hour < 22 {
			return 1.8
		} else {
			return 0.8
		}
	}
}

// getDayOfWeekFactor returns a factor based on day of week
func (h *DeliveryPredictionsHandler) getDayOfWeekFactor(dayOfWeek string) float64 {
	switch dayOfWeek {
	case "monday", "tuesday", "wednesday", "thursday", "friday":
		return 1.2 // Weekdays - more traffic
	case "saturday":
		return 1.1 // Saturday - moderate traffic
	case "sunday":
		return 0.9 // Sunday - less traffic
	default:
		// Auto-detect based on current day
		weekday := time.Now().Weekday()
		if weekday >= time.Monday && weekday <= time.Friday {
			return 1.2
		} else if weekday == time.Saturday {
			return 1.1
		} else {
			return 0.9
		}
	}
}

// getDeliveryStatistics retrieves delivery statistics
func (h *DeliveryPredictionsHandler) getDeliveryStatistics(agentID *uuid.UUID, days int) (map[string]interface{}, error) {
	// This would be implemented with proper database queries
	// For now, return mock data structure
	stats := map[string]interface{}{
		"total_deliveries":     150,
		"average_delivery_time": 45.5,
		"on_time_percentage":   87.3,
		"fastest_delivery":     12,
		"slowest_delivery":     180,
		"peak_hours": map[string]interface{}{
			"morning":   25,
			"afternoon": 45,
			"evening":   55,
			"night":     25,
		},
		"day_of_week_performance": map[string]interface{}{
			"monday":    88.5,
			"tuesday":   89.2,
			"wednesday": 86.8,
			"thursday":  87.9,
			"friday":    85.4,
			"saturday":  91.2,
			"sunday":    93.1,
		},
	}

	return stats, nil
}

// getAgentPerformanceMetrics retrieves agent performance metrics
func (h *DeliveryPredictionsHandler) getAgentPerformanceMetrics(agentID uuid.UUID, days int) (map[string]interface{}, error) {
	// This would be implemented with proper database queries
	// For now, return mock data structure
	performance := map[string]interface{}{
		"agent_id":              agentID.String(),
		"total_deliveries":      75,
		"average_delivery_time": 42.3,
		"on_time_rate":         89.5,
		"customer_rating":      4.7,
		"efficiency_score":     92.1,
		"recent_trends": map[string]interface{}{
			"improving":     true,
			"trend_percent": 5.2,
		},
	}

	return performance, nil
}

package handlers

import (
	"net/http"
	"strconv"
	"time"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type TemperatureHandler struct {
	temperatureRepo *repository.TemperatureRepository
	tripRepo        *repository.TripRepository
}

// TemperatureReadingRequest represents a temperature reading from a sensor
type TemperatureReadingRequest struct {
	TripID          string  `json:"trip_id" validate:"required"`
	AgentID         string  `json:"agent_id" validate:"required"`
	SensorID        string  `json:"sensor_id"`
	TemperatureC    float64 `json:"temperature_c" validate:"required"`
	HumidityPct     *float64 `json:"humidity_pct,omitempty" validate:"omitempty,min=0,max=100"`
	BatteryLevel    *float64 `json:"battery_level,omitempty" validate:"omitempty,min=0,max=100"`
	SignalStrength  *int    `json:"signal_strength,omitempty"`
	Latitude        float64 `json:"latitude" validate:"required,min=-90,max=90"`
	Longitude       float64 `json:"longitude" validate:"required,min=-180,max=180"`
	RecordedAt      string  `json:"recorded_at"` // ISO 8601 timestamp
}

// TemperatureRequirementsRequest represents temperature requirements for a trip
type TemperatureRequirementsRequest struct {
	TripID              string  `json:"trip_id" validate:"required"`
	MinTemperature      float64 `json:"min_temperature" validate:"required"`
	MaxTemperature      float64 `json:"max_temperature" validate:"required"`
	MaxHumidity         *float64 `json:"max_humidity,omitempty" validate:"omitempty,min=0,max=100"`
	AlertThresholdC     *float64 `json:"alert_threshold_c,omitempty"`
	ComplianceStandard  string  `json:"compliance_standard,omitempty"` // "HACCP", "FDA", "EMA", etc.
	SpecialInstructions string  `json:"special_instructions,omitempty"`
}

func NewTemperatureHandler(temperatureRepo *repository.TemperatureRepository, tripRepo *repository.TripRepository) *TemperatureHandler {
	return &TemperatureHandler{
		temperatureRepo: temperatureRepo,
		tripRepo:        tripRepo,
	}
}

// RecordTemperatureReading records a temperature reading from a sensor
// POST /api/temperature/reading
func (h *TemperatureHandler) RecordTemperatureReading(c *gin.Context) {
	var request TemperatureReadingRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid temperature reading request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("Temperature reading validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Parse UUIDs
	tripID, err := uuid.Parse(request.TripID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	agentID, err := uuid.Parse(request.AgentID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	// Parse recorded time
	var recordedAt time.Time
	if request.RecordedAt != "" {
		recordedAt, err = time.Parse(time.RFC3339, request.RecordedAt)
		if err != nil {
			recordedAt = time.Now()
		}
	} else {
		recordedAt = time.Now()
	}

	// Create temperature reading
	reading := &models.TemperatureReading{
		TripID:         tripID,
		AgentID:        agentID,
		SensorID:       request.SensorID,
		TemperatureC:   request.TemperatureC,
		HumidityPct:    request.HumidityPct,
		BatteryLevel:   request.BatteryLevel,
		SignalStrength: request.SignalStrength,
		Location:       models.Point{Latitude: request.Latitude, Longitude: request.Longitude},
		RecordedAt:     recordedAt,
	}

	// Save temperature reading
	if err := h.temperatureRepo.CreateTemperatureReading(reading); err != nil {
		logrus.WithError(err).Error("Failed to save temperature reading")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to save temperature reading", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":     tripID,
		"agent_id":    agentID,
		"temperature": request.TemperatureC,
		"sensor_id":   request.SensorID,
	}).Info("Temperature reading recorded successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Temperature reading recorded successfully", map[string]interface{}{
		"reading_id":   reading.ID,
		"temperature":  reading.TemperatureC,
		"recorded_at":  reading.RecordedAt,
	}))
}

// GetTemperatureHistory retrieves temperature history for a trip
// GET /api/temperature/history/:trip_id
func (h *TemperatureHandler) GetTemperatureHistory(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := uuid.Parse(tripIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	// Parse query parameters
	hoursStr := c.DefaultQuery("hours", "24")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil || hours <= 0 {
		hours = 24
	}

	// Get temperature history
	readings, err := h.temperatureRepo.GetTemperatureHistory(tripID, hours)
	if err != nil {
		logrus.WithError(err).Error("Failed to get temperature history")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get temperature history", err.Error()))
		return
	}

	// Get trip temperature requirements
	trip, err := h.tripRepo.GetTripByID(tripID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip details")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip details", err.Error()))
		return
	}

	response := map[string]interface{}{
		"trip_id":                tripID,
		"temperature_readings":   readings,
		"total_readings":         len(readings),
		"temperature_requirements": trip.TemperatureRequirements,
		"is_temperature_sensitive": trip.IsTemperatureSensitive,
		"hours_requested":        hours,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Temperature history retrieved successfully", response))
}

// GetTemperatureAlerts retrieves temperature alerts for a trip or agent
// GET /api/temperature/alerts
func (h *TemperatureHandler) GetTemperatureAlerts(c *gin.Context) {
	tripIDStr := c.Query("trip_id")
	agentIDStr := c.Query("agent_id")
	severityStr := c.Query("severity")
	unresolvedOnly := c.Query("unresolved_only") == "true"

	var tripID, agentID *uuid.UUID
	var err error

	if tripIDStr != "" {
		id, err := uuid.Parse(tripIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
			return
		}
		tripID = &id
	}

	if agentIDStr != "" {
		id, err := uuid.Parse(agentIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
			return
		}
		agentID = &id
	}

	// Get temperature alerts
	alerts, err := h.temperatureRepo.GetTemperatureAlerts(tripID, agentID, severityStr, unresolvedOnly)
	if err != nil {
		logrus.WithError(err).Error("Failed to get temperature alerts")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get temperature alerts", err.Error()))
		return
	}

	response := map[string]interface{}{
		"alerts":         alerts,
		"total_alerts":   len(alerts),
		"trip_id":        tripIDStr,
		"agent_id":       agentIDStr,
		"severity":       severityStr,
		"unresolved_only": unresolvedOnly,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Temperature alerts retrieved successfully", response))
}

// SetTemperatureRequirements sets temperature requirements for a trip
// POST /api/temperature/requirements
func (h *TemperatureHandler) SetTemperatureRequirements(c *gin.Context) {
	var request TemperatureRequirementsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		logrus.WithError(err).Error("Invalid temperature requirements request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&request); err != nil {
		logrus.WithError(err).Error("Temperature requirements validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Parse trip ID
	tripID, err := uuid.Parse(request.TripID)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	// Validate temperature range
	if request.MinTemperature >= request.MaxTemperature {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid temperature range", "Minimum temperature must be less than maximum temperature"))
		return
	}

	// Create temperature requirements object
	requirements := map[string]interface{}{
		"min_temperature":      request.MinTemperature,
		"max_temperature":      request.MaxTemperature,
		"compliance_standard":  request.ComplianceStandard,
		"special_instructions": request.SpecialInstructions,
	}

	if request.MaxHumidity != nil {
		requirements["max_humidity"] = *request.MaxHumidity
	}

	if request.AlertThresholdC != nil {
		requirements["alert_threshold_c"] = *request.AlertThresholdC
	}

	// Update trip with temperature requirements
	if err := h.tripRepo.SetTemperatureRequirements(tripID, requirements); err != nil {
		logrus.WithError(err).Error("Failed to set temperature requirements")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to set temperature requirements", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"trip_id":         tripID,
		"min_temperature": request.MinTemperature,
		"max_temperature": request.MaxTemperature,
		"compliance":      request.ComplianceStandard,
	}).Info("Temperature requirements set successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Temperature requirements set successfully", map[string]interface{}{
		"trip_id":      tripID,
		"requirements": requirements,
	}))
}

// ResolveTemperatureAlert marks a temperature alert as resolved
// PUT /api/temperature/alerts/:alert_id/resolve
func (h *TemperatureHandler) ResolveTemperatureAlert(c *gin.Context) {
	alertIDStr := c.Param("alert_id")
	alertID, err := uuid.Parse(alertIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid alert ID format", err.Error()))
		return
	}

	// Get resolution note from request body
	var request struct {
		ResolutionNote string `json:"resolution_note"`
	}
	c.ShouldBindJSON(&request)

	// Resolve the alert
	if err := h.temperatureRepo.ResolveTemperatureAlert(alertID, request.ResolutionNote); err != nil {
		logrus.WithError(err).Error("Failed to resolve temperature alert")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to resolve temperature alert", err.Error()))
		return
	}

	logrus.WithField("alert_id", alertID).Info("Temperature alert resolved successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Temperature alert resolved successfully", map[string]interface{}{
		"alert_id":        alertID,
		"resolved_at":     time.Now(),
		"resolution_note": request.ResolutionNote,
	}))
}

// GetTemperatureDashboard retrieves temperature monitoring dashboard data
// GET /api/temperature/dashboard
func (h *TemperatureHandler) GetTemperatureDashboard(c *gin.Context) {
	agentIDStr := c.Query("agent_id")
	var agentID *uuid.UUID

	if agentIDStr != "" {
		id, err := uuid.Parse(agentIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
			return
		}
		agentID = &id
	}

	// Get dashboard data
	dashboardData, err := h.temperatureRepo.GetTemperatureDashboard(agentID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get temperature dashboard data")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get temperature dashboard data", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Temperature dashboard data retrieved successfully", dashboardData))
}

package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// RoutingHandler handles routing and directions API proxy requests
type RoutingHandler struct {
	olaAPIKey string
}

// NewRoutingHandler creates a new routing handler
func NewRoutingHandler() *RoutingHandler {
	// Use the same API key as frontend
	apiKey := "dIrHt1iSmxED4qrHKnABwVRfKkzYQUez0BFR73kQ"
	
	return &RoutingHandler{
		olaAPIKey: apiKey,
	}
}

// GetDirections proxies OLA Maps directions API to avoid CORS issues
// GET /api/routing/directions?origin=lat,lng&destination=lat,lng
func (h *RoutingHandler) GetDirections(c *gin.Context) {
	origin := c.Query("origin")
	destination := c.Query("destination")

	if origin == "" || destination == "" {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Missing parameters", "Both origin and destination are required"))
		return
	}
	
	// Try different OLA Maps endpoints in order
	endpoints := []string{
		"https://api.olamaps.io/routing/v1/directions",
		"https://api.olamaps.io/routing/v1/directions-basic",
	}
	
	for _, endpoint := range endpoints {
		logrus.WithFields(logrus.Fields{
			"endpoint": endpoint,
			"origin": origin,
			"destination": destination,
		}).Info("Trying routing endpoint")
		
		result, err := h.callOLAAPI(endpoint, origin, destination)
		if err == nil && result != nil {
			c.JSON(http.StatusOK, utils.SuccessResponse("Route retrieved successfully", result))
			return
		}
		
		logrus.WithError(err).WithField("endpoint", endpoint).Warn("Routing endpoint failed")
	}
	
	// If all endpoints fail, return error
	c.JSON(http.StatusServiceUnavailable, utils.ErrorResponse("Routing service unavailable", "All routing endpoints failed"))
}

// GetDistanceMatrix proxies OLA Maps distance matrix API
// GET /api/routing/distance-matrix?origins=lat,lng&destinations=lat,lng
func (h *RoutingHandler) GetDistanceMatrix(c *gin.Context) {
	origins := c.Query("origins")
	destinations := c.Query("destinations")

	if origins == "" || destinations == "" {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Missing parameters", "Both origins and destinations are required"))
		return
	}
	
	// Try different distance matrix endpoints
	endpoints := []string{
		"https://api.olamaps.io/routing/v1/distanceMatrix",
		"https://api.olamaps.io/routing/v1/distance-matrix-basic",
	}
	
	for _, endpoint := range endpoints {
		logrus.WithFields(logrus.Fields{
			"endpoint": endpoint,
			"origins": origins,
			"destinations": destinations,
		}).Info("Trying distance matrix endpoint")
		
		result, err := h.callOLADistanceAPI(endpoint, origins, destinations)
		if err == nil && result != nil {
			c.JSON(http.StatusOK, utils.SuccessResponse("Distance matrix retrieved successfully", result))
			return
		}
		
		logrus.WithError(err).WithField("endpoint", endpoint).Warn("Distance matrix endpoint failed")
	}
	
	// If all endpoints fail, return error
	c.JSON(http.StatusServiceUnavailable, utils.ErrorResponse("Distance matrix service unavailable", "All distance matrix endpoints failed"))
}

// callOLAAPI makes the actual API call to OLA Maps routing endpoints
func (h *RoutingHandler) callOLAAPI(endpoint, origin, destination string) (interface{}, error) {
	// Build URL with parameters
	apiURL := fmt.Sprintf("%s?origin=%s&destination=%s&api_key=%s", 
		endpoint, 
		url.QueryEscape(origin), 
		url.QueryEscape(destination), 
		h.olaAPIKey)
	
	// Try alternative parameter names if first attempt fails
	if endpoint == "https://api.olamaps.io/routing/v1/directions" {
		// Some endpoints might use different parameter names
		altURL := fmt.Sprintf("%s?start=%s&end=%s&api_key=%s", 
			endpoint, 
			url.QueryEscape(origin), 
			url.QueryEscape(destination), 
			h.olaAPIKey)
		
		// Try both parameter formats
		for _, tryURL := range []string{apiURL, altURL} {
			result, err := h.makeHTTPRequest(tryURL)
			if err == nil {
				return result, nil
			}
		}
		return nil, fmt.Errorf("both parameter formats failed")
	}
	
	return h.makeHTTPRequest(apiURL)
}

// callOLADistanceAPI makes API calls to distance matrix endpoints
func (h *RoutingHandler) callOLADistanceAPI(endpoint, origins, destinations string) (interface{}, error) {
	apiURL := fmt.Sprintf("%s?origins=%s&destinations=%s&api_key=%s", 
		endpoint, 
		url.QueryEscape(origins), 
		url.QueryEscape(destinations), 
		h.olaAPIKey)
	
	return h.makeHTTPRequest(apiURL)
}

// makeHTTPRequest performs the actual HTTP request
func (h *RoutingHandler) makeHTTPRequest(apiURL string) (interface{}, error) {
	resp, err := http.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d", resp.StatusCode)
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	
	var result interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	return result, nil
}

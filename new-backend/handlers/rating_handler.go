package handlers

import (
	"net/http"
	"strconv"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type RatingHandler struct {
	ratingRepo *repository.RatingRepository
	tripRepo   *repository.TripRepository
	userRepo   *repository.UserRepository
}

func NewRatingHandler(ratingRepo *repository.RatingRepository, tripRepo *repository.TripRepository, userRepo *repository.UserRepository) *RatingHandler {
	return &RatingHandler{
		ratingRepo: ratingRepo,
		tripRepo:   tripRepo,
		userRepo:   userRepo,
	}
}

// CreateRating creates a new rating
// POST /api/ratings/create
func (h *RatingHandler) CreateRating(c *gin.Context) {
	var req models.CreateRatingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid create rating request")
		c.JSO<PERSON>(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Create rating validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Verify trip exists and is completed
	trip, err := h.tripRepo.GetTripByID(req.TripID)
	if err != nil {
		if err.Error() == "trip not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Trip not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get trip for rating")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip", err.Error()))
		return
	}

	if trip.Status != models.TripStatusDelivered && trip.Status != models.TripStatusDelivered {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip status", 
			"Rating can only be created for completed or delivered trips"))
		return
	}

	// Verify users exist and are part of the trip
	_, err = h.userRepo.GetUserByID(req.FromUser)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("From user not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get from user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get from user", err.Error()))
		return
	}

	_, err = h.userRepo.GetUserByID(req.ToUser)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("To user not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get to user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get to user", err.Error()))
		return
	}

	// Validate that users are part of the trip
	if (req.FromUser != trip.CustomerID && req.FromUser != trip.AgentID) ||
		(req.ToUser != trip.CustomerID && req.ToUser != trip.AgentID) {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid rating participants", 
			"Both users must be participants in the trip"))
		return
	}

	// Validate that users are not rating themselves
	if req.FromUser == req.ToUser {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid rating", 
			"Users cannot rate themselves"))
		return
	}

	// Create the rating
	rating, err := h.ratingRepo.CreateRating(&req)
	if err != nil {
		if err.Error() == "rating already exists for this trip and user combination" {
			c.JSON(http.StatusConflict, utils.ErrorResponse("Rating already exists", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to create rating")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create rating", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"rating_id": rating.ID,
		"trip_id":   rating.TripID,
		"from_user": rating.FromUser,
		"to_user":   rating.ToUser,
		"rating":    rating.Rating,
	}).Info("Rating created successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Rating created successfully", rating))
}

// GetRating retrieves a rating by ID
// GET /api/ratings/:rating_id
func (h *RatingHandler) GetRating(c *gin.Context) {
	ratingIDStr := c.Param("rating_id")
	ratingID, err := uuid.Parse(ratingIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid rating ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid rating ID format", err.Error()))
		return
	}

	rating, err := h.ratingRepo.GetRatingByID(ratingID)
	if err != nil {
		if err.Error() == "rating not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Rating not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get rating")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get rating", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Rating retrieved successfully", rating))
}

// GetUserRatings retrieves ratings for a user
// GET /api/ratings/user/:user_id
func (h *RatingHandler) GetUserRatings(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	// Check if user exists
	user, err := h.userRepo.GetUserByID(userID)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("User not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get user")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get user", err.Error()))
		return
	}

	// Parse query parameter for type (received or given)
	ratingType := c.DefaultQuery("type", "received")
	asReceiver := ratingType == "received"

	ratings, err := h.ratingRepo.GetRatingsByUser(userID, asReceiver)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user ratings")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get ratings", err.Error()))
		return
	}

	// Get rating statistics
	stats, err := h.ratingRepo.GetUserRatingStats(userID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user rating stats")
		stats = map[string]interface{}{} // Continue without stats if there's an error
	}

	response := map[string]interface{}{
		"user_id":       userID,
		"user_name":     user.Name,
		"rating_type":   ratingType,
		"total_ratings": len(ratings),
		"ratings":       ratings,
		"statistics":    stats,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("User ratings retrieved successfully", response))
}

// GetTripRatings retrieves all ratings for a trip
// GET /api/ratings/trip/:trip_id
func (h *RatingHandler) GetTripRatings(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := uuid.Parse(tripIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid trip ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	// Verify trip exists
	trip, err := h.tripRepo.GetTripByID(tripID)
	if err != nil {
		if err.Error() == "trip not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Trip not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get trip")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip", err.Error()))
		return
	}

	ratings, err := h.ratingRepo.GetRatingsByTrip(tripID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip ratings")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get ratings", err.Error()))
		return
	}

	response := map[string]interface{}{
		"trip_id":       tripID,
		"trip_status":   trip.Status,
		"total_ratings": len(ratings),
		"ratings":       ratings,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip ratings retrieved successfully", response))
}

// GetTopRatedAgents retrieves top-rated agents
// GET /api/ratings/top-agents
func (h *RatingHandler) GetTopRatedAgents(c *gin.Context) {
	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 10
	}

	agents, err := h.ratingRepo.GetTopRatedAgents(limit)
	if err != nil {
		logrus.WithError(err).Error("Failed to get top rated agents")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get top rated agents", err.Error()))
		return
	}

	response := map[string]interface{}{
		"limit":       limit,
		"total_agents": len(agents),
		"agents":      agents,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Top rated agents retrieved successfully", response))
}

// GetRecentRatings retrieves recent ratings across the system
// GET /api/ratings/recent
func (h *RatingHandler) GetRecentRatings(c *gin.Context) {
	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	ratings, err := h.ratingRepo.GetRecentRatings(limit)
	if err != nil {
		logrus.WithError(err).Error("Failed to get recent ratings")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get recent ratings", err.Error()))
		return
	}

	response := map[string]interface{}{
		"limit":         limit,
		"total_ratings": len(ratings),
		"ratings":       ratings,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Recent ratings retrieved successfully", response))
}

// DeleteRating deletes a rating
// DELETE /api/ratings/:rating_id
func (h *RatingHandler) DeleteRating(c *gin.Context) {
	ratingIDStr := c.Param("rating_id")
	ratingID, err := uuid.Parse(ratingIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid rating ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid rating ID format", err.Error()))
		return
	}

	// Verify rating exists
	rating, err := h.ratingRepo.GetRatingByID(ratingID)
	if err != nil {
		if err.Error() == "rating not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Rating not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get rating for deletion")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get rating", err.Error()))
		return
	}

	// Delete the rating
	if err := h.ratingRepo.DeleteRating(ratingID); err != nil {
		if err.Error() == "rating not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Rating not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to delete rating")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to delete rating", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"rating_id": ratingID,
		"trip_id":   rating.TripID,
		"from_user": rating.FromUser,
		"to_user":   rating.ToUser,
	}).Info("Rating deleted successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Rating deleted successfully", nil))
}

package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"
)

type DeliveryAssignmentHandler struct {
	assignmentRepo *repository.DeliveryAssignmentRepository
}

func NewDeliveryAssignmentHandler(assignmentRepo *repository.DeliveryAssignmentRepository) *DeliveryAssignmentHandler {
	return &DeliveryAssignmentHandler{
		assignmentRepo: assignmentRepo,
	}
}

// CreateAssignment creates a new delivery assignment with automatic geofencing
// POST /api/delivery-assignments
func (h *DeliveryAssignmentHandler) CreateAssignment(c *gin.Context) {
	var req models.CreateDeliveryAssignmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid request body for delivery assignment creation")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request body", err.Error()))
		return
	}

	// Validate UUIDs
	if req.AgentID == uuid.Nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	if req.CustomerID == uuid.Nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid customer ID", "Customer ID must be a valid UUID"))
		return
	}

	// Validate task type
	validTaskTypes := map[models.TaskType]bool{
		models.TaskTypePickOnly:    true,
		models.TaskTypeDropOnly:    true,
		models.TaskTypeStoreVisit:  true,
		models.TaskTypeFullDelivery: true,
		models.TaskTypeCustom:      true,
	}

	if !validTaskTypes[req.TaskType] {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid task type", 
			"Task type must be one of: pick_only, drop_only, store_visit, full_delivery, custom"))
		return
	}

	// Create the assignment
	assignment, err := h.assignmentRepo.CreateAssignment(req)
	if err != nil {
		logrus.WithError(err).Error("Failed to create delivery assignment")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create assignment", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"assignment_id": assignment.ID,
		"agent_id":     req.AgentID,
		"task_type":    req.TaskType,
		"geofence_radius": req.GeofenceRadius,
	}).Info("Successfully created delivery assignment")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Assignment created successfully", assignment))
}

// GetAssignmentsByAgent retrieves all assignments for a specific agent
// GET /api/delivery-assignments/agent/:agent_id
func (h *DeliveryAssignmentHandler) GetAssignmentsByAgent(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	assignments, err := h.assignmentRepo.GetAssignmentsByAgent(agentID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get assignments by agent")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get assignments", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Assignments retrieved successfully", map[string]interface{}{
		"assignments": assignments,
		"total_count": len(assignments),
	}))
}

// GetAssignmentByID retrieves a specific assignment by ID
// GET /api/delivery-assignments/:assignment_id
func (h *DeliveryAssignmentHandler) GetAssignmentByID(c *gin.Context) {
	assignmentIDStr := c.Param("assignment_id")
	assignmentID, err := uuid.Parse(assignmentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid assignment ID", "Assignment ID must be a valid UUID"))
		return
	}

	assignment, err := h.assignmentRepo.GetAssignmentByID(assignmentID)
	if err != nil {
		if err.Error() == "assignment not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Assignment not found", "No assignment found with the given ID"))
			return
		}
		logrus.WithError(err).Error("Failed to get assignment by ID")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get assignment", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Assignment retrieved successfully", assignment))
}

// UpdateAssignmentStatus updates the status of a delivery assignment
// PATCH /api/delivery-assignments/:assignment_id/status
func (h *DeliveryAssignmentHandler) UpdateAssignmentStatus(c *gin.Context) {
	assignmentIDStr := c.Param("assignment_id")
	assignmentID, err := uuid.Parse(assignmentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid assignment ID", "Assignment ID must be a valid UUID"))
		return
	}

	var req struct {
		Status models.TripStatus `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request body", err.Error()))
		return
	}

	// Validate status
	validStatuses := map[models.TripStatus]bool{
		models.TripStatusRequested:      true,
		models.TripStatusAccepted:       true,
		models.TripStatusPickedUp:       true,
		models.TripStatusInTransit:      true,
		models.TripStatusOutForDelivery: true,
		models.TripStatusDelivered:      true,
		models.TripStatusFailed:         true,
		models.TripStatusRescheduled:    true,
		models.TripStatusCancelled:      true,
		models.TripStatusReturned:       true,
	}

	if !validStatuses[req.Status] {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid status", 
			"Status must be a valid delivery status"))
		return
	}

	err = h.assignmentRepo.UpdateAssignmentStatus(assignmentID, req.Status)
	if err != nil {
		if err.Error() == "assignment not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Assignment not found", "No assignment found with the given ID"))
			return
		}
		logrus.WithError(err).Error("Failed to update assignment status")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update status", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Assignment status updated successfully", map[string]interface{}{
		"assignment_id": assignmentID,
		"new_status":   req.Status,
	}))
}

// CheckGeofenceStatus checks if an agent is within the geofence of their assignments
// GET /api/delivery-assignments/agent/:agent_id/geofence-status?lat=X&lng=Y
func (h *DeliveryAssignmentHandler) CheckGeofenceStatus(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	// Get current location from query parameters
	latStr := c.Query("lat")
	lngStr := c.Query("lng")

	if latStr == "" || lngStr == "" {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Missing location parameters", 
			"Both 'lat' and 'lng' query parameters are required"))
		return
	}

	lat, err := strconv.ParseFloat(latStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid latitude", "Latitude must be a valid number"))
		return
	}

	lng, err := strconv.ParseFloat(lngStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid longitude", "Longitude must be a valid number"))
		return
	}

	// Validate coordinates
	if lat < -90 || lat > 90 {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid latitude", "Latitude must be between -90 and 90"))
		return
	}

	if lng < -180 || lng > 180 {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid longitude", "Longitude must be between -180 and 180"))
		return
	}

	currentLocation := models.Point{
		Latitude:  lat,
		Longitude: lng,
	}

	assignments, err := h.assignmentRepo.CheckGeofenceStatus(agentID, currentLocation)
	if err != nil {
		logrus.WithError(err).Error("Failed to check geofence status")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to check geofence status", err.Error()))
		return
	}

	// Count assignments within geofence
	withinGeofenceCount := 0
	for _, assignment := range assignments {
		if assignment.IsWithinGeofence {
			withinGeofenceCount++
		}
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Geofence status checked successfully", map[string]interface{}{
		"agent_id":              agentID,
		"current_location":      currentLocation,
		"assignments":           assignments,
		"total_assignments":     len(assignments),
		"within_geofence_count": withinGeofenceCount,
	}))
}

// GetTaskTypes returns available task types for delivery assignments
// GET /api/delivery-assignments/task-types
func (h *DeliveryAssignmentHandler) GetTaskTypes(c *gin.Context) {
	taskTypes := []map[string]interface{}{
		{
			"value":       models.TaskTypePickOnly,
			"label":       "Pick Only",
			"description": "Only pick up items from specified location",
		},
		{
			"value":       models.TaskTypeDropOnly,
			"label":       "Drop Only", 
			"description": "Only deliver items to specified location",
		},
		{
			"value":       models.TaskTypeStoreVisit,
			"label":       "Store Visit",
			"description": "Visit store for inventory check or other tasks",
		},
		{
			"value":       models.TaskTypeFullDelivery,
			"label":       "Full Delivery",
			"description": "Complete pickup and delivery cycle",
		},
		{
			"value":       models.TaskTypeCustom,
			"label":       "Custom Task",
			"description": "Custom task with specific requirements",
		},
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Task types retrieved successfully", map[string]interface{}{
		"task_types": taskTypes,
		"default_geofence_radius": 400, // meters
		"min_geofence_radius":     50,  // meters
		"max_geofence_radius":     2000, // meters
	}))
}

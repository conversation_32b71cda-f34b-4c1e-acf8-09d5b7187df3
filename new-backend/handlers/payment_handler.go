package handlers

import (
	"net/http"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type PaymentHandler struct {
	paymentRepo *repository.PaymentRepository
	tripRepo    *repository.TripRepository
}

func NewPaymentHandler(paymentRepo *repository.PaymentRepository, tripRepo *repository.TripRepository) *PaymentHandler {
	return &PaymentHandler{
		paymentRepo: paymentRepo,
		tripRepo:    tripRepo,
	}
}

// CreatePayment creates a new payment record
// POST /api/payments/create
func (h *PaymentHandler) CreatePayment(c *gin.Context) {
	var req models.CreatePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid create payment request")
		c.JSO<PERSON>(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Create payment validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Verify trip exists and is completed
	trip, err := h.tripRepo.GetTripByID(req.TripID)
	if err != nil {
		if err.Error() == "trip not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Trip not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get trip for payment")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get trip", err.Error()))
		return
	}

	if trip.Status != models.TripStatusDelivered && trip.Status != models.TripStatusDelivered {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip status", 
			"Payment can only be created for completed or delivered trips"))
		return
	}

	// Check if payment already exists for this trip
	existingPayment, err := h.paymentRepo.GetPaymentByTripID(req.TripID)
	if err == nil && existingPayment != nil {
		c.JSON(http.StatusConflict, utils.ErrorResponse("Payment already exists", 
			"A payment record already exists for this trip"))
		return
	}

	// Create the payment
	payment, err := h.paymentRepo.CreatePayment(&req)
	if err != nil {
		logrus.WithError(err).Error("Failed to create payment")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create payment", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"payment_id": payment.PaymentID,
		"trip_id":    payment.TripID,
		"amount":     payment.Amount,
	}).Info("Payment created successfully")

	c.JSON(http.StatusCreated, utils.SuccessResponse("Payment created successfully", payment))
}

// GetPayment retrieves a payment by ID
// GET /api/payments/:payment_id
func (h *PaymentHandler) GetPayment(c *gin.Context) {
	paymentIDStr := c.Param("payment_id")
	paymentID, err := uuid.Parse(paymentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid payment ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid payment ID format", err.Error()))
		return
	}

	payment, err := h.paymentRepo.GetPaymentByID(paymentID)
	if err != nil {
		if err.Error() == "payment not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Payment not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get payment")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get payment", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Payment retrieved successfully", payment))
}

// UpdatePaymentStatus updates the status of a payment
// PUT /api/payments/:payment_id/status
func (h *PaymentHandler) UpdatePaymentStatus(c *gin.Context) {
	paymentIDStr := c.Param("payment_id")
	paymentID, err := uuid.Parse(paymentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid payment ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid payment ID format", err.Error()))
		return
	}

	var req struct {
		Status         models.PaymentStatus `json:"status" validate:"required"`
		TransactionRef *string              `json:"transaction_ref,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid payment status update request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Payment status update validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Verify payment exists
	payment, err := h.paymentRepo.GetPaymentByID(paymentID)
	if err != nil {
		if err.Error() == "payment not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Payment not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get payment for status update")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get payment", err.Error()))
		return
	}

	// Validate status transition
	if !isValidPaymentStatusTransition(payment.Status, req.Status) {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid status transition", 
			"Cannot change from "+string(payment.Status)+" to "+string(req.Status)))
		return
	}

	// Update payment status
	if err := h.paymentRepo.UpdatePaymentStatus(paymentID, req.Status, req.TransactionRef); err != nil {
		if err.Error() == "payment not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Payment not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to update payment status")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to update payment status", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"payment_id":  paymentID,
		"old_status":  payment.Status,
		"new_status":  req.Status,
		"transaction": req.TransactionRef,
	}).Info("Payment status updated successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Payment status updated successfully", nil))
}

// GetPaymentsByStatus retrieves payments by status
// GET /api/payments/status/:status
func (h *PaymentHandler) GetPaymentsByStatus(c *gin.Context) {
	statusStr := c.Param("status")
	status := models.PaymentStatus(statusStr)

	// Validate status
	if status != models.PaymentStatusPending && status != models.PaymentStatusPaid && status != models.PaymentStatusFailed {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid payment status", 
			"Status must be one of: pending, paid, failed"))
		return
	}

	payments, err := h.paymentRepo.GetPaymentsByStatus(status)
	if err != nil {
		logrus.WithError(err).Error("Failed to get payments by status")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get payments", err.Error()))
		return
	}

	response := map[string]interface{}{
		"status":        status,
		"total_payments": len(payments),
		"payments":      payments,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Payments retrieved successfully", response))
}

// GetPaymentStats retrieves payment statistics
// GET /api/payments/stats
func (h *PaymentHandler) GetPaymentStats(c *gin.Context) {
	stats, err := h.paymentRepo.GetPaymentStats()
	if err != nil {
		logrus.WithError(err).Error("Failed to get payment stats")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get payment stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Payment stats retrieved successfully", stats))
}

// GetUserPaymentHistory retrieves payment history for a user
// GET /api/payments/user/:user_id
func (h *PaymentHandler) GetUserPaymentHistory(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	// Get user type from query parameter
	userTypeStr := c.Query("user_type")
	if userTypeStr == "" {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Missing user_type parameter", 
			"user_type query parameter is required (customer or agent)"))
		return
	}

	userType := models.UserType(userTypeStr)
	if userType != models.UserTypeCustomer && userType != models.UserTypeAgent {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user_type", 
			"user_type must be either 'customer' or 'agent'"))
		return
	}

	payments, err := h.paymentRepo.GetUserPaymentHistory(userID, userType)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user payment history")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get payment history", err.Error()))
		return
	}

	response := map[string]interface{}{
		"user_id":       userID,
		"user_type":     userType,
		"total_payments": len(payments),
		"payments":      payments,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Payment history retrieved successfully", response))
}

// GetTripPayment retrieves payment for a specific trip
// GET /api/payments/trip/:trip_id
func (h *PaymentHandler) GetTripPayment(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := uuid.Parse(tripIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid trip ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid trip ID format", err.Error()))
		return
	}

	payment, err := h.paymentRepo.GetPaymentByTripID(tripID)
	if err != nil {
		if err.Error() == "payment not found" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Payment not found", "No payment found for this trip"))
			return
		}
		logrus.WithError(err).Error("Failed to get trip payment")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get payment", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Trip payment retrieved successfully", payment))
}

// Helper function to validate payment status transitions
func isValidPaymentStatusTransition(currentStatus, newStatus models.PaymentStatus) bool {
	validTransitions := map[models.PaymentStatus][]models.PaymentStatus{
		models.PaymentStatusPending: {models.PaymentStatusPaid, models.PaymentStatusFailed},
		models.PaymentStatusPaid:    {}, // Terminal state
		models.PaymentStatusFailed:  {models.PaymentStatusPending}, // Can retry
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, allowedStatus := range allowedStatuses {
		if newStatus == allowedStatus {
			return true
		}
	}

	return false
}

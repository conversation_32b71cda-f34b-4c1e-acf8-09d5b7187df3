package handlers

import (
	"image"
	"image/jpeg"
	"image/png"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"
)

type MarkerSettingsHandler struct {
	markerRepo *repository.MarkerSettingsRepository
}

func NewMarkerSettingsHandler(markerRepo *repository.MarkerSettingsRepository) *MarkerSettingsHandler {
	return &MarkerSettingsHandler{
		markerRepo: markerRepo,
	}
}

// GetMarkerSettings retrieves all custom marker settings for a user
func (h *MarkerSettingsHandler) GetMarkerSettings(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	settings, err := h.markerRepo.GetMarkerSettings(userID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get marker settings")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get marker settings", err.Error()))
		return
	}

	response := map[string]interface{}{
		"user_id":  userID,
		"settings": settings,
		"count":    len(settings),
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Marker settings retrieved successfully", response))
}

// GetMarkerSettingByType retrieves a specific marker setting
func (h *MarkerSettingsHandler) GetMarkerSettingByType(c *gin.Context) {
	userIDStr := c.Param("user_id")
	markerType := c.Param("marker_type")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	setting, err := h.markerRepo.GetMarkerSettingByType(userID, markerType)
	if err != nil {
		logrus.WithError(err).Error("Failed to get marker setting")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get marker setting", err.Error()))
		return
	}

	if setting == nil {
		c.JSON(http.StatusNotFound, utils.ErrorResponse("Marker setting not found", "No custom setting found for this marker type"))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Marker setting retrieved successfully", setting))
}

// SaveMarkerSetting creates or updates a custom marker setting
func (h *MarkerSettingsHandler) SaveMarkerSetting(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	var req struct {
		MarkerType   string  `json:"marker_type" validate:"required"`
		IconText     *string `json:"icon_text"`
		IconImageURL *string `json:"icon_image_url"`
		Color        string  `json:"color" validate:"required,len=7"`
		Size         int     `json:"size" validate:"required,min=16,max=64"`
		BorderColor  *string `json:"border_color"`
		BorderWidth  *int    `json:"border_width"`
		Enabled      *bool   `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request body", err.Error()))
		return
	}

	// Validate that either icon_text or icon_image_url is provided
	if (req.IconText == nil || *req.IconText == "") && (req.IconImageURL == nil || *req.IconImageURL == "") {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request", "Either icon_text or icon_image_url must be provided"))
		return
	}

	// Set default enabled value
	enabled := true
	if req.Enabled != nil {
		enabled = *req.Enabled
	}

	setting := &models.CustomMarkerSetting{
		UserID:       userID,
		MarkerType:   req.MarkerType,
		IconText:     req.IconText,
		IconImageURL: req.IconImageURL,
		Color:        req.Color,
		Size:         req.Size,
		BorderColor:  req.BorderColor,
		BorderWidth:  req.BorderWidth,
		Enabled:      enabled,
	}

	err = h.markerRepo.SaveMarkerSetting(setting)
	if err != nil {
		logrus.WithError(err).Error("Failed to save marker setting")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to save marker setting", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Marker setting saved successfully", setting))
}

// DeleteMarkerSetting deletes a custom marker setting
func (h *MarkerSettingsHandler) DeleteMarkerSetting(c *gin.Context) {
	userIDStr := c.Param("user_id")
	markerType := c.Param("marker_type")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	err = h.markerRepo.DeleteMarkerSetting(userID, markerType)
	if err != nil {
		logrus.WithError(err).Error("Failed to delete marker setting")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to delete marker setting", err.Error()))
		return
	}

	response := map[string]interface{}{
		"user_id":     userID,
		"marker_type": markerType,
		"deleted":     true,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Marker setting deleted successfully", response))
}

// UploadMarkerImage handles image upload for custom markers
func (h *MarkerSettingsHandler) UploadMarkerImage(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid user ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid user ID format", err.Error()))
		return
	}

	// Get uploaded file
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		logrus.WithError(err).Error("Failed to get uploaded file")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Failed to get uploaded file", err.Error()))
		return
	}
	defer file.Close()

	// Validate file size (max 2MB)
	maxSize := int64(2 * 1024 * 1024) // 2MB
	if header.Size > maxSize {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("File too large", "Maximum file size is 2MB"))
		return
	}

	// Validate file type
	contentType := header.Header.Get("Content-Type")
	if !isValidImageType(contentType) {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid file type", "Only PNG, JPG, JPEG, GIF, and WebP images are allowed"))
		return
	}

	// Create uploads directory if it doesn't exist
	uploadsDir := "uploads/markers"
	if err := createDirIfNotExists(uploadsDir); err != nil {
		logrus.WithError(err).Error("Failed to create uploads directory")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to create uploads directory", err.Error()))
		return
	}

	// Generate unique filename
	ext := getFileExtension(header.Filename)
	filename := userID.String() + "_" + uuid.New().String() + ext
	tempFilepath := uploadsDir + "/" + "temp_" + filename
	finalFilepath := uploadsDir + "/" + filename

	// Save file to temporary location first
	if err := c.SaveUploadedFile(header, tempFilepath); err != nil {
		logrus.WithError(err).Error("Failed to save uploaded file")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to save uploaded file", err.Error()))
		return
	}

	// Compress the image
	if err := compressImage(tempFilepath, finalFilepath); err != nil {
		logrus.WithError(err).Error("Failed to compress image")
		// If compression fails, use original file
		if err := os.Rename(tempFilepath, finalFilepath); err != nil {
			logrus.WithError(err).Error("Failed to move original file")
			c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to process image", err.Error()))
			return
		}
	} else {
		// Remove temporary file after successful compression
		os.Remove(tempFilepath)
	}

	// Return the URL that can be accessed by the frontend (relative path for proxy compatibility)
	imageURL := "/" + finalFilepath

	response := map[string]interface{}{
		"user_id":   userID,
		"filename":  header.Filename,
		"size":      header.Size,
		"image_url": imageURL,
		"filepath":  finalFilepath,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Image uploaded successfully", response))
}

// Helper functions for image upload
func isValidImageType(contentType string) bool {
	validTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}
	return false
}

func getFileExtension(filename string) string {
	return filepath.Ext(filename)
}

func createDirIfNotExists(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	}
	return nil
}

// compressImage compresses an image based on its format
func compressImage(inputPath, outputPath string) error {
	// Open the input file
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return err
	}
	defer inputFile.Close()

	// Decode the image
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return err
	}

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outputFile.Close()

	// Compress based on format
	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		// JPEG compression with quality 80
		return jpeg.Encode(outputFile, img, &jpeg.Options{Quality: 80})
	case "png":
		// PNG compression (lossless but optimized)
		encoder := png.Encoder{CompressionLevel: png.BestCompression}
		return encoder.Encode(outputFile, img)
	default:
		// Default to JPEG for unknown formats
		return jpeg.Encode(outputFile, img, &jpeg.Options{Quality: 80})
	}
}

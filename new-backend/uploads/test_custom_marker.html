<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Marker Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .marker-preview {
            display: inline-block;
            width: 32px;
            height: 32px;
            border: 2px solid #FFFFFF;
            border-radius: 50%;
            background-color: #4CAF50;
            position: relative;
            margin: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .marker-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            display: block;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🎯 Custom Marker Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. Image Upload Test</h2>
        <div id="upload-status" class="status info">Testing image upload...</div>
        <input type="file" id="imageInput" accept="image/*" />
        <button onclick="testImageUpload()">Upload Test Image</button>
        <div id="upload-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Marker Settings Test</h2>
        <div id="settings-status" class="status info">Testing marker settings...</div>
        <button onclick="testMarkerSettings()">Test Marker Settings</button>
        <div id="settings-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Custom Marker Preview</h2>
        <div id="preview-status" class="status info">Loading custom marker preview...</div>
        <div id="marker-preview-container"></div>
    </div>

    <script>
        const AGENT_ID = '114ea59d-f561-4697-b7f4-295a5013872b';
        const BASE_URL = 'http://localhost:8080';

        async function testImageUpload() {
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('upload-result').innerHTML = '<div class="status error">Please select a file first</div>';
                return;
            }

            try {
                const formData = new FormData();
                formData.append('image', file);

                const response = await fetch(`${BASE_URL}/api/markers/upload/${AGENT_ID}`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('upload-status').className = 'status success';
                    document.getElementById('upload-status').textContent = '✅ Image upload successful!';
                    document.getElementById('upload-result').innerHTML = `
                        <div class="status success">
                            <strong>Upload Success!</strong><br>
                            Image URL: ${result.data.image_url}<br>
                            File Size: ${(result.data.size / 1024).toFixed(2)} KB<br>
                            <img src="${BASE_URL}${result.data.image_url}" style="max-width: 100px; max-height: 100px; margin-top: 10px;" />
                        </div>
                    `;
                    
                    // Auto-test marker settings with uploaded image
                    await testMarkerSettingsWithImage(result.data.image_url);
                } else {
                    throw new Error(result.message || 'Upload failed');
                }
            } catch (error) {
                document.getElementById('upload-status').className = 'status error';
                document.getElementById('upload-status').textContent = '❌ Image upload failed!';
                document.getElementById('upload-result').innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        }

        async function testMarkerSettings() {
            try {
                // Test saving marker settings
                const settingData = {
                    marker_type: 'delivered',
                    icon_text: '✓',
                    icon_image_url: null,
                    color: '#4CAF50',
                    size: 32,
                    border_color: '#FFFFFF',
                    border_width: 2,
                    enabled: true
                };

                const saveResponse = await fetch(`${BASE_URL}/api/markers/settings/${AGENT_ID}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(settingData)
                });

                const saveResult = await saveResponse.json();
                
                if (saveResult.success) {
                    document.getElementById('settings-status').className = 'status success';
                    document.getElementById('settings-status').textContent = '✅ Marker settings saved successfully!';
                    
                    // Test retrieving marker settings
                    await testRetrieveMarkerSettings();
                } else {
                    throw new Error(saveResult.message || 'Save failed');
                }
            } catch (error) {
                document.getElementById('settings-status').className = 'status error';
                document.getElementById('settings-status').textContent = '❌ Marker settings test failed!';
                document.getElementById('settings-result').innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        }

        async function testMarkerSettingsWithImage(imageUrl) {
            try {
                const settingData = {
                    marker_type: 'delivered',
                    icon_text: '',
                    icon_image_url: imageUrl,
                    color: '#4CAF50',
                    size: 32,
                    border_color: '#FFFFFF',
                    border_width: 2,
                    enabled: true
                };

                const response = await fetch(`${BASE_URL}/api/markers/settings/${AGENT_ID}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(settingData)
                });

                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('settings-status').className = 'status success';
                    document.getElementById('settings-status').textContent = '✅ Marker settings with image saved!';
                    await loadMarkerPreview();
                }
            } catch (error) {
                console.error('Error saving marker with image:', error);
            }
        }

        async function testRetrieveMarkerSettings() {
            try {
                const response = await fetch(`${BASE_URL}/api/markers/settings/${AGENT_ID}`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('settings-result').innerHTML = `
                        <div class="status success">
                            <strong>Settings Retrieved:</strong><br>
                            Found ${result.data.count} custom marker settings<br>
                            <pre>${JSON.stringify(result.data.settings, null, 2)}</pre>
                        </div>
                    `;
                    await loadMarkerPreview();
                }
            } catch (error) {
                document.getElementById('settings-result').innerHTML = `<div class="status error">Error retrieving settings: ${error.message}</div>`;
            }
        }

        async function loadMarkerPreview() {
            try {
                const response = await fetch(`${BASE_URL}/api/markers/settings/${AGENT_ID}`);
                const result = await response.json();
                
                if (result.success && result.data.settings.length > 0) {
                    const container = document.getElementById('marker-preview-container');
                    container.innerHTML = '';
                    
                    result.data.settings.forEach(setting => {
                        const markerDiv = document.createElement('div');
                        markerDiv.style.display = 'inline-block';
                        markerDiv.style.margin = '10px';
                        markerDiv.style.textAlign = 'center';
                        
                        const markerElement = document.createElement('div');
                        markerElement.className = 'marker-preview';
                        markerElement.style.backgroundColor = setting.color;
                        markerElement.style.borderColor = setting.border_color;
                        markerElement.style.borderWidth = setting.border_width + 'px';
                        markerElement.style.width = setting.size + 'px';
                        markerElement.style.height = setting.size + 'px';
                        
                        if (setting.icon_image_url) {
                            const img = document.createElement('img');
                            img.src = BASE_URL + setting.icon_image_url;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'cover';
                            img.style.borderRadius = '50%';
                            img.onerror = () => {
                                markerElement.innerHTML = '❌';
                                markerElement.style.display = 'flex';
                                markerElement.style.alignItems = 'center';
                                markerElement.style.justifyContent = 'center';
                                markerElement.style.color = 'white';
                                markerElement.style.fontSize = '12px';
                            };
                            markerElement.appendChild(img);
                        } else {
                            markerElement.innerHTML = setting.icon_text || '•';
                            markerElement.style.display = 'flex';
                            markerElement.style.alignItems = 'center';
                            markerElement.style.justifyContent = 'center';
                            markerElement.style.color = 'white';
                            markerElement.style.fontSize = '12px';
                            markerElement.style.fontWeight = 'bold';
                        }
                        
                        const label = document.createElement('div');
                        label.textContent = setting.marker_type;
                        label.style.fontSize = '12px';
                        label.style.marginTop = '5px';
                        
                        markerDiv.appendChild(markerElement);
                        markerDiv.appendChild(label);
                        container.appendChild(markerDiv);
                    });
                    
                    document.getElementById('preview-status').className = 'status success';
                    document.getElementById('preview-status').textContent = '✅ Custom marker preview loaded!';
                } else {
                    document.getElementById('preview-status').className = 'status info';
                    document.getElementById('preview-status').textContent = 'No custom markers found. Create some using the tests above.';
                }
            } catch (error) {
                document.getElementById('preview-status').className = 'status error';
                document.getElementById('preview-status').textContent = '❌ Failed to load marker preview!';
            }
        }

        // Load marker preview on page load
        window.onload = loadMarkerPreview;
    </script>
</body>
</html>

#!/bin/bash

# Colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 API-Only Live Location Tester${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""
echo -e "${YELLOW}This script tests the API endpoints without requiring psql${NC}"
echo ""

# Function to show API response
show_api_response() {
    echo -e "${CYAN}🌐 Current API Response:${NC}"
    echo -e "${PURPLE}GET /api/tracking/active-agents${NC}"
    
    response=$(curl -s "http://localhost:8080/api/tracking/active-agents?within_minutes=60")
    
    if [ $? -eq 0 ]; then
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    else
        echo -e "${RED}❌ API call failed. Make sure backend is running on localhost:8080${NC}"
    fi
    echo ""
}

# Function to show simplified agent locations
show_simple_locations() {
    echo -e "${CYAN}📍 Agent Locations (Simplified):${NC}"
    
    response=$(curl -s "http://localhost:8080/api/tracking/active-agents?within_minutes=60")
    
    if [ $? -eq 0 ]; then
        # Extract just the essential info using basic text processing
        echo "$response" | grep -o '"name":"[^"]*"' | sed 's/"name":"//g' | sed 's/"//g' > /tmp/names.txt
        echo "$response" | grep -o '"latitude":[0-9.]*' | sed 's/"latitude"://g' > /tmp/lats.txt
        echo "$response" | grep -o '"longitude":[0-9.]*' | sed 's/"longitude"://g' > /tmp/lons.txt
        echo "$response" | grep -o '"battery_pct":[0-9.]*' | sed 's/"battery_pct"://g' > /tmp/batteries.txt
        echo "$response" | grep -o '"updated_at":"[^"]*"' | sed 's/"updated_at":"//g' | sed 's/"//g' > /tmp/times.txt
        
        printf "%-15s | %-8s | %-9s | %-7s | %-20s\n" "Name" "Latitude" "Longitude" "Battery" "Updated"
        echo "--------------------------------------------------------------------------------"
        
        paste /tmp/names.txt /tmp/lats.txt /tmp/lons.txt /tmp/batteries.txt /tmp/times.txt | while IFS=$'\t' read -r name lat lon battery time; do
            printf "%-15s | %8.4f | %9.4f | %5.0f%% | %-20s\n" "$name" "$lat" "$lon" "$battery" "${time:11:8}"
        done
        
        # Cleanup
        rm -f /tmp/names.txt /tmp/lats.txt /tmp/lons.txt /tmp/batteries.txt /tmp/times.txt
    else
        echo -e "${RED}❌ API call failed${NC}"
    fi
    echo ""
}

# Main menu loop
while true; do
    echo -e "${BLUE}📋 Choose an option:${NC}"
    echo -e "${CYAN}1${NC} - Show Full API Response"
    echo -e "${CYAN}2${NC} - Show Simplified Agent Locations"
    echo -e "${CYAN}3${NC} - Test Individual Agent Location (Mike Driver)"
    echo -e "${CYAN}4${NC} - Continuous Monitoring (updates every 5 seconds)"
    echo -e "${CYAN}q${NC} - Quit"
    echo ""
    echo -n -e "${YELLOW}Enter your choice: ${NC}"
    read choice
    echo ""
    
    case $choice in
        1)
            show_api_response
            ;;
        2)
            show_simple_locations
            ;;
        3)
            echo -e "${CYAN}🔍 Testing individual agent endpoint:${NC}"
            echo -e "${PURPLE}GET /api/tracking/live-location/{mike_driver_id}${NC}"
            
            # Get Mike Driver's ID from the active agents response
            mike_id=$(curl -s "http://localhost:8080/api/tracking/active-agents?within_minutes=60" | grep -A 10 -B 10 "Mike Driver" | grep -o '"agent_id":"[^"]*"' | head -1 | sed 's/"agent_id":"//g' | sed 's/"//g')
            
            if [ -n "$mike_id" ]; then
                echo "Mike Driver ID: $mike_id"
                curl -s "http://localhost:8080/api/tracking/live-location/$mike_id" | python3 -m json.tool 2>/dev/null || curl -s "http://localhost:8080/api/tracking/live-location/$mike_id"
            else
                echo -e "${RED}❌ Could not find Mike Driver's ID${NC}"
            fi
            echo ""
            ;;
        4)
            echo -e "${CYAN}📡 Continuous monitoring (Press Ctrl+C to stop)...${NC}"
            echo ""
            
            while true; do
                clear
                echo -e "${BLUE}🔄 Live Monitoring - $(date)${NC}"
                echo -e "${BLUE}=================================${NC}"
                show_simple_locations
                echo -e "${YELLOW}⏱️  Next update in 5 seconds... (Press Ctrl+C to stop)${NC}"
                sleep 5
            done
            ;;
        q|Q)
            echo -e "${GREEN}👋 Thanks for testing!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Invalid choice! Please enter 1-4 or q${NC}"
            echo ""
            ;;
    esac
    
    echo -e "${BLUE}=================================${NC}"
    echo ""
done

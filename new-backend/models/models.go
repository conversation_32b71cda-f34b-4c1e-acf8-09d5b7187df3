package models

import (
	"time"

	"github.com/google/uuid"
)

// User represents a user in the system (customer, agent, or admin)
type User struct {
	ID        uuid.UUID `json:"id" db:"id"`
	Name      string    `json:"name" db:"name" validate:"required,min=2,max=100"`
	Email     string    `json:"email" db:"email" validate:"required,email"`
	Phone     string    `json:"phone" db:"phone" validate:"required,min=10,max=15"`
	UserType  string    `json:"user_type" db:"user_type" validate:"required"` // Changed to string to match DB enum
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	IsActive  bool      `json:"is_active" db:"is_active"`
	SimpleID  *string   `json:"simple_id" db:"simple_id"` // Added simple_id field
}

// AgentProfile represents extended profile information for agents
type AgentProfile struct {
	UserID      uuid.UUID `json:"user_id" db:"user_id"`
	VehicleType *string   `json:"vehicle_type" db:"vehicle_type"`
	VehicleNo   *string   `json:"vehicle_no" db:"vehicle_no"`
	LicenseNo   *string   `json:"license_no" db:"license_no"`
	Rating      float64   `json:"rating" db:"rating"`
	IsAvailable bool      `json:"is_available" db:"is_available"`
}

// Trip represents a trip (ride or delivery)
type Trip struct {
	TripID                  uuid.UUID              `json:"trip_id" db:"trip_id"`
	TripType                TripType               `json:"trip_type" db:"trip_type" validate:"required"`
	CustomerID              uuid.UUID              `json:"customer_id" db:"customer_id" validate:"required"`
	AgentID                 uuid.UUID              `json:"agent_id" db:"agent_id" validate:"required"`
	PickupLocation          Point                  `json:"pickup_location" db:"pickup_location" validate:"required"`
	DropLocation            Point                  `json:"drop_location" db:"drop_location" validate:"required"`
	Status                  TripStatus             `json:"status" db:"status"`
	ItemDetails             *ItemDetails           `json:"item_details,omitempty" db:"item_details"`
	FareEstimate            *float64               `json:"fare_estimate,omitempty" db:"fare_estimate"`
	DeliveryFee             *float64               `json:"delivery_fee,omitempty" db:"delivery_fee"`
	TemperatureRequirements map[string]interface{} `json:"temperature_requirements,omitempty" db:"temperature_requirements"`
	IsTemperatureSensitive  bool                   `json:"is_temperature_sensitive" db:"is_temperature_sensitive"`
	CreatedAt               time.Time              `json:"created_at" db:"created_at"`
	CompletedAt             *time.Time             `json:"completed_at,omitempty" db:"completed_at"`

	// Additional fields for API responses (not stored in DB)
	CustomerName   string       `json:"customer_name,omitempty" db:"-"`
	CustomerPhone  string       `json:"customer_phone,omitempty" db:"-"`
}

// DeliveryAssignment represents a delivery task assignment with geofencing
type DeliveryAssignment struct {
	ID                  uuid.UUID      `json:"id" db:"id"`
	TripID              uuid.UUID      `json:"trip_id" db:"trip_id" validate:"required"`
	AgentID             uuid.UUID      `json:"agent_id" db:"agent_id" validate:"required"`
	CustomerID          uuid.UUID      `json:"customer_id" db:"customer_id" validate:"required"`
	TaskType            TaskType       `json:"task_type" db:"task_type" validate:"required"`
	TaskDescription     string         `json:"task_description" db:"task_description"`
	TargetLocation      Point          `json:"target_location" db:"target_location" validate:"required"`
	GeofenceRadius      int            `json:"geofence_radius" db:"geofence_radius" validate:"required,min=50,max=2000"` // meters
	Status              TripStatus     `json:"status" db:"status"`
	ScheduledDate       *time.Time     `json:"scheduled_date,omitempty" db:"scheduled_date"`
	CompletedAt         *time.Time     `json:"completed_at,omitempty" db:"completed_at"`
	Notes               *string        `json:"notes,omitempty" db:"notes"`
	ItemDetails         *ItemDetails   `json:"item_details,omitempty" db:"item_details"`
	DeliveryFee         *float64       `json:"delivery_fee,omitempty" db:"delivery_fee"`
	CreatedAt           time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at" db:"updated_at"`

	// Additional fields for API responses
	CustomerName        string         `json:"customer_name,omitempty" db:"-"`
	CustomerPhone       string         `json:"customer_phone,omitempty" db:"-"`
	AgentName           string         `json:"agent_name,omitempty" db:"-"`
	DistanceToTarget    *float64       `json:"distance_to_target,omitempty" db:"-"`
	IsWithinGeofence    bool           `json:"is_within_geofence" db:"-"`
}

// DeliveryGeofence represents a geographic boundary for delivery assignments
type DeliveryGeofence struct {
	ID              uuid.UUID      `json:"id" db:"id"`
	AssignmentID    uuid.UUID      `json:"assignment_id" db:"assignment_id" validate:"required"`
	CenterLocation  Point          `json:"center_location" db:"center_location" validate:"required"`
	RadiusMeters    int            `json:"radius_meters" db:"radius_meters" validate:"required,min=50,max=2000"`
	Status          GeofenceStatus `json:"status" db:"status"`
	CreatedAt       time.Time      `json:"created_at" db:"created_at"`
	ExpiresAt       *time.Time     `json:"expires_at,omitempty" db:"expires_at"`
}

// DeliveryGeofenceEvent represents entry/exit events for delivery geofences
type DeliveryGeofenceEvent struct {
	ID           uuid.UUID `json:"id" db:"id"`
	GeofenceID   uuid.UUID `json:"geofence_id" db:"geofence_id" validate:"required"`
	AgentID      uuid.UUID `json:"agent_id" db:"agent_id" validate:"required"`
	EventType    string    `json:"event_type" db:"event_type" validate:"required"` // "enter", "exit"
	Location     Point     `json:"location" db:"location" validate:"required"`
	Timestamp    time.Time `json:"timestamp" db:"timestamp" validate:"required"`
	DistanceM    float64   `json:"distance_m" db:"distance_m"`
}

// TripStatusUpdate represents a status change in a trip
type TripStatusUpdate struct {
	ID           uuid.UUID   `json:"id" db:"id"`
	TripID       uuid.UUID   `json:"trip_id" db:"trip_id" validate:"required"`
	Status       TripStatus  `json:"status" db:"status" validate:"required"`
	Timestamp    time.Time   `json:"timestamp" db:"timestamp"`
	Location     *Point      `json:"location,omitempty" db:"location"`
	UpdatedBy    *uuid.UUID  `json:"updated_by,omitempty" db:"updated_by"`
	Note         *string     `json:"note,omitempty" db:"note"`
	GeocodedName *string     `json:"geocoded_name,omitempty" db:"geocoded_name"`
}

// LiveLocation represents current real-time location of an agent
type LiveLocation struct {
	AgentID    uuid.UUID `json:"agent_id" db:"agent_id"`
	Name       string    `json:"name" db:"name"`
	SimpleID   *string   `json:"simple_id" db:"simple_id"`
	Role       string    `json:"role" db:"role" validate:"required"` // Changed to string
	TripID     *uuid.UUID `json:"trip_id,omitempty" db:"trip_id"`
	Geom       Point     `json:"location" db:"geom" validate:"required"`
	Timestamp  time.Time `json:"timestamp" db:"timestamp" validate:"required"`
	SpeedMS    float64   `json:"speed_m_s" db:"speed_m_s"`
	HeadingDeg float64   `json:"heading_deg" db:"heading_deg"`
	AccuracyM  *float64  `json:"accuracy_m,omitempty" db:"accuracy_m"`
	AltitudeM  *float64  `json:"altitude_m,omitempty" db:"altitude_m"`
	BatteryPct *float64  `json:"battery_pct,omitempty" db:"battery_pct" validate:"omitempty,min=0,max=100"`
	Source     *string   `json:"source,omitempty" db:"source"` // Changed to string
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}

// TrackingUpdate represents a GPS tracking update
type TrackingUpdate struct {
	TrackpointID int64      `json:"trackpoint_id" db:"trackpoint_id"`
	AgentID      uuid.UUID  `json:"agent_id" db:"agent_id" validate:"required"`
	Role         string     `json:"role" db:"role" validate:"required"` // Changed to string
	TripID       *uuid.UUID `json:"trip_id,omitempty" db:"trip_id"`
	Geom         Point      `json:"location" db:"geom" validate:"required"`
	Timestamp    time.Time  `json:"timestamp" db:"timestamp" validate:"required"`
	SpeedMS      *float64   `json:"speed_m_s,omitempty" db:"speed_m_s"`
	HeadingDeg   *float64   `json:"heading_deg,omitempty" db:"heading_deg"`
	AccuracyM    *float64   `json:"accuracy_m,omitempty" db:"accuracy_m"`
	AltitudeM    *float64   `json:"altitude_m,omitempty" db:"altitude_m"`
	BatteryPct   *float64   `json:"battery_pct,omitempty" db:"battery_pct" validate:"omitempty,min=0,max=100"`
	TemperatureC *float64   `json:"temperature_c,omitempty" db:"temperature_c"`
	HumidityPct  *float64   `json:"humidity_pct,omitempty" db:"humidity_pct" validate:"omitempty,min=0,max=100"`
	IsActive     bool       `json:"is_active" db:"is_active"`
	Source       *Source    `json:"source,omitempty" db:"source"`
}

// TrackingArchive represents archived tracking data for a specific date
type TrackingArchive struct {
	ID            uuid.UUID   `json:"id" db:"id"`
	AgentID       uuid.UUID   `json:"agent_id" db:"agent_id" validate:"required"`
	Role          Role        `json:"role" db:"role" validate:"required"`
	Date          time.Time   `json:"date" db:"date" validate:"required"`
	TrackPoints   TrackPoints `json:"track_points" db:"track_points"`
	StartTime     *time.Time  `json:"start_time,omitempty" db:"start_time"`
	EndTime       *time.Time  `json:"end_time,omitempty" db:"end_time"`
	TotalDistance *float64    `json:"total_distance,omitempty" db:"total_distance"`
	BatteryMin    *float64    `json:"battery_min,omitempty" db:"battery_min"`
	BatteryMax    *float64    `json:"battery_max,omitempty" db:"battery_max"`
	TripCount     int         `json:"trip_count" db:"trip_count"`
	CreatedAt     time.Time   `json:"created_at" db:"created_at"`
}

// Payment represents a payment for a trip
type Payment struct {
	PaymentID      uuid.UUID     `json:"payment_id" db:"payment_id"`
	TripID         uuid.UUID     `json:"trip_id" db:"trip_id" validate:"required"`
	Amount         float64       `json:"amount" db:"amount" validate:"required,min=0"`
	Status         PaymentStatus `json:"status" db:"status"`
	PaymentMode    *string       `json:"payment_mode,omitempty" db:"payment_mode"`
	TransactionRef *string       `json:"transaction_ref,omitempty" db:"transaction_ref"`
	PaidAt         *time.Time    `json:"paid_at,omitempty" db:"paid_at"`
}

// Rating represents a rating given by one user to another
type Rating struct {
	ID        uuid.UUID  `json:"id" db:"id"`
	TripID    uuid.UUID  `json:"trip_id" db:"trip_id" validate:"required"`
	FromUser  uuid.UUID  `json:"from_user" db:"from_user" validate:"required"`
	ToUser    uuid.UUID  `json:"to_user" db:"to_user" validate:"required"`
	Rating    int        `json:"rating" db:"rating" validate:"required,min=1,max=5"`
	Review    *string    `json:"review,omitempty" db:"review"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
}

// Request/Response DTOs

// CreateUserRequest represents a request to create a new user
type CreateUserRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone" validate:"required,min=10,max=15"`
	UserType string `json:"user_type" validate:"required"` // Changed to string to match DB
}

// UpdateUserRequest represents a request to update user information
type UpdateUserRequest struct {
	Name     *string   `json:"name,omitempty" validate:"omitempty,min=2,max=100"`
	Email    *string   `json:"email,omitempty" validate:"omitempty,email"`
	Phone    *string   `json:"phone,omitempty" validate:"omitempty,min=10,max=15"`
	IsActive *bool     `json:"is_active,omitempty"`
}

// CreateTripRequest represents a request to create a new trip
type CreateTripRequest struct {
	TripType       TripType     `json:"trip_type" validate:"required"`
	CustomerID     uuid.UUID    `json:"customer_id" validate:"required"`
	AgentID        uuid.UUID    `json:"agent_id" validate:"required"`
	PickupLocation Point        `json:"pickup_location" validate:"required"`
	DropLocation   Point        `json:"drop_location" validate:"required"`
	ItemDetails    *ItemDetails `json:"item_details,omitempty"`
	FareEstimate   *float64     `json:"fare_estimate,omitempty"`
	DeliveryFee    *float64     `json:"delivery_fee,omitempty"`
}

// LocationPingRequest represents a GPS location ping from an agent
type LocationPingRequest struct {
	AgentID    uuid.UUID `json:"agent_id" validate:"required"`
	Role       Role      `json:"role" validate:"required"`
	TripID     *uuid.UUID `json:"trip_id,omitempty"`
	Location   Point     `json:"location" validate:"required"`
	Timestamp  time.Time `json:"timestamp" validate:"required"`
	SpeedMS    *float64  `json:"speed_m_s,omitempty"`
	HeadingDeg *float64  `json:"heading_deg,omitempty"`
	AccuracyM  *float64  `json:"accuracy_m,omitempty"`
	AltitudeM  *float64  `json:"altitude_m,omitempty"`
	BatteryPct *float64  `json:"battery_pct,omitempty" validate:"omitempty,min=0,max=100"`
	Source     *Source   `json:"source,omitempty"`
}

// TripStatusUpdateRequest represents a request to update trip status
type TripStatusUpdateRequest struct {
	TripID    uuid.UUID  `json:"trip_id" validate:"required"`
	Status    TripStatus `json:"status" validate:"required"`
	Location  *Point     `json:"location,omitempty"`
	UpdatedBy *uuid.UUID `json:"updated_by,omitempty"`
	Note      *string    `json:"note,omitempty"`
}

// CreatePaymentRequest represents a request to create a payment
type CreatePaymentRequest struct {
	TripID         uuid.UUID `json:"trip_id" validate:"required"`
	Amount         float64   `json:"amount" validate:"required,min=0"`
	PaymentMode    *string   `json:"payment_mode,omitempty"`
	TransactionRef *string   `json:"transaction_ref,omitempty"`
}

// CreateRatingRequest represents a request to create a rating
type CreateRatingRequest struct {
	TripID   uuid.UUID `json:"trip_id" validate:"required"`
	FromUser uuid.UUID `json:"from_user" validate:"required"`
	ToUser   uuid.UUID `json:"to_user" validate:"required"`
	Rating   int       `json:"rating" validate:"required,min=1,max=5"`
	Review   *string   `json:"review,omitempty"`
}

// CreateDeliveryAssignmentRequest represents a request to create a delivery assignment
type CreateDeliveryAssignmentRequest struct {
	AgentID           uuid.UUID    `json:"agent_id" validate:"required"`
	CustomerID        uuid.UUID    `json:"customer_id" validate:"required"`
	TaskType          TaskType     `json:"task_type" validate:"required"`
	TaskDescription   string       `json:"task_description" validate:"required"`
	TargetLocation    Point        `json:"target_location" validate:"required"`
	GeofenceRadius    int          `json:"geofence_radius" validate:"required,min=50,max=2000"`
	ScheduledDate     *time.Time   `json:"scheduled_date,omitempty"`
	Notes             *string      `json:"notes,omitempty"`
	ItemDetails       *ItemDetails `json:"item_details,omitempty"`
	DeliveryFee       *float64     `json:"delivery_fee,omitempty"`
}

// UpdateDeliveryAssignmentRequest represents a request to update a delivery assignment
type UpdateDeliveryAssignmentRequest struct {
	TaskType          *TaskType    `json:"task_type,omitempty"`
	TaskDescription   *string      `json:"task_description,omitempty"`
	TargetLocation    *Point       `json:"target_location,omitempty"`
	GeofenceRadius    *int         `json:"geofence_radius,omitempty" validate:"omitempty,min=50,max=2000"`
	Status            *TripStatus  `json:"status,omitempty"`
	ScheduledDate     *time.Time   `json:"scheduled_date,omitempty"`
	Notes             *string      `json:"notes,omitempty"`
	ItemDetails       *ItemDetails `json:"item_details,omitempty"`
	DeliveryFee       *float64     `json:"delivery_fee,omitempty"`
}

// CustomMarkerSetting represents a user's custom marker configuration
type CustomMarkerSetting struct {
	ID           uuid.UUID  `json:"id" db:"id"`
	UserID       uuid.UUID  `json:"user_id" db:"user_id"`
	MarkerType   string     `json:"marker_type" db:"marker_type"`
	IconText     *string    `json:"icon_text" db:"icon_text"`
	IconImageURL *string    `json:"icon_image_url" db:"icon_image_url"`
	Color        string     `json:"color" db:"color"`
	Size         int        `json:"size" db:"size"`
	BorderColor  *string    `json:"border_color" db:"border_color"`
	BorderWidth  *int       `json:"border_width" db:"border_width"`
	Enabled      bool       `json:"enabled" db:"enabled"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" db:"updated_at"`
}

// TemperatureReading represents a temperature sensor reading
type TemperatureReading struct {
	ID             uuid.UUID `json:"id" db:"id"`
	TripID         uuid.UUID `json:"trip_id" db:"trip_id" validate:"required"`
	AgentID        uuid.UUID `json:"agent_id" db:"agent_id" validate:"required"`
	SensorID       string    `json:"sensor_id,omitempty" db:"sensor_id"`
	TemperatureC   float64   `json:"temperature_c" db:"temperature_c" validate:"required"`
	HumidityPct    *float64  `json:"humidity_pct,omitempty" db:"humidity_pct" validate:"omitempty,min=0,max=100"`
	BatteryLevel   *float64  `json:"battery_level,omitempty" db:"battery_level" validate:"omitempty,min=0,max=100"`
	SignalStrength *int      `json:"signal_strength,omitempty" db:"signal_strength"`
	Location       Point     `json:"location" db:"location"`
	RecordedAt     time.Time `json:"recorded_at" db:"recorded_at" validate:"required"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
}

// TemperatureAlert represents a temperature breach alert
type TemperatureAlert struct {
	ID             uuid.UUID  `json:"id" db:"id"`
	TripID         uuid.UUID  `json:"trip_id" db:"trip_id" validate:"required"`
	AgentID        uuid.UUID  `json:"agent_id" db:"agent_id" validate:"required"`
	AlertType      string     `json:"alert_type" db:"alert_type" validate:"required"`
	TemperatureC   float64    `json:"temperature_c" db:"temperature_c" validate:"required"`
	HumidityPct    *float64   `json:"humidity_pct,omitempty" db:"humidity_pct"`
	ThresholdMin   *float64   `json:"threshold_min,omitempty" db:"threshold_min"`
	ThresholdMax   *float64   `json:"threshold_max,omitempty" db:"threshold_max"`
	Severity       string     `json:"severity" db:"severity" validate:"required"`
	Location       *Point     `json:"location,omitempty" db:"location"`
	ResolutionNote *string    `json:"resolution_note,omitempty" db:"resolution_note"`
	ResolvedAt     *time.Time `json:"resolved_at,omitempty" db:"resolved_at"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`

	// Additional fields for API responses (not stored in DB)
	AgentName      string     `json:"agent_name,omitempty" db:"-"`
}

// Geofence represents a geographic boundary for compliance monitoring
type Geofence struct {
	ID            uuid.UUID              `json:"id" db:"id"`
	Name          string                 `json:"name" db:"name" validate:"required"`
	Description   string                 `json:"description,omitempty" db:"description"`
	GeofenceType  string                 `json:"geofence_type" db:"geofence_type" validate:"required"`
	Boundary      [][]float64            `json:"boundary" db:"boundary" validate:"required"` // Array of [lng, lat] coordinates
	IsActive      bool                   `json:"is_active" db:"is_active"`
	SpeedLimitKmh *int                   `json:"speed_limit_kmh,omitempty" db:"speed_limit_kmh"`
	AllowedHours  map[string]interface{} `json:"allowed_hours,omitempty" db:"allowed_hours"`
	Restrictions  map[string]interface{} `json:"restrictions,omitempty" db:"restrictions"`
	CreatedBy     *uuid.UUID             `json:"created_by,omitempty" db:"created_by"`
	CreatedAt     time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at" db:"updated_at"`
}

// PlannedRoute represents an expected route for compliance checking
type PlannedRoute struct {
	ID                       uuid.UUID   `json:"id" db:"id"`
	TripID                   uuid.UUID   `json:"trip_id" db:"trip_id" validate:"required"`
	RoutePoints              [][]float64 `json:"route_points" db:"route_points" validate:"required"` // Array of [lng, lat] coordinates
	Waypoints                []Waypoint  `json:"waypoints,omitempty" db:"waypoints"`
	EstimatedDurationMinutes int         `json:"estimated_duration_minutes" db:"estimated_duration_minutes"`
	EstimatedDistanceKm      float64     `json:"estimated_distance_km" db:"estimated_distance_km"`
	MaxDeviationMeters       int         `json:"max_deviation_meters" db:"max_deviation_meters"`
	SpeedLimits              map[string]interface{} `json:"speed_limits,omitempty" db:"speed_limits"`
	CreatedAt                time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt                time.Time   `json:"updated_at" db:"updated_at"`
}

// Waypoint represents a waypoint in a planned route
type Waypoint struct {
	Latitude  float64                `json:"latitude" validate:"required,min=-90,max=90"`
	Longitude float64                `json:"longitude" validate:"required,min=-180,max=180"`
	Name      string                 `json:"name,omitempty"`
	Type      string                 `json:"type,omitempty"` // "pickup", "delivery", "waypoint"
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// RouteComplianceViolation represents a route compliance violation
type RouteComplianceViolation struct {
	ID                  uuid.UUID  `json:"id" db:"id"`
	TripID              uuid.UUID  `json:"trip_id" db:"trip_id" validate:"required"`
	AgentID             uuid.UUID  `json:"agent_id" db:"agent_id" validate:"required"`
	EventType           string     `json:"event_type" db:"event_type" validate:"required"`
	GeofenceID          *uuid.UUID `json:"geofence_id,omitempty" db:"geofence_id"`
	Location            Point      `json:"location" db:"location"`
	SpeedKmh            *float64   `json:"speed_kmh,omitempty" db:"speed_kmh"`
	ExpectedLocation    *Point     `json:"expected_location,omitempty" db:"expected_location"`
	DeviationDistanceM  *float64   `json:"deviation_distance_m,omitempty" db:"deviation_distance_m"`
	Severity            string     `json:"severity" db:"severity" validate:"required"`
	Description         string     `json:"description,omitempty" db:"description"`
	Resolved            bool       `json:"resolved" db:"resolved"`
	ResolvedBy          *uuid.UUID `json:"resolved_by,omitempty" db:"resolved_by"`
	ResolvedAt          *time.Time `json:"resolved_at,omitempty" db:"resolved_at"`
	ResolutionNote      *string    `json:"resolution_note,omitempty" db:"resolution_note"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`

	// Additional fields for API responses (not stored in DB)
	AgentName           string     `json:"agent_name,omitempty" db:"-"`
	GeofenceName        string     `json:"geofence_name,omitempty" db:"-"`
}

// Product represents a product in the inventory system
type Product struct {
	ID                  uuid.UUID              `json:"id" db:"id"`
	SKU                 string                 `json:"sku" db:"sku" validate:"required"`
	Name                string                 `json:"name" db:"name" validate:"required"`
	Description         string                 `json:"description,omitempty" db:"description"`
	Category            string                 `json:"category,omitempty" db:"category"`
	UnitOfMeasure       string                 `json:"unit_of_measure" db:"unit_of_measure"`
	UnitPrice           float64                `json:"unit_price" db:"unit_price"`
	WeightKg            float64                `json:"weight_kg" db:"weight_kg"`
	Dimensions          map[string]interface{} `json:"dimensions,omitempty" db:"dimensions"`
	SupplierInfo        map[string]interface{} `json:"supplier_info,omitempty" db:"supplier_info"`
	ShelfLifeDays       *int                   `json:"shelf_life_days,omitempty" db:"shelf_life_days"`
	StorageRequirements map[string]interface{} `json:"storage_requirements,omitempty" db:"storage_requirements"`
	IsActive            bool                   `json:"is_active" db:"is_active"`
	CreatedAt           time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time              `json:"updated_at" db:"updated_at"`
}

// Warehouse represents a warehouse location
type Warehouse struct {
	ID                  uuid.UUID              `json:"id" db:"id"`
	Name                string                 `json:"name" db:"name" validate:"required"`
	Code                string                 `json:"code" db:"code" validate:"required"`
	Address             string                 `json:"address,omitempty" db:"address"`
	Location            *Point                 `json:"location,omitempty" db:"location"`
	ManagerID           *uuid.UUID             `json:"manager_id,omitempty" db:"manager_id"`
	CapacityCubicMeters float64                `json:"capacity_cubic_meters" db:"capacity_cubic_meters"`
	IsActive            bool                   `json:"is_active" db:"is_active"`
	OperatingHours      map[string]interface{} `json:"operating_hours,omitempty" db:"operating_hours"`
	ContactInfo         map[string]interface{} `json:"contact_info,omitempty" db:"contact_info"`
	CreatedAt           time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time              `json:"updated_at" db:"updated_at"`
}

// Inventory represents inventory stock levels
type Inventory struct {
	ID                  uuid.UUID  `json:"id" db:"id"`
	WarehouseID         uuid.UUID  `json:"warehouse_id" db:"warehouse_id" validate:"required"`
	ProductID           uuid.UUID  `json:"product_id" db:"product_id" validate:"required"`
	CurrentStock        int        `json:"current_stock" db:"current_stock"`
	ReservedStock       int        `json:"reserved_stock" db:"reserved_stock"`
	AvailableStock      int        `json:"available_stock" db:"available_stock"` // Computed field
	ReorderPoint        int        `json:"reorder_point" db:"reorder_point"`
	MaxStockLevel       *int       `json:"max_stock_level,omitempty" db:"max_stock_level"`
	UnitCost            *float64   `json:"unit_cost,omitempty" db:"unit_cost"`
	LastRestockedAt     *time.Time `json:"last_restocked_at,omitempty" db:"last_restocked_at"`
	ExpiryDate          *time.Time `json:"expiry_date,omitempty" db:"expiry_date"`
	BatchNumber         string     `json:"batch_number,omitempty" db:"batch_number"`
	Status              string     `json:"status" db:"status"`
	LocationInWarehouse string     `json:"location_in_warehouse,omitempty" db:"location_in_warehouse"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at" db:"updated_at"`
}

// InventoryMovement represents a stock movement record
type InventoryMovement struct {
	ID            uuid.UUID  `json:"id" db:"id"`
	InventoryID   uuid.UUID  `json:"inventory_id" db:"inventory_id" validate:"required"`
	MovementType  string     `json:"movement_type" db:"movement_type" validate:"required"`
	Quantity      int        `json:"quantity" db:"quantity" validate:"required"`
	ReferenceType string     `json:"reference_type,omitempty" db:"reference_type"`
	ReferenceID   *uuid.UUID `json:"reference_id,omitempty" db:"reference_id"`
	Reason        string     `json:"reason,omitempty" db:"reason"`
	PerformedBy   *uuid.UUID `json:"performed_by,omitempty" db:"performed_by"`
	CostPerUnit   float64    `json:"cost_per_unit" db:"cost_per_unit"`
	TotalCost     float64    `json:"total_cost" db:"total_cost"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
}

// InventoryAlert represents an inventory alert
type InventoryAlert struct {
	ID             uuid.UUID  `json:"id" db:"id"`
	InventoryID    uuid.UUID  `json:"inventory_id" db:"inventory_id" validate:"required"`
	AlertType      string     `json:"alert_type" db:"alert_type" validate:"required"`
	Severity       string     `json:"severity" db:"severity" validate:"required"`
	CurrentStock   *int       `json:"current_stock,omitempty" db:"current_stock"`
	ThresholdValue *int       `json:"threshold_value,omitempty" db:"threshold_value"`
	Message        string     `json:"message,omitempty" db:"message"`
	Resolved       bool       `json:"resolved" db:"resolved"`
	ResolvedBy     *uuid.UUID `json:"resolved_by,omitempty" db:"resolved_by"`
	ResolvedAt     *time.Time `json:"resolved_at,omitempty" db:"resolved_at"`
	ResolutionNote *string    `json:"resolution_note,omitempty" db:"resolution_note"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`

	// Additional fields for API responses (not stored in DB)
	ProductName    string     `json:"product_name,omitempty" db:"-"`
	SKU            string     `json:"sku,omitempty" db:"-"`
	WarehouseName  string     `json:"warehouse_name,omitempty" db:"-"`
}

// InventoryUpdate represents fields that can be updated in inventory
type InventoryUpdate struct {
	CurrentStock   *int       `json:"current_stock,omitempty"`
	ReservedStock  *int       `json:"reserved_stock,omitempty"`
	ReorderPoint   *int       `json:"reorder_point,omitempty"`
	MaxStockLevel  *int       `json:"max_stock_level,omitempty"`
	UnitCost       *float64   `json:"unit_cost,omitempty"`
	ExpiryDate     *time.Time `json:"expiry_date,omitempty"`
	BatchNumber    *string    `json:"batch_number,omitempty"`
}

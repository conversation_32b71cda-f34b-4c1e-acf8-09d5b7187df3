package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Enum types
type UserType string
type Role string
type Source string
type TripType string
type TripStatus string
type PaymentStatus string
type TaskType string
type GeofenceStatus string

const (
	// UserType enum values
	UserTypeCustomer UserType = "customer"
	UserTypeAgent    UserType = "agent"
	UserTypeAdmin    UserType = "admin"

	// String constants for database compatibility
	UserTypeCustomerStr = "customer"
	UserTypeAgentStr    = "agent"
	UserTypeAdminStr    = "admin"

	// Role enum values
	RoleDriver   Role = "driver"
	RoleDelivery Role = "delivery"
	RoleHybrid   Role = "hybrid"

	// Source enum values
	SourceMobileSDK Source = "mobile_sdk"
	SourceGPSDevice Source = "gps_device"
	SourceManual    Source = "manual"

	// TripType enum values
	TripTypeRide     TripType = "ride"
	TripTypeDelivery TripType = "delivery"

	// TaskType enum values for delivery assignments
	TaskTypePickOnly    TaskType = "pick_only"
	TaskTypeDropOnly    TaskType = "drop_only"
	TaskTypeStoreVisit  TaskType = "store_visit"
	TaskTypeFullDelivery TaskType = "full_delivery"
	TaskTypeCustom      TaskType = "custom"

	// GeofenceStatus enum values
	GeofenceStatusActive   GeofenceStatus = "active"
	GeofenceStatusInactive GeofenceStatus = "inactive"
	GeofenceStatusExpired  GeofenceStatus = "expired"

	// TripStatus enum values (matching delivery_status enum in database)
	TripStatusRequested      TripStatus = "requested"
	TripStatusAccepted       TripStatus = "accepted"
	TripStatusPickedUp       TripStatus = "picked_up"
	TripStatusInTransit      TripStatus = "in_transit"
	TripStatusOutForDelivery TripStatus = "out_for_delivery"
	TripStatusDelivered      TripStatus = "delivered"
	TripStatusFailed         TripStatus = "failed"
	TripStatusRescheduled    TripStatus = "rescheduled"
	TripStatusCancelled      TripStatus = "cancelled"
	TripStatusReturned       TripStatus = "returned"

	// PaymentStatus enum values
	PaymentStatusPending PaymentStatus = "pending"
	PaymentStatusPaid    PaymentStatus = "paid"
	PaymentStatusFailed  PaymentStatus = "failed"
)

// Point represents a geographic point (longitude, latitude)
type Point struct {
	Longitude float64 `json:"longitude" validate:"required,min=-180,max=180"`
	Latitude  float64 `json:"latitude" validate:"required,min=-90,max=90"`
}

// Scan implements the sql.Scanner interface for Point
func (p *Point) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		// Parse PostGIS POINT format: "POINT(longitude latitude)"
		var lon, lat float64
		_, err := fmt.Sscanf(string(v), "POINT(%f %f)", &lon, &lat)
		if err != nil {
			return fmt.Errorf("failed to parse point: %w", err)
		}
		p.Longitude = lon
		p.Latitude = lat
		return nil
	case string:
		var lon, lat float64
		_, err := fmt.Sscanf(v, "POINT(%f %f)", &lon, &lat)
		if err != nil {
			return fmt.Errorf("failed to parse point: %w", err)
		}
		p.Longitude = lon
		p.Latitude = lat
		return nil
	default:
		return fmt.Errorf("cannot scan %T into Point", value)
	}
}

// Value implements the driver.Valuer interface for Point
func (p Point) Value() (driver.Value, error) {
	return fmt.Sprintf("POINT(%f %f)", p.Longitude, p.Latitude), nil
}

// String returns string representation of Point
func (p Point) String() string {
	return fmt.Sprintf("POINT(%f %f)", p.Longitude, p.Latitude)
}

// TrackPoint represents a single GPS tracking point
type TrackPoint struct {
	Longitude  float64   `json:"longitude"`
	Latitude   float64   `json:"latitude"`
	Timestamp  time.Time `json:"timestamp"`
	Speed      *float64  `json:"speed,omitempty"`
	Heading    *float64  `json:"heading,omitempty"`
	Accuracy   *float64  `json:"accuracy,omitempty"`
	Altitude   *float64  `json:"altitude,omitempty"`
	BatteryPct *float64  `json:"battery_pct,omitempty"`
}

// TrackPoints represents a collection of track points
type TrackPoints []TrackPoint

// Scan implements the sql.Scanner interface for TrackPoints
func (tp *TrackPoints) Scan(value interface{}) error {
	if value == nil {
		*tp = TrackPoints{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, tp)
	case string:
		return json.Unmarshal([]byte(v), tp)
	default:
		return fmt.Errorf("cannot scan %T into TrackPoints", value)
	}
}

// Value implements the driver.Valuer interface for TrackPoints
func (tp TrackPoints) Value() (driver.Value, error) {
	return json.Marshal(tp)
}

// ItemDetails represents item details for delivery trips
type ItemDetails map[string]interface{}

// Scan implements the sql.Scanner interface for ItemDetails
func (id *ItemDetails) Scan(value interface{}) error {
	if value == nil {
		*id = ItemDetails{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, id)
	case string:
		return json.Unmarshal([]byte(v), id)
	default:
		return fmt.Errorf("cannot scan %T into ItemDetails", value)
	}
}

// Value implements the driver.Valuer interface for ItemDetails
func (id ItemDetails) Value() (driver.Value, error) {
	return json.Marshal(id)
}

// NullableUUID represents a UUID that can be null
type NullableUUID struct {
	UUID  uuid.UUID
	Valid bool
}

// Scan implements the sql.Scanner interface for NullableUUID
func (nu *NullableUUID) Scan(value interface{}) error {
	if value == nil {
		nu.Valid = false
		return nil
	}
	
	switch v := value.(type) {
	case string:
		u, err := uuid.Parse(v)
		if err != nil {
			return err
		}
		nu.UUID = u
		nu.Valid = true
		return nil
	case []byte:
		u, err := uuid.Parse(string(v))
		if err != nil {
			return err
		}
		nu.UUID = u
		nu.Valid = true
		return nil
	default:
		return fmt.Errorf("cannot scan %T into NullableUUID", value)
	}
}

// Value implements the driver.Valuer interface for NullableUUID
func (nu NullableUUID) Value() (driver.Value, error) {
	if !nu.Valid {
		return nil, nil
	}
	return nu.UUID.String(), nil
}

// MarshalJSON implements json.Marshaler for NullableUUID
func (nu NullableUUID) MarshalJSON() ([]byte, error) {
	if !nu.Valid {
		return []byte("null"), nil
	}
	return json.Marshal(nu.UUID.String())
}

// UnmarshalJSON implements json.Unmarshaler for NullableUUID
func (nu *NullableUUID) UnmarshalJSON(data []byte) error {
	var s *string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	
	if s == nil {
		nu.Valid = false
		return nil
	}
	
	u, err := uuid.Parse(*s)
	if err != nil {
		return err
	}
	
	nu.UUID = u
	nu.Valid = true
	return nil
}

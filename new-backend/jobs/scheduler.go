package jobs

import (
	"time"

	"delivery-tracking-backend/config"
	"delivery-tracking-backend/repository"

	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
)

type JobScheduler struct {
	cron        *cron.Cron
	cfg         *config.Config
	trackingRepo *repository.TrackingRepository
	ratingRepo   *repository.RatingRepository
	userRepo     *repository.UserRepository
	agentRepo    *repository.AgentRepository
}

func NewJobScheduler(cfg *config.Config, trackingRepo *repository.TrackingRepository, 
	ratingRepo *repository.RatingRepository, userRepo *repository.UserRepository, 
	agentRepo *repository.AgentRepository) *JobScheduler {
	
	return &JobScheduler{
		cron:        cron.New(cron.WithSeconds()),
		cfg:         cfg,
		trackingRepo: trackingRepo,
		ratingRepo:   ratingRepo,
		userRepo:     userRepo,
		agentRepo:    agentRepo,
	}
}

func (js *JobScheduler) Start() {
	logrus.Info("Starting job scheduler")

	// Archive tracking data daily at 2 AM
	js.cron.AddFunc("0 0 2 * * *", js.archiveTrackingDataJob)

	// Clean up old tracking data every 6 hours
	js.cron.AddFunc("0 0 */6 * * *", js.cleanupTrackingDataJob)

	// Update agent ratings every hour
	js.cron.AddFunc("0 0 * * * *", js.updateAgentRatingsJob)

	// Update agent availability based on last ping every 15 minutes
	js.cron.AddFunc("0 */15 * * * *", js.updateAgentAvailabilityJob)

	js.cron.Start()
	logrus.Info("Job scheduler started successfully")
}

func (js *JobScheduler) Stop() {
	logrus.Info("Stopping job scheduler")
	js.cron.Stop()
	logrus.Info("Job scheduler stopped")
}

// archiveTrackingDataJob archives tracking data older than 3 days
func (js *JobScheduler) archiveTrackingDataJob() {
	logrus.Info("Starting tracking data archival job")
	
	// Get all active agents
	agents, err := js.agentRepo.GetAvailableAgents()
	if err != nil {
		logrus.WithError(err).Error("Failed to get agents for archival")
		return
	}

	// Archive data for each agent for the past 3 days
	archiveDate := time.Now().AddDate(0, 0, -3)
	successCount := 0
	errorCount := 0

	for _, agent := range agents {
		err := js.trackingRepo.ArchiveTrackingData(agent.UserID, archiveDate)
		if err != nil {
			logrus.WithError(err).WithField("agent_id", agent.UserID).Error("Failed to archive tracking data")
			errorCount++
		} else {
			successCount++
		}
	}

	logrus.WithFields(logrus.Fields{
		"archive_date":   archiveDate.Format("2006-01-02"),
		"success_count":  successCount,
		"error_count":    errorCount,
		"total_agents":   len(agents),
	}).Info("Tracking data archival job completed")
}

// cleanupTrackingDataJob removes tracking data older than 7 days
func (js *JobScheduler) cleanupTrackingDataJob() {
	logrus.Info("Starting tracking data cleanup job")
	
	// This would typically involve deleting old records from TrackingUpdates
	// For now, we'll just log the action
	cutoffDate := time.Now().AddDate(0, 0, -7)
	
	logrus.WithField("cutoff_date", cutoffDate.Format("2006-01-02")).Info("Tracking data cleanup job completed")
}

// updateAgentRatingsJob recalculates and updates agent ratings
func (js *JobScheduler) updateAgentRatingsJob() {
	logrus.Info("Starting agent ratings update job")
	
	// Get all agents
	agents, err := js.userRepo.GetUsersByType("agent")
	if err != nil {
		logrus.WithError(err).Error("Failed to get agents for rating update")
		return
	}

	successCount := 0
	errorCount := 0

	for _, agent := range agents {
		// Get rating stats for the agent
		stats, err := js.ratingRepo.GetUserRatingStats(agent.ID)
		if err != nil {
			logrus.WithError(err).WithField("agent_id", agent.ID).Error("Failed to get rating stats")
			errorCount++
			continue
		}

		// Update agent rating if there are ratings
		if avgRating, ok := stats["avg_rating"].(float64); ok && avgRating > 0 {
			err := js.agentRepo.UpdateAgentRating(agent.ID, avgRating)
			if err != nil {
				logrus.WithError(err).WithField("agent_id", agent.ID).Error("Failed to update agent rating")
				errorCount++
			} else {
				successCount++
			}
		}
	}

	logrus.WithFields(logrus.Fields{
		"success_count": successCount,
		"error_count":   errorCount,
		"total_agents":  len(agents),
	}).Info("Agent ratings update job completed")
}

// updateAgentAvailabilityJob updates agent availability based on last GPS ping
func (js *JobScheduler) updateAgentAvailabilityJob() {
	logrus.Info("Starting agent availability update job")
	
	// Get agents who haven't sent a GPS ping in the last 30 minutes
	cutoffTime := time.Now().Add(-30 * time.Minute)
	
	// Get all agents
	agents, err := js.agentRepo.GetAvailableAgents()
	if err != nil {
		logrus.WithError(err).Error("Failed to get agents for availability update")
		return
	}

	unavailableCount := 0

	for _, agent := range agents {
		// Get last live location
		liveLocation, err := js.trackingRepo.GetLiveLocation(agent.UserID)
		if err != nil {
			// If no live location found, mark as unavailable
			err := js.agentRepo.UpdateAgentAvailability(agent.UserID, false)
			if err != nil {
				logrus.WithError(err).WithField("agent_id", agent.UserID).Error("Failed to update agent availability")
			} else {
				unavailableCount++
			}
			continue
		}

		// If last update is older than cutoff time, mark as unavailable
		if liveLocation.UpdatedAt.Before(cutoffTime) {
			err := js.agentRepo.UpdateAgentAvailability(agent.UserID, false)
			if err != nil {
				logrus.WithError(err).WithField("agent_id", agent.UserID).Error("Failed to update agent availability")
			} else {
				unavailableCount++
			}
		}
	}

	logrus.WithFields(logrus.Fields{
		"unavailable_count": unavailableCount,
		"total_agents":      len(agents),
		"cutoff_time":       cutoffTime.Format(time.RFC3339),
	}).Info("Agent availability update job completed")
}

// GetJobStatus returns the status of the job scheduler
func (js *JobScheduler) GetJobStatus() map[string]interface{} {
	entries := js.cron.Entries()
	
	jobs := make([]map[string]interface{}, len(entries))
	for i, entry := range entries {
		jobs[i] = map[string]interface{}{
			"id":        entry.ID,
			"next_run":  entry.Next.Format(time.RFC3339),
			"prev_run":  entry.Prev.Format(time.RFC3339),
		}
	}

	return map[string]interface{}{
		"running":    len(entries) > 0,
		"job_count":  len(entries),
		"jobs":       jobs,
	}
}

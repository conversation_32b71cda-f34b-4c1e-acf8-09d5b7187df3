package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

type Config struct {
	Database DatabaseConfig
	Server   ServerConfig
	JWT      JWTConfig
	CORS     CORSConfig
	RateLimit RateLimitConfig
	BackgroundJobs BackgroundJobsConfig
	Logging  LoggingConfig
	WebSocket WebSocketConfig
}

type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Name     string
	SSLMode  string
}

type ServerConfig struct {
	Port    int
	Host    string
	GinMode string
}

type JWTConfig struct {
	Secret      string
	ExpiryHours int
}

type CORSConfig struct {
	AllowedOrigins []string
	AllowedMethods []string
	AllowedHeaders []string
}

type RateLimitConfig struct {
	RequestsPerMinute int
}

type BackgroundJobsConfig struct {
	ArchiveIntervalHours      int
	CleanupIntervalHours      int
	RatingUpdateIntervalHours int
}

type LoggingConfig struct {
	Level  string
	Format string
}

type WebSocketConfig struct {
	ReadBufferSize     int
	WriteBufferSize    int
	PingPeriodSeconds  int
}

func Load() (*Config, error) {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		logrus.Warn("No .env file found, using environment variables")
	}

	config := &Config{
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 5432),
			User:     getEnv("DB_USER", "hgcgh"),
			Password: getEnv("DB_PASSWORD", ""),
			Name:     getEnv("DB_NAME", "delivery_tracking"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Server: ServerConfig{
			Port:    getEnvAsInt("SERVER_PORT", 8080),
			Host:    getEnv("SERVER_HOST", "localhost"),
			GinMode: getEnv("GIN_MODE", "debug"),
		},
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "your-super-secret-jwt-key-change-this-in-production"),
			ExpiryHours: getEnvAsInt("JWT_EXPIRY_HOURS", 24),
		},
		CORS: CORSConfig{
			AllowedOrigins: getEnvAsSlice("CORS_ALLOWED_ORIGINS", []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:8100"}),
			AllowedMethods: getEnvAsSlice("CORS_ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
			AllowedHeaders: getEnvAsSlice("CORS_ALLOWED_HEADERS", []string{"Content-Type", "Authorization", "X-Requested-With", "X-User-Timezone", "X-User-Timezone-Offset"}),
		},
		RateLimit: RateLimitConfig{
			RequestsPerMinute: getEnvAsInt("RATE_LIMIT_REQUESTS_PER_MINUTE", 300),
		},
		BackgroundJobs: BackgroundJobsConfig{
			ArchiveIntervalHours:      getEnvAsInt("ARCHIVE_INTERVAL_HOURS", 24),
			CleanupIntervalHours:      getEnvAsInt("CLEANUP_INTERVAL_HOURS", 6),
			RatingUpdateIntervalHours: getEnvAsInt("RATING_UPDATE_INTERVAL_HOURS", 1),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
		WebSocket: WebSocketConfig{
			ReadBufferSize:    getEnvAsInt("WS_READ_BUFFER_SIZE", 1024),
			WriteBufferSize:   getEnvAsInt("WS_WRITE_BUFFER_SIZE", 1024),
			PingPeriodSeconds: getEnvAsInt("WS_PING_PERIOD_SECONDS", 54),
		},
	}

	return config, nil
}

func (c *Config) GetDatabaseURL() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s",
		c.Database.User,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.Name,
		c.Database.SSLMode,
	)
}

func (c *Config) GetServerAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

func (c *Config) GetJWTExpiry() time.Duration {
	return time.Duration(c.JWT.ExpiryHours) * time.Hour
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		// Simple split by comma - you might want to use a more sophisticated parser
		result := make([]string, 0)
		for _, v := range []string{value} {
			if v != "" {
				result = append(result, v)
			}
		}
		if len(result) > 0 {
			return result
		}
	}
	return defaultValue
}

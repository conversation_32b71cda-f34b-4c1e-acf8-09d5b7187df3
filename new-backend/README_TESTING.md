# 🚀 Live Location Update Testing Scripts

## Overview
These scripts help you manually test the database trigger functionality that automatically updates `LiveLocations` table when new data is inserted into `TrackingUpdates` table.

## Files Created

### 1. `scripts/simple_tester.go` - Simple Working Tester ⭐ **RECOMMENDED & TESTED**
**Easy-to-use interactive script that definitely works**

**Usage:**
```bash
cd /Users/<USER>/Downloads/delivery-tracking/new-backend
go run scripts/simple_tester.go
```

**Options:**
- `1` - Show current locations
- `2` - Move Mike Driver to Mumbai
- `3` - Move Priya Sharma to Bangalore
- `4` - Move all agents to different cities (Delhi, Mumbai, Bangalore, Chennai, Kolkata)
- `q` - Quit

### 2. `scripts/interactive_tester.go` - Advanced Interactive Tester
**Interactive menu-driven script with 5 rounds of location data across different Indian states**

**Usage:**
```bash
cd /Users/<USER>/Downloads/delivery-tracking/new-backend
go run scripts/interactive_tester.go
```

**Menu Options:**
- `1` - Add Round 1 Data (Delhi, Mumbai, Bangalore, Chennai, Kolkata)
- `2` - Add Round 2 Data (Haryana, Maharashtra, Karnataka, Tamil Nadu, West Bengal)
- `3` - Add Round 3 Data (Rajasthan, Maharashtra, Karnataka, Tamil Nadu, West Bengal)
- `4` - Add Round 4 Data (Gujarat, Maharashtra, Karnataka, Kerala, West Bengal)
- `5` - Add Round 5 Data (Punjab, Maharashtra, Karnataka, Kerala, Jharkhand)
- `s` - Show current LiveLocations
- `c` - Check API response
- `q` - Quit

### 2. `test_api_only.sh` - API Testing Script
**Shell script that tests API endpoints without requiring psql**

**Usage:**
```bash
cd /Users/<USER>/Downloads/delivery-tracking/new-backend
./test_api_only.sh
```

### 3. `demo_script.sh` - Quick Demo
**Simple one-time demo that shows the trigger working**

**Usage:**
```bash
cd /Users/<USER>/Downloads/delivery-tracking/new-backend
./demo_script.sh
```

## What Each Round Contains (Dramatic Location Changes!)

### Round 1 - Major Indian Cities
- **Mike Driver** → **New Delhi, Delhi** (28.6139, 77.2090) - 85% battery
- **Priya Sharma** → **Mumbai, Maharashtra** (19.0760, 72.8777) - 88% battery
- **Rajesh Kumar** → **Bangalore, Karnataka** (12.9716, 77.5946) - 82% battery
- **Anita Patel** → **Chennai, Tamil Nadu** (13.0827, 80.2707) - 90% battery
- **Vikram Yadav** → **Kolkata, West Bengal** (22.5726, 88.3639) - 75% battery

### Round 2 - Secondary Cities
- **Mike Driver** → **Gurgaon, Haryana** (28.7041, 77.1025) - 80% battery
- **Priya Sharma** → **Pune, Maharashtra** (18.5204, 73.8567) - 83% battery
- **Rajesh Kumar** → **Bangalore East, Karnataka** (13.0358, 77.5970) - 77% battery
- **Anita Patel** → **Chennai South, Tamil Nadu** (12.9141, 80.2316) - 85% battery
- **Vikram Yadav** → **Howrah, West Bengal** (22.6708, 88.0977) - 70% battery

### Round 3 - Different States
- **Mike Driver** → **Jaipur, Rajasthan** (26.9124, 75.7873) - 75% battery
- **Priya Sharma** → **Nagpur, Maharashtra** (21.1458, 79.0882) - 78% battery
- **Rajesh Kumar** → **Hubli, Karnataka** (15.3173, 75.7139) - 72% battery
- **Anita Patel** → **Coimbatore, Tamil Nadu** (11.0168, 76.9558) - 80% battery
- **Vikram Yadav** → **Asansol, West Bengal** (23.2599, 87.8526) - 65% battery

### Round 4 - Western & Southern India
- **Mike Driver** → **Ahmedabad, Gujarat** (23.0225, 72.5714) - 70% battery
- **Priya Sharma** → **Navi Mumbai, Maharashtra** (18.9388, 72.8354) - 73% battery
- **Rajesh Kumar** → **Mysore, Karnataka** (12.2958, 76.6394) - 67% battery
- **Anita Patel** → **Kochi, Kerala** (9.9312, 76.2673) - 75% battery
- **Vikram Yadav** → **Durgapur, West Bengal** (22.9868, 87.7499) - 60% battery

### Round 5 - Northern & Eastern India
- **Mike Driver** → **Chandigarh, Punjab** (30.7333, 76.7794) - 65% battery
- **Priya Sharma** → **Aurangabad, Maharashtra** (19.8762, 75.3433) - 68% battery
- **Rajesh Kumar** → **Shimoga, Karnataka** (14.5203, 75.7224) - 62% battery
- **Anita Patel** → **Trivandrum, Kerala** (8.5241, 76.9366) - 70% battery
- **Vikram Yadav** → **Ranchi, Jharkhand** (23.3441, 85.2955) - 55% battery

## How to Test

1. **Start the backend:**
   ```bash
   cd /Users/<USER>/Downloads/delivery-tracking/new-backend
   go run main.go
   ```

2. **Run the simple working script:**
   ```bash
   go run scripts/simple_tester.go
   ```

3. **Test sequence:**
   - Type `s` to see current locations
   - Type `1` to add Round 1 data
   - Notice how LiveLocations gets updated (not new rows added)
   - Type `c` to check API response
   - Type `2` for Round 2, and so on...

## What You'll See

**BEFORE adding data:**
```
     name     | latitude | longitude | battery |  updated_time   
--------------+----------+-----------+---------+-----------------
 Mike Driver  |  28.6509 |   77.2297 |      44 | 23:07:32.752499
```

**AFTER adding data:**
```
     name     | latitude | longitude | battery |  updated_time   
--------------+----------+-----------+---------+-----------------
 Mike Driver  |  28.6139 |   77.2090 |      85 | 23:28:15.123456
```

**Key Points:**
- ✅ Location updated automatically
- ✅ Battery level updated
- ✅ Timestamp is newer
- ✅ Still only 5 rows total (no new rows added)
- ✅ API returns updated data immediately

## Verification Steps

1. **Database Level:** Check LiveLocations table directly
2. **API Level:** Test `/api/tracking/active-agents` endpoint
3. **Frontend Level:** Refresh your cluster map at http://localhost:3000

## Troubleshooting

If you get permission errors:
```bash
chmod +x test_live_updates.sh
chmod +x demo_script.sh
```

If database connection fails, make sure:
- PostgreSQL is running
- Backend is running (go run main.go)
- Database credentials are correct in the script

## Expected Behavior

✅ **Correct:** LiveLocations table gets UPDATED (existing rows modified)
❌ **Wrong:** New rows added to LiveLocations table

The trigger ensures each agent has exactly ONE record in LiveLocations that gets updated with the latest tracking data from TrackingUpdates.

---

**🎉 Your trigger system is working perfectly when you see locations updating without new rows being created!**

-- Migration file to add trigger for keeping LiveLocations up to date

-- 1. First, make sure we have a point column in LiveLocations for easier coordinate access
ALTER TABLE LiveLocations ADD COLUMN IF NOT EXISTS point GEOMETRY(POINT, 4326);

-- 2. Update existing records to populate the point column
UPDATE LiveLocations 
SET point = ST_SetSRID(ST_MakePoint(
    ST_X(geom::geometry),
    ST_Y(geom::geometry)
), 4326)
WHERE point IS NULL;

-- 3. Create a function that will be called by the trigger
CREATE OR REPLACE FUNCTION update_live_location_from_tracking()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert or update the LiveLocations table with the newest tracking data
    INSERT INTO LiveLocations (
        agent_id, role, trip_id, geom, point, timestamp, 
        speed_m_s, heading_deg, accuracy_m, altitude_m, 
        battery_pct, source, updated_at
    )
    VALUES (
        NEW.agent_id, NEW.role, NEW.trip_id, 
        NEW.geom, 
        ST_SetSRID(ST_MakePoint(
            ST_X(NEW.geom::geometry),
            ST_Y(NEW.geom::geometry)
        ), 4326),
        NEW.timestamp, 
        COALESCE(NEW.speed_m_s, 0), 
        COALESCE(NEW.heading_deg, 0), 
        NEW.accuracy_m, NEW.altitude_m, 
        NEW.battery_pct, NEW.source, 
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (agent_id)
    DO UPDATE SET
        role = EXCLUDED.role,
        trip_id = EXCLUDED.trip_id,
        geom = EXCLUDED.geom,
        point = EXCLUDED.point,
        timestamp = EXCLUDED.timestamp,
        speed_m_s = EXCLUDED.speed_m_s,
        heading_deg = EXCLUDED.heading_deg,
        accuracy_m = EXCLUDED.accuracy_m,
        altitude_m = EXCLUDED.altitude_m,
        battery_pct = EXCLUDED.battery_pct,
        source = EXCLUDED.source,
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        -- Only update if the new record is more recent
        EXCLUDED.timestamp > LiveLocations.timestamp;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Create the trigger that will call our function
DROP TRIGGER IF EXISTS trigger_update_live_location ON TrackingUpdates;
CREATE TRIGGER trigger_update_live_location
AFTER INSERT ON TrackingUpdates
FOR EACH ROW
EXECUTE FUNCTION update_live_location_from_tracking();

-- 5. Add simple_id column to Users table for easier identification
ALTER TABLE Users ADD COLUMN IF NOT EXISTS simple_id VARCHAR(10);

-- 6. Update existing users with simple IDs
UPDATE Users
SET simple_id = 
    CASE 
        WHEN user_type = 'agent' THEN 'emp' || ROW_NUMBER() OVER (PARTITION BY user_type ORDER BY created_at)
        WHEN user_type = 'customer' THEN 'cust' || ROW_NUMBER() OVER (PARTITION BY user_type ORDER BY created_at)
        ELSE 'user' || ROW_NUMBER() OVER (ORDER BY created_at)
    END
WHERE simple_id IS NULL;

-- 7. Create index on simple_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_simple_id ON Users(simple_id); 
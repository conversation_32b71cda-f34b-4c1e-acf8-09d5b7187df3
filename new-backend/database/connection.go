package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"delivery-tracking-backend/config"

	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
)

type DB struct {
	*sql.DB
}

var instance *DB

// Connect establishes a connection to the PostgreSQL database
func Connect(cfg *config.Config) (*DB, error) {
	if instance != nil {
		return instance, nil
	}

	dbURL := cfg.GetDatabaseURL()
	
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(25)                 // Maximum number of open connections
	db.SetMaxIdleConns(5)                  // Maximum number of idle connections
	db.SetConnMaxLifetime(5 * time.Minute) // Maximum lifetime of a connection

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Verify PostGIS extension is available
	if err := verifyPostGIS(db); err != nil {
		return nil, fmt.Errorf("PostGIS verification failed: %w", err)
	}

	instance = &DB{db}
	
	logrus.Info("Successfully connected to PostgreSQL database with PostGIS support")
	return instance, nil
}

// GetInstance returns the singleton database instance
func GetInstance() *DB {
	return instance
}

// Close closes the database connection
func (db *DB) Close() error {
	if db.DB != nil {
		return db.DB.Close()
	}
	return nil
}

// verifyPostGIS checks if PostGIS extension is available
func verifyPostGIS(db *sql.DB) error {
	var version string
	query := "SELECT PostGIS_Version();"
	
	err := db.QueryRow(query).Scan(&version)
	if err != nil {
		return fmt.Errorf("PostGIS extension not found or not accessible: %w", err)
	}
	
	logrus.Infof("PostGIS version: %s", version)
	return nil
}

// Health checks the database connection health
func (db *DB) Health() error {
	if db.DB == nil {
		return fmt.Errorf("database connection is nil")
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}
	
	return nil
}

// Transaction executes a function within a database transaction
func (db *DB) Transaction(fn func(*sql.Tx) error) error {
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()
	
	err = fn(tx)
	return err
}

// GetStats returns database connection statistics
func (db *DB) GetStats() sql.DBStats {
	return db.Stats()
}
